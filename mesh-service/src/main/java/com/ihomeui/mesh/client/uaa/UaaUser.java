package com.ihomeui.mesh.client.uaa;


import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

/**
 * Created by XXM on 2016/12/30.
 */
public class UaaUser implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @NotNull
    @Size(min = 1, max = 50)
    private String login;

    @Size(max = 64)
    private String firstName;

    @Size(max = 64)
    private String lastName;

    @Size(max = 256)
    private String email;

    private Boolean emailVerified = false;

    @Size(max = 64)
    private String name;

    private Integer sex;

    private LocalDate birthday;

    @Size(max = 256)
    private String photo;

    @Size(max = 64)
    private String mobilePhone;

    private Boolean mobilePhoneVerified = false;

    @Size(max = 10)
    private String langKey;

    @NotNull
    private Boolean activated = false;

    @Size(max = 256)
    private String remarks;

    private String createdBy;

    private LocalDateTime createdDate = LocalDateTime.now();

    private String lastModifiedBy;

    private LocalDateTime lastModifiedDate = LocalDateTime.now();

    // Office IDs
    private Set<Long> offices = new HashSet<>();

    // Groups IDs
    private Set<Long> groups = new HashSet<>();

    //组名
    private Set<String> groupNames = new HashSet<>();

    // Authority names
    private Set<String> authorities = new HashSet<>();


    public Long getId() {
        return id;
    }
    public void setId(Long id) {
        this.id = id;
    }
    public UaaUser id(Long id) {
        this.id = id;
        return this;
    }

    public String getLogin() {
        return login;
    }
    public void setLogin(String login) {
        this.login = login;
    }
    public UaaUser login(String login) {
        this.login = login;
        return this;
    }

    public String getFirstName() {
        return firstName;
    }
    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }
    public UaaUser firstName(String firstName) {
        this.firstName = firstName;
        return this;
    }

    public String getLastName() {
        return lastName;
    }
    public void setLastName(String lastName) {
        this.lastName = lastName;
    }
    public UaaUser lastName(String lastName) {
        this.lastName = lastName;
        return this;
    }

    public String getEmail() {
        return email;
    }
    public void setEmail(String email) {
        this.email = email;
    }
    public UaaUser email(String email) {
        this.email = email;
        return this;
    }

    public Boolean getEmailVerified() {
        if(emailVerified == null) return false;
        return emailVerified;
    }
    public void setEmailVerified(Boolean emailVerified) {
        this.emailVerified = emailVerified;
    }
    public UaaUser emailVerified(Boolean emailVerified) {
        this.emailVerified = emailVerified;
        return this;
    }

    public String getName() {
        return name;
    }
    public void setName(String name) {
        this.name = name;
    }
    public UaaUser name(String name) {
        this.name = name;
        return this;
    }

    public Integer getSex() {
        return sex;
    }
    public void setSex(Integer sex) {
        this.sex = sex;
    }
    public UaaUser sex(Integer sex) {
        this.sex = sex;
        return this;
    }

    public LocalDate getBirthday() {
        return birthday;
    }
    public void setBirthday(LocalDate birthday) {
        this.birthday = birthday;
    }
    public UaaUser birthday(LocalDate birthday) {
        this.birthday = birthday;
        return this;
    }

    public String getPhoto() {
        return photo;
    }
    public void setPhoto(String photo) {
        this.photo = photo;
    }
    public UaaUser photo(String photo) {
        this.photo = photo;
        return this;
    }

    public String getMobilePhone() {
        return mobilePhone;
    }
    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }
    public UaaUser mobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
        return this;
    }

    public Boolean getMobilePhoneVerified() {
        if(mobilePhoneVerified == null) return false;
        return mobilePhoneVerified;
    }
    public void setMobilePhoneVerified(Boolean mobilePhoneVerified) {
        this.mobilePhoneVerified = mobilePhoneVerified;
    }
    public UaaUser mobilePhoneVerified(Boolean mobilePhoneVerified) {
        this.mobilePhoneVerified = mobilePhoneVerified;
        return this;
    }

    public String getLangKey() {
        return langKey;
    }
    public void setLangKey(String langKey) {
        this.langKey = langKey;
    }
    public UaaUser langKey(String langKey) {
        this.langKey = langKey;
        return this;
    }

    public Boolean getActivated() {
        if(activated == null) return false;
        return activated;
    }
    public void setActivated(Boolean activated) {
        this.activated = activated;
    }
    public UaaUser activated(Boolean activated) {
        this.activated = activated;
        return this;
    }

    public String getRemarks() {
        return remarks;
    }
    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }
    public UaaUser remarks(String remarks) {
        this.remarks = remarks;
        return this;
    }


    public String getCreatedBy() {
        return createdBy;
    }
    public UaaUser createdBy(String createdBy) {
        this.createdBy = createdBy;
        return this;
    }
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }
    public UaaUser createdDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
        return this;
    }
    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getLastModifiedBy() {
        return lastModifiedBy;
    }
    public UaaUser setLastModifiedBy(String lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
        return this;
    }
    public void lastModifiedBy(String lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    public LocalDateTime getLastModifiedDate() {
        return lastModifiedDate;
    }
    public UaaUser lastModifiedDate(LocalDateTime lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
        return this;
    }
    public void setLastModifiedDate(LocalDateTime lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    public Set<Long> getOffices() {
        return offices;
    }
    public void setOffices(Set<Long> offices) {
        this.offices = offices;
    }
    public UaaUser offices(Set<Long> offices) {
        this.offices = offices;
        return this;
    }

    public Set<Long> getGroups() {
        return groups;
    }
    public void setGroups(Set<Long> groups) {
        this.groups = groups;
    }
    public UaaUser groups(Set<Long> groups) {
        this.groups = groups;
        return this;
    }

    public Set<String> getAuthorities() {
        return authorities;
    }
    public void setAuthorities(Set<String> authorities) {
        this.authorities = authorities;
    }
    public UaaUser authorities(Set<String> authorities) {
        this.authorities = authorities;
        return this;
    }

    public Set<String> getGroupNames() {
        return groupNames;
    }
    public void setGroupNames(Set<String> groupNames) {
        this.groupNames = groupNames;
    }
    public UaaUser groupNames(Set<String> groupNames) {
        this.groupNames = groupNames;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        UaaUser uaaUser = (UaaUser) o;

        if (!login.equals(uaaUser.login)) {
            return false;
        }

        return true;
    }

    @Override
    public int hashCode() {
        return login.hashCode();
    }

    @Override
    public String toString() {
        return "UaaUser{" +
            "id=" + id +
            ", login='" + login + '\'' +
            ", firstName='" + firstName + '\'' +
            ", lastName='" + lastName + '\'' +
            ", email='" + email + '\'' +
            ", emailVerified=" + emailVerified +
            ", name='" + name + '\'' +
            ", sex=" + sex +
            ", birthday=" + birthday +
            ", photo='" + photo + '\'' +
            ", mobilePhone='" + mobilePhone + '\'' +
            ", mobilePhoneVerified=" + mobilePhoneVerified +
            ", langKey='" + langKey + '\'' +
            ", activated=" + activated +
            ", remarks='" + remarks + '\'' +
            ", createdBy='" + createdBy + '\'' +
            ", createdDate=" + createdDate +
            ", lastModifiedBy='" + lastModifiedBy + '\'' +
            ", lastModifiedDate=" + lastModifiedDate +
            ", offices=" + offices +
            ", groups=" + groups +
            ", groupNames=" + groupNames +
            ", authorities=" + authorities +
            '}';
    }
}
