package com.ihomeui.mesh.service;

import org.apache.http.client.HttpClient;

import java.util.Set;

public interface ProxyIpPool {

    long size();

    void add(String proxyIp, long expireEpochMilli);

    Set<String> getAll();

    String get();

    String get(Set<String> ipIgnores);

    void remove(String proxyIp);

    void signSuccess(String proxyIp);

    void signError(String proxyIp);

    void recallWaiting();

    void clearExpires();

    void fillPool();

    HttpClient getProxyHttpClient();

    HttpClient getProxyHttpClient(Set<String> ignoreProxies);
}
