<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.modules.sales.mapper.GoodsComboItemMapper">

    <select id="queryComboItems" resultType="com.jmt.modules.sales.vto.GoodsComboItemVO">
        select goods.*,item.quantity from zg_goods_combo_item item,zg_goods goods WHERE item.item_goods_id= goods.id
        and item.combo_goods_id=#{comboGoodsId}
    </select>

    <delete id="clearComboItems" >
        delete from zg_goods_combo_item WHERE combo_goods_id=#{comboGoodsId}
    </delete>

</mapper>