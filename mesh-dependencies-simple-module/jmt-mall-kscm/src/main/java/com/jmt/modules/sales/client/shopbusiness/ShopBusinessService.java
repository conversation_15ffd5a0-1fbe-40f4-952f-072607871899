package com.jmt.modules.sales.client.shopbusiness;

import com.ihomeui.mesh.client.AuthorizedFeignClient;
import com.jmt.modules.sales.vto.ThinkJSResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 客户服务；都是餐谋里面的用户
 */
@AuthorizedFeignClient(name = "jmt-commission-kscm",decode404 = true)
public interface ShopBusinessService {

    /**
     * 通过ID得到用户信息
     * @param id
     * @return
     */
    @RequestMapping(value = "/user/getById",method = RequestMethod.GET)
    public ThinkJSResult<UserProfile> getById(@RequestParam Long id);

    /**
     * 通过手机号码获得用户信息
     * @param phone
     * @return
     */
    @RequestMapping(value = "/user/getByPhone",method = RequestMethod.GET)
    public ThinkJSResult<UserProfile> getByPhone(@RequestParam String phone);

    /**
     * 通过ID得到用户联系地址
     * @param id
     * @return
     */
    @RequestMapping(value = "/user/getUserAddressById",method = RequestMethod.GET)
    public ThinkJSResult<UserAddress> getUserAddress(@RequestParam Long id);

}
