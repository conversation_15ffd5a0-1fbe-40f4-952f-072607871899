package com.jmt.modules.sales.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
   优选商品品类
 */
@Data
@TableName("zg_goods")
public class Goods extends EntityBase2 {
    /**
     * 单品调码/套餐货号
     */
    private String code;
    /**
     * 商品名称
     */
    private String name;
    /**
     * 商品缩略图
     */
    private String thumb;
    /**
     * 规格
     */
    private String sku;
    /**
     * 分类ID
     */
    private Integer catagory_id;
    /**
     * 供应商ID
     */
    private Integer supplier_id;
    /**
     * s商品分类：单品、套餐
     */
    private String goodsType="单品";
    /**
     * 成本价：暂时无用
     */
    private Double costPrice=0.; //成本价
    /**
     * 商品标价:原价
     */
    private Double retailPrice=0.;
    /**
     * 实际销售价格
     */
    private Double tradePrice=0.;
    /**
     * 备注
     */
    private String remark;
    /**
     * 库存量：目前没有跟踪库存量
     */
    private Integer stock=1000;

    /**
     * 需要几份：暂时没有用
     */
    private Integer usePoints=0;

    /**
     * 状态:0下架,1上架,默认1
     */
    private Integer status=1;


}
