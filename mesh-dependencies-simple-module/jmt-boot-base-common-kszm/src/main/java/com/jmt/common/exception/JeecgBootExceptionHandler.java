package com.jmt.common.exception;

import com.jmt.common.api.vo.Result;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.data.redis.connection.PoolException;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.web.servlet.NoHandlerFoundException;

import lombok.extern.slf4j.Slf4j;

/**
 * 异常处理器
 *
 * <AUTHOR>
 * @Date 2019
 */
@RestControllerAdvice
@Slf4j
public class JeecgBootExceptionHandler {

	/**
	 * 处理自定义异常
	 */
	@ExceptionHandler(JeecgBootException.class)
	public Result<?> handleRRException(JeecgBootException e){
		log.error(e.getMessage(), e);
		return Result.error(e.getMessage());
	}

	@ExceptionHandler(NoHandlerFoundException.class)
	public Result<?> handlerNoFoundException(Exception e) {
		log.error(e.getMessage(), e);
		return Result.error(404, "路径不存在，请检查路径是否正确");
	}


	@ExceptionHandler(DuplicateKeyException.class)
	public Result<?> handleDuplicateKeyException(DuplicateKeyException e){
		log.error(e.getMessage(), e);
		String message="数据库已存在该数据";
		if(e.getLocalizedMessage().toString().contains("p_login_name")){
			message="登陆账号已存在";
		}
		return Result.error(message);
	}


	@ExceptionHandler(Exception.class)
	public Result<?> handleException(Exception e){
		log.error(e.getMessage(), e);
		return Result.error("服务繁忙，请稍后再试。。");
	}

	/**
	 * <AUTHOR>
	 * @param e
	 * @return
	 */
	@ExceptionHandler(HttpRequestMethodNotSupportedException.class)
	public Result<?> HttpRequestMethodNotSupportedException(HttpRequestMethodNotSupportedException e){
		StringBuffer sb = new StringBuffer();
		sb.append("不支持");
		sb.append(e.getMethod());
		sb.append("请求方法，");
		sb.append("支持以下");
		String [] methods = e.getSupportedMethods();
		if(methods!=null){
			for(String str:methods){
				sb.append(str);
				sb.append("、");
			}
		}
		log.error(sb.toString(), e);
		//return Result.error("没有权限，请联系管理员授权");
		return Result.error(405,sb.toString());
	}

	 /**
	  * spring默认上传大小100MB 超出大小捕获异常MaxUploadSizeExceededException
	  */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    public Result<?> handleMaxUploadSizeExceededException(MaxUploadSizeExceededException e) {
    	log.error(e.getMessage(), e);
        return Result.error("文件大小超出100MB限制, 请压缩或降低文件质量! ");
    }

    @ExceptionHandler(DataIntegrityViolationException.class)
    public Result<?> handleDataIntegrityViolationException(DataIntegrityViolationException e) {
    	log.error(e.getMessage(), e);
        return Result.error("字段太长,超出数据库字段的长度");
    }

    @ExceptionHandler(PoolException.class)
    public Result<?> handlePoolException(PoolException e) {
    	log.error(e.getMessage(), e);
        return Result.error("Redis 连接异常!");
    }


	/**
	 * 描述: 入参异常
	 * @date: 2020/4/1 0001
	 * @author: hanshangrong
	 * @param e
	 * @return Result<?>
	 */
	@ExceptionHandler(value = HttpMessageNotReadableException.class)
	public Result<?> parameterException(Exception e) {
		// 将消息返回给前端
		log.error("入参异常",e);
		return Result.error("参数异常");
	}

	/**
	 * 描述: 参数校验异常
	 * @date: 2020/4/1 0001
	 * @author: hanshangrong
	 * @param e
	 * @return Result<?>
	 */
	@ExceptionHandler(value = MethodArgumentNotValidException.class)
	public Result<?> exceptionHandler(Exception e) {
		String failMsg = null;
		if (e instanceof MethodArgumentNotValidException) {
			// 拿到参数校验具体异常信息提示
			failMsg = ((MethodArgumentNotValidException) e).getBindingResult().getFieldError().getDefaultMessage();
		}
		// 将消息返回给前端
		return Result.error(failMsg);
	}
	@ExceptionHandler(value = TokenException.class)
	public Result<?> tokenException(Exception e){
		Result<?> result=new Result<>();
		result.setCode(800);
		result.setMessage(e.getMessage());
		return result;
	}


	/**
	 * 描述:	自定义断言异常
	 * @method  handleRRException
	 * @date: 2020/5/22 0022
	 * @author: hanshangrong
	 * @param e
	 * @return org.jeecg.common.api.vo.Result<?>
	 */
	@ExceptionHandler(RRException.class)
	public Result<?> handleRRException(RRException e){
		return Result.error(e.getCode(),e.getMsg());
	}


	@ExceptionHandler(UserNotExistException.class)
	public Result<?> UserNotExistException(UserNotExistException e){
		Result<Object> result=new Result<>();
		result.setCode(401);
		result.setMessage("用户信息不存在");
		return result;
	}
}
