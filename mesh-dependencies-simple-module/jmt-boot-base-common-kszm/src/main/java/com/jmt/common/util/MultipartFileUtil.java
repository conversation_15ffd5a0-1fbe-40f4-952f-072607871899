package com.jmt.common.util;


import cn.hutool.core.util.IdUtil;
import org.springframework.web.multipart.MultipartFile;
import ws.schild.jave.MultimediaInfo;
import ws.schild.jave.MultimediaObject;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.channels.FileChannel;

/**
 * MultipartFile和File互转工具类
 */
public class MultipartFileUtil {

    /**
     * 输入流转MultipartFile
     *
     * @param fileName
     * @param inputStream
     * @return
     */

    /**
     * 读取网络文件
     *
     * @param url      文件地址
     * @param fileName 文件名称（需带文件名后缀）
     * @return
     */

    /**
     * File 转MultipartFile
     *
     * @param file
     * @return
     */


    /**
     * MultipartFileUtil 转File
     *
     * @param multipartFile
     * @return
     */
    public static File getFile(MultipartFile multipartFile) {
        // 获取文件名
        String fileName = multipartFile.getOriginalFilename();
        // 获取文件后缀
        String prefix = "." + getExtensionName(fileName);
        File file = null;
        try {
            // 用uuid作为文件名，防止生成的临时文件重复
            file = File.createTempFile(IdUtil.simpleUUID(), prefix);
            // MultipartFile to File
            multipartFile.transferTo(file);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return file;
    }

    /**
     * 获取文件扩展名，不带 .
     */
    public static String getExtensionName(String filename) {
        if ((filename != null) && (filename.length() > 0)) {
            int dot = filename.lastIndexOf('.');
            if ((dot > -1) && (dot < (filename.length() - 1))) {
                return filename.substring(dot + 1);
            }
        }
        return filename;
    }


    public static void main(String[] args) {
        long ls = 0L;
        String[] length = new String[2];
        String se = "0";
        try {
            MultimediaObject instance = new MultimediaObject(new File("D://5566.mp4"));
            MultimediaInfo result = instance.getInfo();

            ls = result.getDuration() / 1000;
            length[0] = String.valueOf(ls);
            Integer hour = (int) (ls / 3600);
            Integer minute = (int) (ls % 3600) / 60;
            Integer second = (int) (ls - hour * 3600 - minute * 60);
            String hr = hour.toString();
            String mi = minute.toString();
            se = second.toString();
            System.out.println(se);
        } catch (Exception e) {


        }
    }

    /**
     * 视频时长
     *
     * @param
     * @return String[] 0=秒时长，1=展示时长（格式如 01:00:00）
     */
    public static String parseDuration(MultipartFile multipartFile) {
        long ls = 0L;
        String[] length = new String[2];
        String se = "0";
        try {
            MultimediaObject instance = new MultimediaObject(getFile(multipartFile));
            MultimediaInfo result = instance.getInfo();
            ls = result.getDuration() / 1000;
            length[0] = String.valueOf(ls);
            Integer hour = (int) (ls / 3600);
            Integer minute = (int) (ls % 3600) / 60;
            Integer second = (int) (ls - hour * 3600 - minute * 60);
            String hr = hour.toString();
            String mi = minute.toString();
            se = second.toString();

        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("这是:===" + se);//{"20","00:20"}
        return se;
    }


    /**
     * 2、获取文件大小
     *
     * @param source
     * @return //***获取视频大小的时候，由于用到了流，使用完之后一定要及时的关闭流，避免无法删除视频文件***
     */
    public static BigDecimal readFileSize(File source) {
        //cn.hutool.core.io.unit.DataSize.ofMegabytes()
        FileChannel fc = null;
        //String size = "";
        BigDecimal size = null;
        try {
            @SuppressWarnings("resource")
            FileInputStream fis = new FileInputStream(source);
            fc = fis.getChannel();
            BigDecimal fileSize = new BigDecimal(fc.size());
            //size = fileSize.divide(new BigDecimal(1048576), 2, RoundingMode.HALF_UP) + "MB";
            size = fileSize.divide(new BigDecimal(1024 * 1024), 2, RoundingMode.HALF_UP);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (null != fc) {
                try {
                    fc.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return size;
    }


}