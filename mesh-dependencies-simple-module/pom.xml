<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>mesh-parent</artifactId>
        <groupId>com.ihomeui.mesh</groupId>
        <version>3.5.0</version>
        <relativePath/>
    </parent>

    <modelVersion>4.0.0</modelVersion>

    <artifactId>mesh-dependencies-simple-module</artifactId>
    <packaging>pom</packaging>

    <dependencies>

        <dependency>
            <groupId>com.ihomeui.mesh</groupId>
            <artifactId>mesh-service</artifactId>
            <version>${mesh-framework.version}</version>
        </dependency>
        <dependency>
            <groupId>com.ihomeui.mesh</groupId>
            <artifactId>mesh-jpa</artifactId>
            <version>${mesh-framework.version}</version>
        </dependency>

    </dependencies>

    <modules>
        <module>jmt-boot-base-common-kszm</module>
        <module>jmt-boot-module-system-kszm</module>
        <module>jmt-boot-base-common-kscm</module>
        <module>jmt-boot-module-system-kscm</module>
        <module>jmt-mall-kscm</module>
    </modules>

</project>
