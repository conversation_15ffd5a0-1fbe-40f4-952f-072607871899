<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.modules.revenueshare.mapper.OrderRevenue2Mapper">

    <select id="listCmsOrderRevenue" resultType="com.jmt.modules.revenueshare.entity.OrderRevenue">
        SELECT operator_code,operator_name,revenue_item,SUM(order_amount) AS order_amount, SUM(investor_revenue) AS investor_revenue,
        SUM(restaurant_revenue) AS restaurant_revenue, SUM(operator_revenue) AS operator_revenue, SUM(promoter_revenue) AS promoter_revenue
        FROM zw_order_revenue
        <where>
            <if test="cms.partner!=null and cms.partner!=''">
                and (operator_name like concat('%',#{cms.partner},'%')
                or operator_code = #{cms.partner})
            </if>
            <if test="cms.startDate != null and cms.startDate != ''">
                and order_date >= #{cms.startDate}
            </if>
            <if test="cms.sendDate != null and cms.sendDate != ''">
                and order_date &lt;= {cms.sendDate}
            </if>
            <if test="cms.incomeItem != null and cms.incomeItem !=''">
                and revenue_item = #{cms.incomeItem}
            </if>
        </where>
        group by  operator_code,operator_name,revenue_item
        ORDER BY operator_code DESC
    </select>

    <select id="listRevenueDetails" resultType="com.jmt.modules.revenueshare.entity.OrderRevenue">
        select *   from zw_order_revenue
        <where>
            <if test="cms.partnerNumber != null">
                and operator_code = #{cms.partnerNumber}
            </if>
            <if test="cms.incomeItem != null and cms.incomeItem !=''">
                and revenue_item = #{cms.incomeItem}
            </if>
            <if test="cms.startDate != null and cms.startDate != ''">
                and order_date >= #{cms.startDate}
            </if>
            <if test="cms.sendDate != null and cms.sendDate != ''">
                and order_date &lt;= #{cms.sendDate}
            </if>
            <if test="cms.mname != null and cms.mname !=''">
                and restaurant_name like concat('%',#{cms.mname},'%')
            </if>
        </where>
        order by order_date desc
    </select>
    <select id="listOrderRevenueApp" resultType="com.jmt.modules.revenueshare.entity.OrderRevenue">
        SELECT * from zw_order_revenue
        <where>
            operator_code = #{cms.pid}
            <if test="cms.date != null and cms.date != ''">
                AND LEFT(order_date,7) = #{cms.date}
            </if>
            <if test="cms.projectName!=null and cms.projectName!=''">
                and revenue_item = #{cms.projectName}
            </if>
        </where>
        order by order_date desc
    </select>

    <select id="listOrderRevenueApp2" resultType="com.jmt.modules.revenueshare.entity.OrderRevenue">
        SELECT * from zw_order_revenue
        <where>
            restaurant_code in   (select m_code from view_shop_partner where p_code=#{cms.pid})
            <if test="cms.date != null and cms.date != ''">
                AND LEFT(order_date,7)= #{cms.date}
            </if>
            <if test="cms.projectName!=null and cms.projectName!=''">
                and revenue_item = #{cms.projectName}
            </if>
        </where>
        order by order_date desc
    </select>


    <select id="queryShopRanking" resultType="com.jmt.modules.revenueshare.vto.RankingVo">
        SELECT restaurant_code, restaurant_name,
        ( SELECT count( * ) FROM cms_eq b WHERE b.m_code = a.restaurant_code ) AS eq,
        SUM( order_amount ) / ( SELECT count( * ) FROM cms_eq b WHERE b.m_code = a.restaurant_code ) AS money
        FROM zw_order_revenue a
        WHERE restaurant_code IS NOT NULL
        AND ( SELECT count( * ) FROM cms_eq b WHERE b.m_code = a.restaurant_code ) > 0
        AND restaurant_code IN (
        SELECT m_code FROM cms_shop_info WHERE p_sub_code = #{partnerCode}
        )
        GROUP BY restaurant_code
        ORDER BY money DESC
    </select>


    <select id="statIncomeOfPartener1" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(investor_revenue),0) FROM zw_order_revenue
        WHERE investor_code = #{pCode}
        <if test="dateStart != null and dateStart != ''">
            and order_date >=#{dateStart}
        </if>
        <if test="dateEnd != null and dateEnd != ''">
            and order_date &lt;= #{dateEnd}
        </if>
    </select>
    <select id="statIncomeOfPartener2" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(operator_revenue),0) FROM zw_order_revenue
        WHERE  operator_code  = #{pCode}
        <if test="dateStart != null and dateStart != ''">
            and order_date >= #{dateStart}
        </if>
        <if test="dateEnd != null and dateEnd != ''">
            and order_date &lt;= #{dateEnd}
        </if>
    </select>

    <select id="statShopIncomeOfPartener2" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(order_amount),0) FROM zw_order_revenue
        WHERE operator_code  = #{pCode}
        <if test="dateStart != null and dateStart != ''">
            and order_date >= #{dateStart}
        </if>
        <if test="dateEnd != null and dateEnd != ''">
            and order_date &lt;= #{dateEnd}
        </if>
    </select>
    <select id="statShopIncomeOfPartener1" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(order_amount),0) FROM zw_order_revenue
        WHERE investor_code = #{pCode}
        <if test="dateStart != null and dateStart != ''">
            and order_date >= #{dateStart}
        </if>
        <if test="dateEnd != null and dateEnd != ''">
        and order_date &lt;= #{dateEnd}
        </if>
    </select>
    <select id="statHasIncomeShopCountOfPartener1" resultType="java.lang.Integer">
        SELECT IFNULL(COUNT( DISTINCT restaurant_code),0) FROM zw_order_revenue
        WHERE investor_code = #{pCode}
    </select>

    <select id="statIncomeGroupbyDateOfPartener2" resultType="com.jmt.modules.revenueshare.vto.RevenueListVo">
       SELECT SUM(operator_revenue) as partnerIncome,order_date as date from zw_order_revenue
        WHERE operator_code  = #{pCode}
        <if test="dateStart != null and dateStart != ''">
            and order_date >= #{dateStart}
        </if>
        GROUP BY date
    </select>
    <select id="statIncomeGroupbyDateOfPartener1" resultType="com.jmt.modules.revenueshare.vto.RevenueListVo">
        SELECT SUM(operator_revenue) as partnerIncome,order_date as date from zw_order_revenue
        WHERE operator_code  = #{pCode}
        <if test="dateStart != null and dateStart != ''">
            and order_date >= #{dateStart}
        </if>
        GROUP BY date
    </select>
    <select id="statIncomeGroupbyEQ" resultType="com.jmt.modules.revenueshare.vto.RevenueStatItemVO">
        SELECT SUM(order_amount) as revenueSum,revenue_item as revenue_item  from zw_order_revenue
        WHERE eq_code  = #{eqCode} GROUP BY revenue_item
    </select>

</mapper>
