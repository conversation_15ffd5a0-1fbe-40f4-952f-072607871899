<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.modules.devicemanage.mapper.ShopInfo2Mapper">


    <select id="selectByMCode" resultType="com.jmt.modules.devicemanage.entity.ShopInfo">
        SELECT * FROM zb_shop_info where m_code = #{mCode}

    </select>
    <select id="queryByPhone" resultType="com.jmt.modules.devicemanage.entity.ShopInfo">
        SELECT * FROM zb_shop_info where m_phone = #{queryByPhone}

    </select>

    <select id="queryByUserId" resultType="com.jmt.modules.devicemanage.entity.ShopInfo">
        SELECT * FROM zb_shop_info where user_id = #{userId}

    </select>

    <select id="queryByMcode" resultType="com.jmt.modules.devicemanage.vto.ShopInfoVo">
        SELECT i.*,
        s.`m_volume`,
        s.m_min_voume,
        s.m_DurationTime,
        concat(m_provinces, m_city, m_countiy, m_address) as address
        FROM zb_shop_info i
        LEFT JOIN cms_eq_set s ON i.`m_code` = s.`m_code`
        where i.m_code = #{mCode}

    </select>

    <select id="queryByOperator" resultType="com.jmt.modules.devicemanage.vto.ShopInfoListVo">
        SELECT s.*,CONCAT_WS('/',m_provinces,m_city,m_countiy) AS address FROM zb_shop_info s
        <where>
            operator_code = #{cond.pCode}
            <if test="cond.subCode != null and cond.subCode>0">
                and s.promoter_No = #{cond.subCode}
            </if>
            <if test="cond.areaCode != null and cond.areaCode>0">
                and (s.m_provinces_num = #{cond.areaCode} or s.m_city_num = #{cond.areaCode} or s.m_countiy_num = #{cond.areaCode})
            </if>
        </where>
        order by s.create_time desc
    </select>
    <select id="queryByPartner" resultType="com.jmt.modules.devicemanage.vto.ShopInfoListVo">
        SELECT s.*,CONCAT_WS('/',m_provinces,m_city,m_countiy) AS address FROM zb_shop_info s
        <where>
            <if test="userType==0">
                s.promoter_No = #{cond.pCode}
            </if>
            <if test="userType==1">
                s.investor_code = #{cond.pCode}
            </if>
            <if test="userType==2">
                s.operator_code = #{cond.pCode}
            </if>
            <if test="cond.status != null and cond.status>-1">
                and s.m_status = #{cond.status}
            </if>
            <if test="cond.subCode != null and cond.subCode>0">
                and s.promoter_No = #{cond.subCode}
            </if>
            <if test="cond.areaCode != null and cond.areaCode>0">
                and (s.m_provinces_num = #{cond.areaCode} or s.m_city_num = #{cond.areaCode} or s.m_countiy_num = #{cond.areaCode})
            </if>
        </where>
        order by s.create_time desc
    </select>

    <select id="queryAllLike" resultType="com.jmt.modules.devicemanage.vto.ShopInfoListVo">
        SELECT s.*,CONCAT_WS('/',m_provinces,m_city,m_countiy) AS address,
        p .`p_name` as operatorName,i.p_name as investorName
        FROM zb_shop_info s
            LEFT JOIN cms_partner_info p ON s.`operator_code` = p.`p_id`
            LEFT JOIN cms_partner_info i ON s.`investor_code` = i.`p_id`
        <where>
            m_status = 2
            <if test="cms.plike!=null and cms.plike!= ''">
                and (
                p.p_name like concat('%',#{cms.plike},'%') or p.p_id = #{cms.plike}
                )
            </if>
            <if test="cms.startTime != null and cms.startTime != ''">
                and s.createTime >= concat(#{cms.startTime},' 00.00.00')
            </if>
            <if test="cms.sendTime != null and cms.sendTime != ''">
                and s.createTime &lt;= concat(#{cms.sendTime},' 23.59.59')
            </if>
            <if test="cms.provinces != null and cms.provinces!= '' ">
                and s.m_provinces = #{cms.provinces}
            </if>
            <if test="cms.city != null and cms.city!= '' ">
                and s.m_city = #{cms.city}
            </if>
            <if test="cms.countiy != null and cms.countiy!= '' ">
                and s.m_countiy = #{cms.countiy}
            </if>
            <if test="cms.mlike != null and cms.mlike!= '' ">
                and (
                s.m_name like concat('%',#{cms.mlike},'%') or s.m_code = #{cms.mlike}
                )
            </if>
        </where>
        order by s.create_time desc
    </select>
    <select id="queryByRecommend" resultType="com.jmt.modules.devicemanage.vto.ShopInfoListAppVo">
        SELECT * FROM zb_shop_info WHERE promoter_No =  #{pId}
        ORDER BY create_time ASC
    </select>

    <select id="queryByPartener1" resultType="com.jmt.modules.devicemanage.vto.ShopInfoListAppVo">
        SELECT *,CONCAT_WS('/',m_provinces,m_city,m_countiy) AS address
        FROM zb_shop_info
        <where>
            m_status = 1 and investor_code =#{cms.pId}
            <if test="cms.area !=null and cms.area !=''">
                and m_countiy_num = #{cms.area}
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="queryByPartener2" resultType="com.jmt.modules.devicemanage.vto.ShopInfoListAppVo">
        SELECT *,CONCAT_WS('/',m_provinces,m_city,m_countiy) AS address
        FROM zb_shop_info
        <where>
            m_status = 1 and operator_code=#{cms.pId}
            <if test="cms.area !=null and cms.area !=''">
                and m_countiy_num = #{cms.area}
            </if>
        </where>
        order by create_time desc
    </select>
    <select id="getToAuditShopCount" resultType="java.lang.Integer">
        SELECT  count(*) from zb_shop_info WHERE operator_code=#{pId} and m_status=0;
    </select>

    <select id="getShopCountOfPartner1" resultType="java.lang.Integer">
        SELECT  count(*)  from zb_shop_info WHERE investor_code=#{pId}
        <if test="status !=null">
            and m_status=#{status};
        </if>
    </select>
    <select id="getShopCountOfPartner2" resultType="java.lang.Integer">
        SELECT  count(*)  from zb_shop_info WHERE operator_code=#{pId}
        <if test="status !=null">
            and m_status=#{status};
        </if>
    </select>
    <select id="listShopAudit" resultType="com.jmt.modules.devicemanage.vto.ShopAuditListVO">
        SELECT t1.m_code,t1.m_name,t1.m_contacts,t1.m_status,t1.update_time,t2.file_url FROM zb_shop_info  t1
            LEFT JOIN cms_attach_file t2 ON t1.m_code = t2.user_id AND t2.img_type = 3
            WHERE t1.operator_code =#{pId}
        <if test="status !=null and status !=''">
            and m_status=#{status}
        </if>
        order by t1.update_time desc
    </select>

    <select id="getToDeployEqCount" resultType="java.lang.Integer">
        SELECT  IFNULL(sum(machine_a+machine_b+machine_c),0)  from zb_shop_info WHERE operator_code=#{pId} and m_status  in (0, 1)
    </select>

</mapper>
