<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.modules.devicemanage.mapper.WorkOrderMapper">

    <!--查询单个-->
    <select id="queryByOrderNo" resultType="com.jmt.modules.devicemanage.entity.WorkOrder">
        select *  from zb_workorder
        where order_no = #{orderNo}
    </select>

    <delete id="deleteByMCode">
        delete from zb_workorder where shop_code = #{mcode}
    </delete>

    <select id="queryAllByPartnerIdApp" resultType="com.jmt.modules.devicemanage.vto.WorkOrderListAppVo">
        SELECT wo.*, shop.m_name as shopName,sub.p_sub_name as subName from zb_workorder wo
        left join cms_shop_info shop on wo.shop_code= shop.m_code
        left join cms_partner_sub sub on wo.deployer_code= sub.p_code
        WHERE wo.operator_code= #{cms.pId}
        <if test="cms.mOrderStatus!=null and cms.mOrderStatus!=''">
            and  wo.status = #{cms.mOrderStatus}
        </if>
         ORDER BY wo.create_time desc
    </select>

    <select id="queryAllByPartnerSubIdApp" resultType="com.jmt.modules.devicemanage.vto.WorkOrderListAppVo">
        SELECT wo.*, shop.m_name as shopName,sub.p_sub_name as subName from zb_workorder wo
        left join cms_shop_info shop on wo.shop_code= shop.m_code
        left join cms_partner_sub sub on wo.deployer_code= sub.p_code
        WHERE wo.deployer_code= #{cms.pId}
        <if test="cms.mOrderStatus!=null and cms.mOrderStatus!=''">
            and  wo.status = #{cms.mOrderStatus}
        </if>
        ORDER BY wo.create_time desc
    </select>

    <select id="queryByMcode" resultType="com.jmt.modules.devicemanage.vto.WorkOrderInfoVo">
        SELECT w.*,s.p_sub_name FROM zb_workorder w,cms_partner_sub s WHERE w.deployer_code=s.p_sub_code AND w.shop_code= #{mCode} limit 1
    </select>

    <select id="queryByOrderId" resultType="com.jmt.modules.devicemanage.vto.WorkOrderInfoAppVo">
        SELECT wo.*,shop.m_name as shopName,shop.m_contacts as shopContacts ,shop.m_phone as shopPhone,
        shop.m_address as shopAddress,sub.p_sub_name as deployerName
        FROM zb_workorder wo
        LEFT JOIN cms_shop_info shop ON shop.m_code = wo.shop_code
        left join cms_partner_sub sub on wo.deployer_code = sub.p_sub_code
        WHERE wo.order_no = #{orderId}
    </select>

    <select id="getOrderCountBySubCode" resultType="java.lang.Integer">
         SELECT IFNULL(count(*),0) FROM zb_workorder WHERE  `status` =#{orderStatus} and deployer_code=#{subCode}
    </select>
    <select id="getOrderCountByPid" resultType="java.lang.Integer">
        SELECT IFNULL(count(*),0) FROM zb_workorder WHERE  `status` =#{orderStatus}  and operator_code =#{pId}
    </select>
</mapper>
