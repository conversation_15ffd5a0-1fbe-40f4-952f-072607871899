<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.modules.commission.mapper.CmsRewardDistributionMapper">

    <resultMap id="BaseResultMap" type="com.jmt.modules.commission.entity.CmsRewardDistribution">
        <!--@Table cms_reward_distribution-->
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="recommendedPartner" column="recommended_partner" jdbcType="INTEGER"/>
        <result property="beRecommendedPartner" column="be_recommended_partner" jdbcType="INTEGER"/>
        <result property="amountIssued" column="amount_issued" jdbcType="INTEGER"/>
        <result property="amountToBeReleased" column="amount_to_be_released" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="BaseResultMap">
        select
          id, recommended_partner, be_recommended_partner, amount_issued, amount_to_be_released, create_time
        from commission.cms_reward_distribution
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="BaseResultMap">
        select
          id, recommended_partner, be_recommended_partner, amount_issued, amount_to_be_released, create_time
        from commission.cms_reward_distribution
        limit #{offset}, #{limit}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="BaseResultMap">
        select
          id, recommended_partner, be_recommended_partner, amount_issued, amount_to_be_released, create_time
        from commission.cms_reward_distribution
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="recommendedPartner != null">
                and recommended_partner = #{recommendedPartner}
            </if>
            <if test="beRecommendedPartner != null">
                and be_recommended_partner = #{beRecommendedPartner}
            </if>
            <if test="amountIssued != null">
                and amount_issued = #{amountIssued}
            </if>
            <if test="amountToBeReleased != null">
                and amount_to_be_released = #{amountToBeReleased}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into commission.cms_reward_distribution(recommended_partner, be_recommended_partner, amount_issued, amount_to_be_released, create_time)
        values (#{recommendedPartner}, #{beRecommendedPartner}, #{amountIssued}, #{amountToBeReleased}, #{createTime})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update commission.cms_reward_distribution
        <set>
            <if test="recommendedPartner != null">
                recommended_partner = #{recommendedPartner},
            </if>
            <if test="beRecommendedPartner != null">
                be_recommended_partner = #{beRecommendedPartner},
            </if>
            <if test="amountIssued != null">
                amount_issued = #{amountIssued},
            </if>
            <if test="amountToBeReleased != null">
                amount_to_be_released = #{amountToBeReleased},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from commission.cms_reward_distribution where id = #{id}
    </delete>

</mapper>