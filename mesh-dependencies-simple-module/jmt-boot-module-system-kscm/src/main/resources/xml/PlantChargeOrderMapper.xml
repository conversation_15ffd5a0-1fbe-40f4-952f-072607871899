<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.modules.commission.mapper.CmsShopEqMapper">

    <select id="statEqChargeOrder" resultType="java.util.Map">
        select eq_code,count(*) as order_count from nideshop_charge_order
            group by eq_code;
    </select>

</mapper>