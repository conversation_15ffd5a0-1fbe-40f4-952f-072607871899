<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.modules.commission.mapper.CmsShopEqMapper">

    <resultMap type="com.jmt.modules.commission.entity.CmsShopEq" id="CmsShopEqMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="mCode" column="m_code" jdbcType="VARCHAR"/>
        <result property="mEqNum" column="m_eq_num" jdbcType="INTEGER"/>
        <result property="mEqType" column="m_eq_type" jdbcType="INTEGER"/>
        <result property="mWorkorder" column="m_workOrder" jdbcType="VARCHAR"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="CmsShopEqMap">
        select
          id, m_code, m_eq_num, m_eq_type, m_workOrder
        from cms_shop_eq
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="CmsShopEqMap">
        select
          id, m_code, m_eq_num, m_eq_type, m_workOrder
        from cms_shop_eq
        limit #{offset}, #{limit}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="CmsShopEqMap">
        select
          id, m_code, m_eq_num, m_eq_type, m_workOrder
        from cms_shop_eq
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="mCode != null and mCode != ''">
                and m_code = #{mCode}
            </if>
            <if test="mEqNum != null">
                and m_eq_num = #{mEqNum}
            </if>
            <if test="mEqType != null">
                and m_eq_type = #{mEqType}
            </if>
            <if test="mWorkorder != null and mWorkorder != ''">
                and m_workOrder = #{mWorkorder}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into cms_shop_eq(m_code, m_eq_num, m_eq_type, m_workOrder)
        values (#{mCode}, #{mEqNum}, #{mEqType}, #{mWorkorder})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update cms_shop_eq
        <set>
            <if test="mCode != null and mCode != ''">
                m_code = #{mCode},
            </if>
            <if test="mEqNum != null">
                m_eq_num = #{mEqNum},
            </if>
            <if test="mEqType != null">
                m_eq_type = #{mEqType},
            </if>
            <if test="mWorkorder != null and mWorkorder != ''">
                m_workOrder = #{mWorkorder},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from cms_shop_eq where id = #{id}
    </delete>
    <delete id="deleteByMCode">
        delete from cms_shop_eq where m_code = #{mcode}
    </delete>
    <select id="queryAllByOrderId" resultType="com.jmt.modules.commission.model.vo.CmsShopEqListAppVo">
        select
          m_eq_num, m_eq_type
        from cms_shop_eq
        where m_workOrder = #{orderId} ORDER BY m_eq_type ASC
    </select>

    <select id="queryAllByMcode" resultType="com.jmt.modules.commission.model.vo.CmsShopEqListAppVo">
        SELECT SUM(m_eq_num) AS mEqNum , m_eq_type FROM cms_shop_eq
            WHERE m_code = #{mCode}  AND m_workOrder IN (
                SELECT m_workOrder FROM cms_workorder WHERE m_code = #{mCode} AND m_order_status = 2 AND m_workOrder !=#{orderId}
            ) GROUP BY m_eq_type
    </select>

    <select id="queryEqNUm" resultType="Integer">
        select count(*) from cms_eq where m_code = #{mCode} and m_eq_ststus = #{mEqStstus} and update_time &lt;= #{updateTime}
    </select>

    <select id="queryByPId" resultType="Integer">
        SELECT
	        COUNT(*)
        FROM
	        cms_eq
        WHERE update_time &lt;= #{endTime} AND update_time &gt;= #{entTime} AND(
                m_code IN (
	            SELECT m_code FROM cms_shop_info WHERE p_sub_code IN (
	                SELECT p_sub_code FROM cms_partner_sub WHERE p_code = #{pid}
	            )
	        )
	    )
	    OR (
	        m_eq_ststus = #{mEqStatus} AND m_code IN (
	            SELECT m_code FROM cms_shop_info WHERE p_sub_code = #{pid}
	        )
	    )
    </select>
    
    <select id="queryByPIdSum" resultType="Integer">
        SELECT sum(m_eq_num) FROM cms_shop_eq WHERE m_workOrder = #{mWorkOrder}
    </select>

    <select id="getByPro" resultType="com.jmt.modules.commission.model.vo.EqAreaVO">
        select shop.m_provinces,sum(eq.m_eq_num)
            from cms_shop_info shop,cms_shop_eq eq
                where eq.m_code= shop.m_code
                    group by shop.m_provinces
    </select>

    <select id="getEqSum" resultType="java.lang.Integer">
        select sum(m_eq_num) from cms_shop_eq shop,cms_workorder worder WHERE shop.m_workOrder=worder.m_workOrder
    </select>

    <select id="getInstalledEqSum" resultType="java.lang.Integer">
        select sum(m_eq_num) from cms_shop_eq shop,cms_workorder worder WHERE shop.m_workOrder=worder.m_workOrder and worder.m_order_status!=2
    </select>
</mapper>