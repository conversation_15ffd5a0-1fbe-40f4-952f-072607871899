<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.modules.commission.mapper.NideshopChargeOrderMapper">

    <resultMap type="com.jmt.modules.commission.entity.NideshopChargeOrder" id="NideshopChargeOrderMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="mobile" column="mobile" jdbcType="VARCHAR"/>
        <result property="eqCode" column="eq_code" jdbcType="VARCHAR"/>
        <result property="chargeOrder" column="charge_order" jdbcType="VARCHAR"/>
        <result property="orderStartTime" column="order_start_time" jdbcType="TIMESTAMP"/>
        <result property="orderEndTime" column="order_end_time" jdbcType="TIMESTAMP"/>
        <result property="orderStatus" column="order_status" jdbcType="INTEGER"/>
        <result property="orderChargeTime" column="order_charge_time" jdbcType="VARCHAR"/>
        <result property="orderMerchant" column="order_merchant" jdbcType="VARCHAR"/>
        <result property="orderUseIntegral" column="order_use_integral" jdbcType="VARCHAR"/>
        <result property="orderUsePrice" column="order_use_price" jdbcType="VARCHAR"/>
        <result property="orderReduction" column="order_reduction" jdbcType="VARCHAR"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="NideshopChargeOrderMap">
        select
          id, mobile, eq_code, charge_order, order_start_time, order_end_time, order_status, order_charge_time, order_merchant, order_use_integral, order_use_price, order_reduction
        from nideshop_charge_order
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="NideshopChargeOrderMap">
        select
          id, mobile, eq_code, charge_order, order_start_time, order_end_time, order_status, order_charge_time, order_merchant, order_use_integral, order_use_price, order_reduction
        from nideshop_charge_order
        limit #{offset}, #{limit}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="NideshopChargeOrderMap">
        select
          id, mobile, eq_code, charge_order, order_start_time, order_end_time, order_status, order_charge_time, order_merchant, order_use_integral, order_use_price, order_reduction
        from nideshop_charge_order
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="mobile != null and mobile != ''">
                and mobile = #{mobile}
            </if>
            <if test="eqCode != null and eqCode != ''">
                and eq_code = #{eqCode}
            </if>
            <if test="chargeOrder != null and chargeOrder != ''">
                and charge_order = #{chargeOrder}
            </if>
            <if test="orderStartTime != null">
                and order_start_time = #{orderStartTime}
            </if>
            <if test="orderEndTime != null">
                and order_end_time = #{orderEndTime}
            </if>
            <if test="orderStatus != null">
                and order_status = #{orderStatus}
            </if>
            <if test="orderChargeTime != null and orderChargeTime != ''">
                and order_charge_time = #{orderChargeTime}
            </if>
            <if test="orderMerchant != null and orderMerchant != ''">
                and order_merchant = #{orderMerchant}
            </if>
            <if test="orderUseIntegral != null and orderUseIntegral != ''">
                and order_use_integral = #{orderUseIntegral}
            </if>
            <if test="orderUsePrice != null and orderUsePrice != ''">
                and order_use_price = #{orderUsePrice}
            </if>
            <if test="orderReduction != null and orderReduction != ''">
                and order_reduction = #{orderReduction}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into nideshop_charge_order(
        <trim suffixOverrides=",">
            <if test="mobile != null and mobile != ''">
                mobile ,
            </if>
            <if test="eqCode != null and eqCode != ''">
                eq_code ,
            </if>
            <if test="chargeOrder != null and chargeOrder != ''">
                charge_order ,
            </if>
            <if test="orderStartTime != null">
                order_start_time ,
            </if>
            <if test="orderEndTime != null">
                order_end_time ,
            </if>
            <if test="orderStatus != null">
                order_status ,
            </if>
            <if test="orderChargeTime != null and orderChargeTime != ''">
                order_charge_time ,
            </if>
            <if test="orderMerchant != null and orderMerchant != ''">
                order_merchant ,
            </if>
            <if test="orderUseIntegral != null and orderUseIntegral != ''">
                order_use_integral ,
            </if>
            <if test="orderUsePrice != null and orderUsePrice != ''">
                order_use_price ,
            </if>
            <if test="orderReduction != null and orderReduction != ''">
                order_reduction </if>
        </trim>
        )
        values (
        <trim suffixOverrides=",">
            <if test="mobile != null and mobile != ''">
                #{mobile},
            </if>
            <if test="eqCode != null and eqCode != ''">
                #{eqCode},
            </if>
            <if test="chargeOrder != null and chargeOrder != ''">
                #{chargeOrder},
            </if>
            <if test="orderStartTime != null">
                #{orderStartTime},
            </if>
            <if test="orderEndTime != null">
                #{orderEndTime},
            </if>
            <if test="orderStatus != null">
                #{orderStatus},
            </if>
            <if test="orderChargeTime != null and orderChargeTime != ''">
                #{orderChargeTime},
            </if>
            <if test="orderMerchant != null and orderMerchant != ''">
                #{orderMerchant},
            </if>
            <if test="orderUseIntegral != null and orderUseIntegral != ''">
                #{orderUseIntegral},
            </if>
            <if test="orderUsePrice != null and orderUsePrice != ''">
                #{orderUsePrice},
            </if>
            <if test="orderReduction != null and orderReduction != ''">
                #{orderReduction}</if>
        </trim>)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update nideshop_charge_order
        <set>
            <if test="mobile != null and mobile != ''">
                mobile = #{mobile},
            </if>
            <if test="eqCode != null and eqCode != ''">
                eq_code = #{eqCode},
            </if>
            <if test="chargeOrder != null and chargeOrder != ''">
                charge_order = #{chargeOrder},
            </if>
            <if test="orderStartTime != null">
                order_start_time = #{orderStartTime},
            </if>
            <if test="orderEndTime != null">
                order_end_time = #{orderEndTime},
            </if>
            <if test="orderStatus != null">
                order_status = #{orderStatus},
            </if>
            <if test="orderChargeTime != null and orderChargeTime != ''">
                order_charge_time = #{orderChargeTime},
            </if>
            <if test="orderMerchant != null and orderMerchant != ''">
                order_merchant = #{orderMerchant},
            </if>
            <if test="orderUseIntegral != null and orderUseIntegral != ''">
                order_use_integral = #{orderUseIntegral},
            </if>
            <if test="orderUsePrice != null and orderUsePrice != ''">
                order_use_price = #{orderUsePrice},
            </if>
            <if test="orderReduction != null and orderReduction != ''">
                order_reduction = #{orderReduction},
            </if>
            <if test="income != null">
                income = #{income},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from nideshop_charge_order where id = #{id}
    </delete>

    <select id="queryThatDay" resultMap="NideshopChargeOrderMap">
        SELECT * FROM nideshop_charge_order WHERE order_status = 1 and income = 0
    </select>
</mapper>