<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.modules.commission.mapper.NideshopGoodsMapper">

    <resultMap type="com.jmt.modules.commission.entity.NideshopGoods" id="NideshopGoodsMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="categoryId" column="category_id" jdbcType="INTEGER"/>
        <result property="goodsSn" column="goods_sn" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="brandId" column="brand_id" jdbcType="INTEGER"/>
        <result property="goodsNumber" column="goods_number" jdbcType="INTEGER"/>
        <result property="keywords" column="keywords" jdbcType="VARCHAR"/>
        <result property="goodsBrief" column="goods_brief" jdbcType="VARCHAR"/>
        <result property="goodsDesc" column="goods_desc" jdbcType="VARCHAR"/>
        <result property="isOnSale" column="is_on_sale" jdbcType="BOOLEAN"/>
        <result property="addTime" column="add_time" jdbcType="TIMESTAMP"/>
        <result property="sortOrder" column="sort_order" jdbcType="INTEGER"/>
        <result property="isDelete" column="is_delete" jdbcType="BOOLEAN"/>
        <result property="attributeCategory" column="attribute_category" jdbcType="INTEGER"/>
        <result property="counterPrice" column="counter_price" jdbcType="OTHER"/>
        <result property="extraPrice" column="extra_price" jdbcType="OTHER"/>
        <result property="isNew" column="is_new" jdbcType="BOOLEAN"/>
        <result property="goodsUnit" column="goods_unit" jdbcType="VARCHAR"/>
        <result property="primaryPicUrl" column="primary_pic_url" jdbcType="VARCHAR"/>
        <result property="listPicUrl" column="list_pic_url" jdbcType="VARCHAR"/>
        <result property="retailPrice" column="retail_price" jdbcType="OTHER"/>
        <result property="sellVolume" column="sell_volume" jdbcType="INTEGER"/>
        <result property="primaryProductId" column="primary_product_id" jdbcType="INTEGER"/>
        <result property="unitPrice" column="unit_price" jdbcType="OTHER"/>
        <result property="promotionDesc" column="promotion_desc" jdbcType="VARCHAR"/>
        <result property="promotionTag" column="promotion_tag" jdbcType="VARCHAR"/>
        <result property="appExclusivePrice" column="app_exclusive_price" jdbcType="OTHER"/>
        <result property="isAppExclusive" column="is_app_exclusive" jdbcType="BOOLEAN"/>
        <result property="isLimited" column="is_limited" jdbcType="BOOLEAN"/>
        <result property="isHot" column="is_hot" jdbcType="BOOLEAN"/>
        <result property="marketPrice" column="market_price" jdbcType="OTHER"/>
        <result property="createUserId" column="create_user_id" jdbcType="INTEGER"/>
        <result property="updateUserId" column="update_user_id" jdbcType="INTEGER"/>
        <result property="commissionAmount" column="commission_amount" jdbcType="OTHER"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="createUserDeptId" column="create_user_dept_id" jdbcType="INTEGER"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="NideshopGoodsMap">
        select
          id, category_id, goods_sn, name, brand_id, goods_number, keywords, goods_brief, goods_desc, is_on_sale, add_time, sort_order, is_delete, attribute_category, counter_price, extra_price, is_new, goods_unit, primary_pic_url, list_pic_url, retail_price, sell_volume, primary_product_id, unit_price, promotion_desc, promotion_tag, app_exclusive_price, is_app_exclusive, is_limited, is_hot, market_price, create_user_id, update_user_id, commission_amount, update_time, create_user_dept_id
        from nideshop_goods
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="NideshopGoodsMap">
        select
          id, category_id, goods_sn, name, brand_id, goods_number, keywords, goods_brief, goods_desc, is_on_sale, add_time, sort_order, is_delete, attribute_category, counter_price, extra_price, is_new, goods_unit, primary_pic_url, list_pic_url, retail_price, sell_volume, primary_product_id, unit_price, promotion_desc, promotion_tag, app_exclusive_price, is_app_exclusive, is_limited, is_hot, market_price, create_user_id, update_user_id, commission_amount, update_time, create_user_dept_id
        from nideshop_goods
        limit #{offset}, #{limit}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="NideshopGoodsMap">
        select
          id, category_id, goods_sn, name, brand_id, goods_number, keywords, goods_brief, goods_desc, is_on_sale, add_time, sort_order, is_delete, attribute_category, counter_price, extra_price, is_new, goods_unit, primary_pic_url, list_pic_url, retail_price, sell_volume, primary_product_id, unit_price, promotion_desc, promotion_tag, app_exclusive_price, is_app_exclusive, is_limited, is_hot, market_price, create_user_id, update_user_id, commission_amount, update_time, create_user_dept_id
        from nideshop_goods
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="categoryId != null">
                and category_id = #{categoryId}
            </if>
            <if test="goodsSn != null and goodsSn != ''">
                and goods_sn = #{goodsSn}
            </if>
            <if test="name != null and name != ''">
                and name = #{name}
            </if>
            <if test="brandId != null">
                and brand_id = #{brandId}
            </if>
            <if test="goodsNumber != null">
                and goods_number = #{goodsNumber}
            </if>
            <if test="keywords != null and keywords != ''">
                and keywords = #{keywords}
            </if>
            <if test="goodsBrief != null and goodsBrief != ''">
                and goods_brief = #{goodsBrief}
            </if>
            <if test="goodsDesc != null and goodsDesc != ''">
                and goods_desc = #{goodsDesc}
            </if>
            <if test="isOnSale != null">
                and is_on_sale = #{isOnSale}
            </if>
            <if test="addTime != null">
                and add_time = #{addTime}
            </if>
            <if test="sortOrder != null">
                and sort_order = #{sortOrder}
            </if>
            <if test="isDelete != null">
                and is_delete = #{isDelete}
            </if>
            <if test="attributeCategory != null">
                and attribute_category = #{attributeCategory}
            </if>
            <if test="counterPrice != null">
                and counter_price = #{counterPrice}
            </if>
            <if test="extraPrice != null">
                and extra_price = #{extraPrice}
            </if>
            <if test="isNew != null">
                and is_new = #{isNew}
            </if>
            <if test="goodsUnit != null and goodsUnit != ''">
                and goods_unit = #{goodsUnit}
            </if>
            <if test="primaryPicUrl != null and primaryPicUrl != ''">
                and primary_pic_url = #{primaryPicUrl}
            </if>
            <if test="listPicUrl != null and listPicUrl != ''">
                and list_pic_url = #{listPicUrl}
            </if>
            <if test="retailPrice != null">
                and retail_price = #{retailPrice}
            </if>
            <if test="sellVolume != null">
                and sell_volume = #{sellVolume}
            </if>
            <if test="primaryProductId != null">
                and primary_product_id = #{primaryProductId}
            </if>
            <if test="unitPrice != null">
                and unit_price = #{unitPrice}
            </if>
            <if test="promotionDesc != null and promotionDesc != ''">
                and promotion_desc = #{promotionDesc}
            </if>
            <if test="promotionTag != null and promotionTag != ''">
                and promotion_tag = #{promotionTag}
            </if>
            <if test="appExclusivePrice != null">
                and app_exclusive_price = #{appExclusivePrice}
            </if>
            <if test="isAppExclusive != null">
                and is_app_exclusive = #{isAppExclusive}
            </if>
            <if test="isLimited != null">
                and is_limited = #{isLimited}
            </if>
            <if test="isHot != null">
                and is_hot = #{isHot}
            </if>
            <if test="marketPrice != null">
                and market_price = #{marketPrice}
            </if>
            <if test="createUserId != null">
                and create_user_id = #{createUserId}
            </if>
            <if test="updateUserId != null">
                and update_user_id = #{updateUserId}
            </if>
            <if test="commissionAmount != null">
                and commission_amount = #{commissionAmount}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="createUserDeptId != null">
                and create_user_dept_id = #{createUserDeptId}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into nideshop_goods(
        <trim suffixOverrides=",">
            <if test="categoryId != null">
                category_id ,
            </if>
            <if test="goodsSn != null and goodsSn != ''">
                goods_sn ,
            </if>
            <if test="name != null and name != ''">
                name ,
            </if>
            <if test="brandId != null">
                brand_id ,
            </if>
            <if test="goodsNumber != null">
                goods_number ,
            </if>
            <if test="keywords != null and keywords != ''">
                keywords ,
            </if>
            <if test="goodsBrief != null and goodsBrief != ''">
                goods_brief ,
            </if>
            <if test="goodsDesc != null and goodsDesc != ''">
                goods_desc ,
            </if>
            <if test="isOnSale != null">
                is_on_sale ,
            </if>
            <if test="addTime != null">
                add_time ,
            </if>
            <if test="sortOrder != null">
                sort_order ,
            </if>
            <if test="isDelete != null">
                is_delete ,
            </if>
            <if test="attributeCategory != null">
                attribute_category ,
            </if>
            <if test="counterPrice != null">
                counter_price ,
            </if>
            <if test="extraPrice != null">
                extra_price ,
            </if>
            <if test="isNew != null">
                is_new ,
            </if>
            <if test="goodsUnit != null and goodsUnit != ''">
                goods_unit ,
            </if>
            <if test="primaryPicUrl != null and primaryPicUrl != ''">
                primary_pic_url ,
            </if>
            <if test="listPicUrl != null and listPicUrl != ''">
                list_pic_url ,
            </if>
            <if test="retailPrice != null">
                retail_price ,
            </if>
            <if test="sellVolume != null">
                sell_volume ,
            </if>
            <if test="primaryProductId != null">
                primary_product_id ,
            </if>
            <if test="unitPrice != null">
                unit_price ,
            </if>
            <if test="promotionDesc != null and promotionDesc != ''">
                promotion_desc ,
            </if>
            <if test="promotionTag != null and promotionTag != ''">
                promotion_tag ,
            </if>
            <if test="appExclusivePrice != null">
                app_exclusive_price ,
            </if>
            <if test="isAppExclusive != null">
                is_app_exclusive ,
            </if>
            <if test="isLimited != null">
                is_limited ,
            </if>
            <if test="isHot != null">
                is_hot ,
            </if>
            <if test="marketPrice != null">
                market_price ,
            </if>
            <if test="createUserId != null">
                create_user_id ,
            </if>
            <if test="updateUserId != null">
                update_user_id ,
            </if>
            <if test="commissionAmount != null">
                commission_amount ,
            </if>
            <if test="updateTime != null">
                update_time ,
            </if>
            <if test="createUserDeptId != null">
                create_user_dept_id </if>
        </trim>
        )
        values (
        <trim suffixOverrides=",">
            <if test="categoryId != null">
                #{categoryId},
            </if>
            <if test="goodsSn != null and goodsSn != ''">
                #{goodsSn},
            </if>
            <if test="name != null and name != ''">
                #{name},
            </if>
            <if test="brandId != null">
                #{brandId},
            </if>
            <if test="goodsNumber != null">
                #{goodsNumber},
            </if>
            <if test="keywords != null and keywords != ''">
                #{keywords},
            </if>
            <if test="goodsBrief != null and goodsBrief != ''">
                #{goodsBrief},
            </if>
            <if test="goodsDesc != null and goodsDesc != ''">
                #{goodsDesc},
            </if>
            <if test="isOnSale != null">
                #{isOnSale},
            </if>
            <if test="addTime != null">
                #{addTime},
            </if>
            <if test="sortOrder != null">
                #{sortOrder},
            </if>
            <if test="isDelete != null">
                #{isDelete},
            </if>
            <if test="attributeCategory != null">
                #{attributeCategory},
            </if>
            <if test="counterPrice != null">
                #{counterPrice},
            </if>
            <if test="extraPrice != null">
                #{extraPrice},
            </if>
            <if test="isNew != null">
                #{isNew},
            </if>
            <if test="goodsUnit != null and goodsUnit != ''">
                #{goodsUnit},
            </if>
            <if test="primaryPicUrl != null and primaryPicUrl != ''">
                #{primaryPicUrl},
            </if>
            <if test="listPicUrl != null and listPicUrl != ''">
                #{listPicUrl},
            </if>
            <if test="retailPrice != null">
                #{retailPrice},
            </if>
            <if test="sellVolume != null">
                #{sellVolume},
            </if>
            <if test="primaryProductId != null">
                #{primaryProductId},
            </if>
            <if test="unitPrice != null">
                #{unitPrice},
            </if>
            <if test="promotionDesc != null and promotionDesc != ''">
                #{promotionDesc},
            </if>
            <if test="promotionTag != null and promotionTag != ''">
                #{promotionTag},
            </if>
            <if test="appExclusivePrice != null">
                #{appExclusivePrice},
            </if>
            <if test="isAppExclusive != null">
                #{isAppExclusive},
            </if>
            <if test="isLimited != null">
                #{isLimited},
            </if>
            <if test="isHot != null">
                #{isHot},
            </if>
            <if test="marketPrice != null">
                #{marketPrice},
            </if>
            <if test="createUserId != null">
                #{createUserId},
            </if>
            <if test="updateUserId != null">
                #{updateUserId},
            </if>
            <if test="commissionAmount != null">
                #{commissionAmount},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="createUserDeptId != null">
                #{createUserDeptId}</if>
        </trim>)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update nideshop_goods
        <set>
            <if test="categoryId != null">
                category_id = #{categoryId},
            </if>
            <if test="goodsSn != null and goodsSn != ''">
                goods_sn = #{goodsSn},
            </if>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="brandId != null">
                brand_id = #{brandId},
            </if>
            <if test="goodsNumber != null">
                goods_number = #{goodsNumber},
            </if>
            <if test="keywords != null and keywords != ''">
                keywords = #{keywords},
            </if>
            <if test="goodsBrief != null and goodsBrief != ''">
                goods_brief = #{goodsBrief},
            </if>
            <if test="goodsDesc != null and goodsDesc != ''">
                goods_desc = #{goodsDesc},
            </if>
            <if test="isOnSale != null">
                is_on_sale = #{isOnSale},
            </if>
            <if test="addTime != null">
                add_time = #{addTime},
            </if>
            <if test="sortOrder != null">
                sort_order = #{sortOrder},
            </if>
            <if test="isDelete != null">
                is_delete = #{isDelete},
            </if>
            <if test="attributeCategory != null">
                attribute_category = #{attributeCategory},
            </if>
            <if test="counterPrice != null">
                counter_price = #{counterPrice},
            </if>
            <if test="extraPrice != null">
                extra_price = #{extraPrice},
            </if>
            <if test="isNew != null">
                is_new = #{isNew},
            </if>
            <if test="goodsUnit != null and goodsUnit != ''">
                goods_unit = #{goodsUnit},
            </if>
            <if test="primaryPicUrl != null and primaryPicUrl != ''">
                primary_pic_url = #{primaryPicUrl},
            </if>
            <if test="listPicUrl != null and listPicUrl != ''">
                list_pic_url = #{listPicUrl},
            </if>
            <if test="retailPrice != null">
                retail_price = #{retailPrice},
            </if>
            <if test="sellVolume != null">
                sell_volume = #{sellVolume},
            </if>
            <if test="primaryProductId != null">
                primary_product_id = #{primaryProductId},
            </if>
            <if test="unitPrice != null">
                unit_price = #{unitPrice},
            </if>
            <if test="promotionDesc != null and promotionDesc != ''">
                promotion_desc = #{promotionDesc},
            </if>
            <if test="promotionTag != null and promotionTag != ''">
                promotion_tag = #{promotionTag},
            </if>
            <if test="appExclusivePrice != null">
                app_exclusive_price = #{appExclusivePrice},
            </if>
            <if test="isAppExclusive != null">
                is_app_exclusive = #{isAppExclusive},
            </if>
            <if test="isLimited != null">
                is_limited = #{isLimited},
            </if>
            <if test="isHot != null">
                is_hot = #{isHot},
            </if>
            <if test="marketPrice != null">
                market_price = #{marketPrice},
            </if>
            <if test="createUserId != null">
                create_user_id = #{createUserId},
            </if>
            <if test="updateUserId != null">
                update_user_id = #{updateUserId},
            </if>
            <if test="commissionAmount != null">
                commission_amount = #{commissionAmount},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="createUserDeptId != null">
                create_user_dept_id = #{createUserDeptId},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from nideshop_goods where id = #{id}
    </delete>

</mapper>