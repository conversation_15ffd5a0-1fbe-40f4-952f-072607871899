<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.modules.commission.mapper.CmsRewardMapper">

    <resultMap type="com.jmt.modules.commission.entity.CmsReward" id="CmsRewardMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="projectid" column="projectId" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="eqnumber" column="eqNumber" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="CmsRewardMap">
        select
          id, projectId, name, eqNumber, status
        from cms_reward
        where id = #{id}
    </select>
    <!--查询单个-->
    <select id="queryByProjectId" resultMap="CmsRewardMap">
        select
          id, projectId, name, eqNumber, status
        from cms_reward
        where projectId = #{id}
    </select>
    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="CmsRewardMap">
        select
          id, projectId, name, eqNumber, status
        from cms_reward
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="projectid != null">
                and projectId = #{projectid}
            </if>
            <if test="name != null and name != ''">
                and name = #{name}
            </if>
            <if test="eqnumber != null">
                and eqNumber = #{eqnumber}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into cms_reward(projectId, name, eqNumber, status)
        values (#{projectid}, #{name}, #{eqnumber}, #{status})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update cms_reward
        <set>
            <if test="projectid != null">
                projectId = #{projectid},
            </if>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="eqnumber != null">
                eqNumber = #{eqnumber},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from cms_reward where id = #{id}
    </delete>

    <!--分页查询-->
    <select id="queryAllLimit" resultMap="CmsRewardMap">
        select
          id, projectId, name, eqNumber, status
        from cms_reward
    </select>
</mapper>