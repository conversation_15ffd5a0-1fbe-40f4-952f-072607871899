<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.modules.commission.mapper.CmsEqPartsOrderMapper">

    <resultMap id="BaseResultMap" type="com.jmt.modules.commission.entity.CmsEqPartsOrder">
        <!--@Table cms_eq_parts_order-->
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="orderid" column="orderId" jdbcType="VARCHAR"/>
        <result property="receivername" column="ReceiverName" jdbcType="VARCHAR"/>
        <result property="receiveraddr" column="ReceiverAddr" jdbcType="VARCHAR"/>
        <result property="receiverphone" column="ReceiverPhone" jdbcType="VARCHAR"/>
        <result property="logisticsOrderid" column="logistics_orderId" jdbcType="VARCHAR"/>
        <result property="logisticsName" column="logistics_name" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="paymentMethod" column="payment_method" jdbcType="INTEGER"/>
        <result property="orderprice" column="orderPrice" jdbcType="NUMERIC"/>
        <result property="balancePaymentAmount" column="balance_payment_amount" jdbcType="NUMERIC"/>
        <result property="cashPaymentAmount" column="cash_payment_amount" jdbcType="NUMERIC"/>
        <result property="transactionId" column="transaction_id" jdbcType="VARCHAR"/>
        <result property="pId" column="p_id" jdbcType="INTEGER"/>
        <result property="expireTime" column="expire_time" jdbcType="TIMESTAMP"/>
        <result property="ordertime" column="orderTime" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>


    <!--查询单个-->
    <select id="queryById" resultMap="BaseResultMap">
        select
          id, orderId, ReceiverName, ReceiverAddr, ReceiverPhone, logistics_orderId, logistics_name, status, payment_method, orderPrice, balance_payment_amount, cash_payment_amount, transaction_id, p_id, expire_time,orderTime, update_time
        from cms_eq_parts_order
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="BaseResultMap">
        select
          id, orderId, ReceiverName, ReceiverAddr, ReceiverPhone, logistics_orderId, logistics_name, status, payment_method, orderPrice, balance_payment_amount, cash_payment_amount, transaction_id, p_id,expire_time, orderTime, update_time
        from cms_eq_parts_order
        limit #{offset}, #{limit}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="BaseResultMap">
        select
        id, orderId, ReceiverName, ReceiverAddr, ReceiverPhone, logistics_orderId, logistics_name, status, payment_method, orderPrice, balance_payment_amount, cash_payment_amount, transaction_id, p_id,expire_time, orderTime, update_time
        from cms_eq_parts_order
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="orderid != null and orderid != ''">
                and orderId = #{orderid}
            </if>
            <if test="receivername != null and receivername != ''">
                and ReceiverName = #{receivername}
            </if>
            <if test="receiveraddr != null and receiveraddr != ''">
                and ReceiverAddr = #{receiveraddr}
            </if>
            <if test="receiverphone != null and receiverphone != ''">
                and ReceiverPhone = #{receiverphone}
            </if>
            <if test="logisticsOrderid != null and logisticsOrderid != ''">
                and logistics_orderId = #{logisticsOrderid}
            </if>
            <if test="logisticsName != null and logisticsName != ''">
                and logistics_name = #{logisticsName}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="paymentMethod != null">
                and payment_method = #{paymentMethod}
            </if>
            <if test="orderprice != null">
                and orderPrice = #{orderprice}
            </if>
            <if test="balancePaymentAmount != null">
                and balance_payment_amount = #{balancePaymentAmount}
            </if>
            <if test="cashPaymentAmount != null">
                and cash_payment_amount = #{cashPaymentAmount}
            </if>
            <if test="transactionId != null and transactionId != ''">
                and transaction_id = #{transactionId}
            </if>
            <if test="pId != null">
                and p_id = #{pId}
            </if>
            <if test="ordertime != null">
                and orderTime = #{ordertime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into cms_eq_parts_order(orderId, ReceiverName, ReceiverAddr, ReceiverPhone, logistics_orderId, logistics_name, status, payment_method, orderPrice, balance_payment_amount, cash_payment_amount, transaction_id, p_id, expire_time,orderTime, update_time)
        values (#{orderid}, #{receivername}, #{receiveraddr}, #{receiverphone}, #{logisticsOrderid}, #{logisticsName}, #{status}, #{paymentMethod}, #{orderprice}, #{balancePaymentAmount}, #{cashPaymentAmount}, #{transactionId}, #{pId},#{expireTime}, #{ordertime}, #{updateTime})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update cms_eq_parts_order
        <set>
            <if test="orderid != null and orderid != ''">
                orderId = #{orderid},
            </if>
            <if test="receivername != null and receivername != ''">
                ReceiverName = #{receivername},
            </if>
            <if test="receiveraddr != null and receiveraddr != ''">
                ReceiverAddr = #{receiveraddr},
            </if>
            <if test="receiverphone != null and receiverphone != ''">
                ReceiverPhone = #{receiverphone},
            </if>
            <if test="logisticsOrderid != null and logisticsOrderid != ''">
                logistics_orderId = #{logisticsOrderid},
            </if>
            <if test="logisticsName != null and logisticsName != ''">
                logistics_name = #{logisticsName},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="paymentMethod != null">
                payment_method = #{paymentMethod},
            </if>
            <if test="orderprice != null">
                orderPrice = #{orderprice},
            </if>
            <if test="balancePaymentAmount != null">
                balance_payment_amount = #{balancePaymentAmount},
            </if>
            <if test="cashPaymentAmount != null">
                cash_payment_amount = #{cashPaymentAmount},
            </if>
            <if test="transactionId != null and transactionId != ''">
                transaction_id = #{transactionId},
            </if>
            <if test="pId != null">
                p_id = #{pId},
            </if>
            <if test="ordertime != null">
                orderTime = #{ordertime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from cms_eq_parts_order where id = #{id}
    </delete>


    <select id="queryAllByLike" resultType="com.jmt.modules.commission.model.vo.CmsEqPartsOrderListVo">
        SELECT o.id, orderId, ReceiverName, ReceiverAddr, ReceiverPhone,
         orderPrice, logistics_orderId, logistics_name,status, orderTime, o.p_id,p_name,
         (SELECT SUM(parts_quantity) FROM cms_eq_parts_order_detai WHERE orderId = o.`orderId`) AS eqNum
          FROM cms_eq_parts_order o LEFT JOIN cms_partner_info p ON o.p_id=p.`p_id`
          <where>
              <if test="cms.startTime !=null and cms.startTime !=''">
                and  orderTime >= concat(#{cms.startTime},' 00.00.00')
              </if>
              <if test="cms.sendTime !=null and cms.sendTime !=''">
                  and  orderTime &lt;= concat(#{cms.sendTime},' 23.59.59')
              </if>
              <if test="cms.like !=null and cms.like !=''">
                  and  (p_name = #{cms.like} or p.p_id = #{cms.like})
              </if>
              <if test="cms.phone !=null and cms.phone !=''">
                  and  ReceiverPhone = #{phone}
              </if>
              <if test="cms.status !=null and cms.status !=''">
                  and  status = #{cms.status}
              </if>
          </where>
        order by o.orderTime desc
    </select>

    <select id="queryByOrderId" resultType="com.jmt.modules.commission.model.vo.CmsEqPartsOrderInfoVo">
        SELECT o.id, orderId, ReceiverName, ReceiverAddr, ReceiverPhone, orderPrice, logistics_orderId,
         logistics_name, STATUS, orderTime, o.p_id,p.`p_name`
         FROM cms_eq_parts_order o LEFT JOIN cms_partner_info p ON o.p_id=p.`p_id`
         where orderId = #{orderId}
    </select>

    <select id="queryByOrderIds" resultMap="BaseResultMap">
        select
        id, orderId, ReceiverName, ReceiverAddr, ReceiverPhone, logistics_orderId, logistics_name, status, payment_method, orderPrice, balance_payment_amount, cash_payment_amount, transaction_id, p_id,expire_time, orderTime, update_time
        from cms_eq_parts_order
        where orderId = #{orderId}
    </select>

    <update id="updateStatusByOrderId">
        update cms_eq_parts_order set status = #{status},logistics_orderId = #{logisticsNumber},logistics_name = #{logisticsName} where orderId = #{orderId}
    </update>

    <select id="queryAllApp" resultType="com.jmt.modules.commission.model.vo.CmsEqPartsOrderListAppVo">
        select * from cms_eq_parts_order
        <where>
            p_id = #{map.pid}
            <if test="map.status != 3">
                and status = #{map.status}
            </if>
        </where>
        order by orderTime desc
    </select>
</mapper>