<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.modules.commission.mapper.NideshopOrderGoodsMapper">

    <resultMap type="com.jmt.modules.commission.entity.NideshopOrderGoods" id="NideshopOrderGoodsMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="orderId" column="order_id" jdbcType="INTEGER"/>
        <result property="goodsId" column="goods_id" jdbcType="INTEGER"/>
        <result property="goodsName" column="goods_name" jdbcType="VARCHAR"/>
        <result property="goodsSn" column="goods_sn" jdbcType="VARCHAR"/>
        <result property="productId" column="product_id" jdbcType="INTEGER"/>
        <result property="number" column="number" jdbcType="INTEGER"/>
        <result property="marketPrice" column="market_price" jdbcType="OTHER"/>
        <result property="retailPrice" column="retail_price" jdbcType="OTHER"/>
        <result property="goodsSpecifitionNameValue" column="goods_specifition_name_value" jdbcType="VARCHAR"/>
        <result property="isReal" column="is_real" jdbcType="BOOLEAN"/>
        <result property="goodsSpecifitionIds" column="goods_specifition_ids" jdbcType="VARCHAR"/>
        <result property="listPicUrl" column="list_pic_url" jdbcType="VARCHAR"/>
        <result property="commissionAmount" column="commission_amount" jdbcType="OTHER"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="NideshopOrderGoodsMap">
        select
          id, order_id, goods_id, goods_name, goods_sn, product_id, number, market_price, retail_price, goods_specifition_name_value, is_real, goods_specifition_ids, list_pic_url, commission_amount
        from nideshop_order_goods
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="NideshopOrderGoodsMap">
        select
          id, order_id, goods_id, goods_name, goods_sn, product_id, number, market_price, retail_price, goods_specifition_name_value, is_real, goods_specifition_ids, list_pic_url, commission_amount
        from nideshop_order_goods
        limit #{offset}, #{limit}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="NideshopOrderGoodsMap">
        select
          id, order_id, goods_id, goods_name, goods_sn, product_id, number, market_price, retail_price, goods_specifition_name_value, is_real, goods_specifition_ids, list_pic_url, commission_amount
        from nideshop_order_goods
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="orderId != null">
                and order_id = #{orderId}
            </if>
            <if test="goodsId != null">
                and goods_id = #{goodsId}
            </if>
            <if test="goodsName != null and goodsName != ''">
                and goods_name = #{goodsName}
            </if>
            <if test="goodsSn != null and goodsSn != ''">
                and goods_sn = #{goodsSn}
            </if>
            <if test="productId != null">
                and product_id = #{productId}
            </if>
            <if test="number != null">
                and number = #{number}
            </if>
            <if test="marketPrice != null">
                and market_price = #{marketPrice}
            </if>
            <if test="retailPrice != null">
                and retail_price = #{retailPrice}
            </if>
            <if test="goodsSpecifitionNameValue != null and goodsSpecifitionNameValue != ''">
                and goods_specifition_name_value = #{goodsSpecifitionNameValue}
            </if>
            <if test="isReal != null">
                and is_real = #{isReal}
            </if>
            <if test="goodsSpecifitionIds != null and goodsSpecifitionIds != ''">
                and goods_specifition_ids = #{goodsSpecifitionIds}
            </if>
            <if test="listPicUrl != null and listPicUrl != ''">
                and list_pic_url = #{listPicUrl}
            </if>
            <if test="commissionAmount != null">
                and commission_amount = #{commissionAmount}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into nideshop_order_goods(
        <trim suffixOverrides=",">
            <if test="orderId != null">
                order_id ,
            </if>
            <if test="goodsId != null">
                goods_id ,
            </if>
            <if test="goodsName != null and goodsName != ''">
                goods_name ,
            </if>
            <if test="goodsSn != null and goodsSn != ''">
                goods_sn ,
            </if>
            <if test="productId != null">
                product_id ,
            </if>
            <if test="number != null">
                number ,
            </if>
            <if test="marketPrice != null">
                market_price ,
            </if>
            <if test="retailPrice != null">
                retail_price ,
            </if>
            <if test="goodsSpecifitionNameValue != null and goodsSpecifitionNameValue != ''">
                goods_specifition_name_value ,
            </if>
            <if test="isReal != null">
                is_real ,
            </if>
            <if test="goodsSpecifitionIds != null and goodsSpecifitionIds != ''">
                goods_specifition_ids ,
            </if>
            <if test="listPicUrl != null and listPicUrl != ''">
                list_pic_url ,
            </if>
            <if test="commissionAmount != null">
                commission_amount </if>
        </trim>
        )
        values (
        <trim suffixOverrides=",">
            <if test="orderId != null">
                #{orderId},
            </if>
            <if test="goodsId != null">
                #{goodsId},
            </if>
            <if test="goodsName != null and goodsName != ''">
                #{goodsName},
            </if>
            <if test="goodsSn != null and goodsSn != ''">
                #{goodsSn},
            </if>
            <if test="productId != null">
                #{productId},
            </if>
            <if test="number != null">
                #{number},
            </if>
            <if test="marketPrice != null">
                #{marketPrice},
            </if>
            <if test="retailPrice != null">
                #{retailPrice},
            </if>
            <if test="goodsSpecifitionNameValue != null and goodsSpecifitionNameValue != ''">
                #{goodsSpecifitionNameValue},
            </if>
            <if test="isReal != null">
                #{isReal},
            </if>
            <if test="goodsSpecifitionIds != null and goodsSpecifitionIds != ''">
                #{goodsSpecifitionIds},
            </if>
            <if test="listPicUrl != null and listPicUrl != ''">
                #{listPicUrl},
            </if>
            <if test="commissionAmount != null">
                #{commissionAmount}</if>
        </trim>)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update nideshop_order_goods
        <set>
            <if test="orderId != null">
                order_id = #{orderId},
            </if>
            <if test="goodsId != null">
                goods_id = #{goodsId},
            </if>
            <if test="goodsName != null and goodsName != ''">
                goods_name = #{goodsName},
            </if>
            <if test="goodsSn != null and goodsSn != ''">
                goods_sn = #{goodsSn},
            </if>
            <if test="productId != null">
                product_id = #{productId},
            </if>
            <if test="number != null">
                number = #{number},
            </if>
            <if test="marketPrice != null">
                market_price = #{marketPrice},
            </if>
            <if test="retailPrice != null">
                retail_price = #{retailPrice},
            </if>
            <if test="goodsSpecifitionNameValue != null and goodsSpecifitionNameValue != ''">
                goods_specifition_name_value = #{goodsSpecifitionNameValue},
            </if>
            <if test="isReal != null">
                is_real = #{isReal},
            </if>
            <if test="goodsSpecifitionIds != null and goodsSpecifitionIds != ''">
                goods_specifition_ids = #{goodsSpecifitionIds},
            </if>
            <if test="listPicUrl != null and listPicUrl != ''">
                list_pic_url = #{listPicUrl},
            </if>
            <if test="commissionAmount != null">
                commission_amount = #{commissionAmount},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from nideshop_order_goods where id = #{id}
    </delete>

    <select id="queryByOrderId" resultMap="NideshopOrderGoodsMap">
        select
          id, order_id, goods_id, goods_name, goods_sn, product_id, number, market_price, retail_price, goods_specifition_name_value, is_real, goods_specifition_ids, list_pic_url, commission_amount
        from nideshop_order_goods
        where order_id = #{orderSn}
    </select>
</mapper>