<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.modules.commission.mapper.CmsEqApplyOrderMapper">

    <resultMap type="com.jmt.modules.commission.entity.CmsEqApplyOrder" id="CmsEqApplyOrderMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="orderid" column="orderId" jdbcType="VARCHAR"/>
        <result property="eqapplynum" column="eqApplyNum" jdbcType="INTEGER"/>
        <result property="pCode" column="p_code" jdbcType="VARCHAR"/>
        <result property="receivername" column="ReceiverName" jdbcType="VARCHAR"/>
        <result property="receiverphone" column="ReceiverPhone" jdbcType="INTEGER"/>
        <result property="receiveraddr" column="ReceiverAddr" jdbcType="VARCHAR"/>
        <result property="creationtime" column="Creationtime" jdbcType="TIMESTAMP"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="remarks" column="remarks" jdbcType="VARCHAR"/>
        <result property="logisticsnumber" column="logisticsNumber" jdbcType="VARCHAR"/>
        <result property="logisticsname" column="logisticsName" jdbcType="VARCHAR"/>
        <result property="recevingStatus" column="receving_status" javaType="INTEGER"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="CmsEqApplyOrderMap">
        select
          id, orderId, eqApplyNum, p_code, ReceiverName, ReceiverPhone, ReceiverAddr, Creationtime, status, remarks, logisticsNumber, logisticsName,receving_status
        from cms_eq_apply_order
        where id = #{id}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="CmsEqApplyOrderMap">
        select
        id, orderId, eqApplyNum, p_code, ReceiverName, ReceiverPhone, ReceiverAddr, Creationtime, status, remarks, logisticsNumber, logisticsName
        from cms_eq_apply_order
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="orderid != null and orderid != ''">
                and orderId = #{orderid}
            </if>
            <if test="eqapplynum != null">
                and eqApplyNum = #{eqapplynum}
            </if>
            <if test="pCode != null and pCode != ''">
                and p_code = #{pCode}
            </if>
            <if test="receivername != null and receivername != ''">
                and ReceiverName = #{receivername}
            </if>
            <if test="receiverphone != null">
                and ReceiverPhone = #{receiverphone}
            </if>
            <if test="receiveraddr != null and receiveraddr != ''">
                and ReceiverAddr = #{receiveraddr}
            </if>
            <if test="creationtime != null">
                and Creationtime = #{creationtime}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="remarks != null and remarks != ''">
                and remarks = #{remarks}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into cms_eq_apply_order(orderId, eqApplyNum, p_code, ReceiverName, ReceiverPhone, ReceiverAddr, Creationtime, status, remarks)
        values (#{orderid}, #{eqapplynum}, #{pCode}, #{receivername}, #{receiverphone}, #{receiveraddr}, #{creationtime}, #{status}, #{remarks})
    </insert>

    <!--通过订单id修改数据-->
    <update id="update">
        update cms_eq_apply_order
        <set>
            <if test="eqapplynum != null">
                eqApplyNum = #{eqapplynum},
            </if>
            <if test="pCode != null and pCode != ''">
                p_code = #{pCode},
            </if>
            <if test="receivername != null and receivername != ''">
                ReceiverName = #{receivername},
            </if>
            <if test="receiverphone != null">
                ReceiverPhone = #{receiverphone},
            </if>
            <if test="receiveraddr != null and receiveraddr != ''">
                ReceiverAddr = #{receiveraddr},
            </if>
            <if test="creationtime != null">
                Creationtime = #{creationtime},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="remarks != null and remarks != ''">
                remarks = #{remarks},
            </if>
            <if test="logisticsnumber != null and logisticsnumber != ''">
                logisticsNumber = #{logisticsnumber},
            </if>
            <if test="logisticsname != null and logisticsname != ''">
                logisticsName = #{logisticsname},
            </if>
            <if test="recevingStatus !=null">
                receving_status = #{recevingStatus},
            </if>
        </set>
        where orderId = #{orderid}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from cms_eq_apply_order where id = #{id}
    </delete>

    <select id="queryAllByLike" resultType="com.jmt.modules.commission.model.vo.CmsEqApplyOrderListVo">
        SELECT o.id,o.`orderId`,o.eqApplyNum,o.Creationtime,o.receving_status,p_name,CONCAT_WS('/',p_area_province,p_area_city) AS address,
        o.status FROM cms_eq_apply_order o LEFT JOIN
         cms_partner_info p ON o.p_code = p.`p_id`
          LEFT JOIN cms_area a ON a.`p_id` = o.p_code
          <where>
              <if test="cms.like!=null and cms.like!=''">
                 and (p.p_id = #{cms.like} or p.p_name = #{cms.like})
              </if>
              <if test="cms.startTime !=null and cms.startTime !=''">
                  and o.Creationtime >=CONCAT(#{cms.startTime},' 00:00:00')
              </if>

              <if test="cms.sendTime !=null and cms.sendTime !=''">
                  and o.Creationtime &lt;= CONCAT(#{cms.sendTime},' 23:59:59')
              </if>
              <if test="cms.province!=null">
                  and a.p_area_province_num = #{cms.province}
              </if>
              <if test="cms.city!=null">
                  and a.p_area_city_num = #{cms.city}
              </if>
              <if test="cms.status!=null">
                  and o.status = #{cms.status}
              </if>
          </where>
        order by o.Creationtime desc
    </select>

    <select id="queryByOrderId" resultType="com.jmt.modules.commission.model.vo.CmsEqApplyOrderInfoVo">
        SELECT o.*,p.`p_name`,e.`p_eq_surplus` FROM cms_eq_apply_order o
        LEFT JOIN cms_partner_info p ON o.`p_code` = p.`p_id`
        LEFT JOIN cms_eq_info e ON o.`p_code`= e.`p_code`
        where o.orderId=#{orderId}
    </select>

    <select id="queryByOrderIds" resultMap="CmsEqApplyOrderMap">
        select * from cms_eq_apply_order where orderId = #{orderId};
    </select>

    <select id="queryAllByStatusApp" resultMap="CmsEqApplyOrderMap">
        select * from cms_eq_apply_order
        <where>
            p_code = #{pId}
            <if test="status != null and status !=''">
                and status = #{status}
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="listUnreceivedOrder" resultMap="CmsEqApplyOrderMap">
        SELECT * FROM cms_eq_apply_order WHERE DATE_SUB(CURDATE(),INTERVAL 10 DAY) > update_time and status = 5 and receving_status = 0
    </select>
</mapper>