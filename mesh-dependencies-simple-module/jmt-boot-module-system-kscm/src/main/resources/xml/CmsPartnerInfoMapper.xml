<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.modules.commission.mapper.CmsPartnerInfoMapper">

    <resultMap id="CmsPartnerInfoMap" type="com.jmt.modules.commission.entity.CmsPartnerInfo">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="pName" column="p_name" jdbcType="VARCHAR"/>
        <result property="pContractNum" column="p_contract_num" jdbcType="VARCHAR"/>
        <result property="pContractNumMoney" column="p_contract_num_money"/>
        <result property="pContractStarTime" column="p_contract_star_time" jdbcType="TIMESTAMP"/>
        <result property="pContractEndTime" column="p_contract_end_time" jdbcType="TIMESTAMP"/>
        <result property="pContactsName" column="p_contacts_name" jdbcType="VARCHAR"/>
        <result property="pRecommenderCode" column="p_recommender_code" jdbcType="INTEGER"/>
        <result property="pSex" column="p_sex" jdbcType="INTEGER"/>
        <result property="pPhoneNum" column="p_phone_num" jdbcType="VARCHAR"/>
        <result property="pLoginName" column="p_login_name" jdbcType="VARCHAR"/>
        <result property="pLoginPwd" column="p_login_pwd" jdbcType="VARCHAR"/>
        <result property="pStatus" column="p_status" jdbcType="INTEGER"/>
        <result property="userCId" column="user_c_id" jdbcType="INTEGER"/>
        <result property="pCertificatesCode" column="p_certificates_code" jdbcType="VARCHAR"/>
        <result property="pEmail" column="p_email" jdbcType="VARCHAR"/>
        <result property="pRefereeCode" column="p_referee_code" jdbcType="VARCHAR"/>
        <result property="pProfit" column="p_profit" jdbcType="OTHER"/>
        <result property="pWithdrawMoney" column="p_withdraw_money" jdbcType="OTHER"/>
        <result property="pEqNum" column="p_eq_num" jdbcType="INTEGER"/>
        <result property="pLevel" column="p_level" jdbcType="INTEGER"/>
        <result property="teaminfo" column="teamInfo" jdbcType="VARCHAR"/>
        <result property="paymentPassword" column="payment_password" jdbcType="INTEGER"/>
        <result property="partnerType" column="partner_type"/>
        <result property="maxCommissionAmount" column="max_commission_amount"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <resultMap id="cmsPartnerProfitInfoMap" type="com.jmt.modules.commission.model.vo.CmsPartnerProfitInfoVo">
        <result property="pName" column="p_name"/>
        <result property="pRecommenderCode" column="p_recommender_code"/>
        <result property="eqTotal" column="p_eq_num"/>
        <result property="address" column="address"/>
        <result property="onlineEqNum" column="onlineEq"/>
        <result property="activationEqNum" column="eqTotal"/>
        <result property="merchantNum" column="shopNum"/>
    </resultMap>
    <resultMap id="operatorMap" type="com.jmt.modules.commission.model.vo.CmsPartsInfoOperatorVo">
        <result property="id" column="id"/>
        <result property="pId" column="p_id"/>
        <result property="pContactsName" column="p_contacts_name"/>
        <result property="pProfit" column="p_profit"/>
    </resultMap>
    <!--查询单个-->
    <select id="queryById" resultMap="CmsPartnerInfoMap">
        select
          id, p_id, p_name, p_contract_num, p_contract_star_time, p_contract_end_time, p_contacts_name, p_recommender_code, p_sex, p_phone_num, p_login_name, p_login_pwd, p_status, user_c_id, p_certificates_code, p_email, p_referee_code, p_profit, p_withdraw_money, p_eq_num, p_level, teamInfo,payment_password, createTime, updateTime
        from yy_partner_info
        where id = #{id}
    </select>
    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="CmsPartnerInfoMap">
        select
          id, p_id, p_name, p_contract_num, p_contract_star_time, p_contract_end_time, p_contacts_name, p_recommender_code, p_sex, p_phone_num, p_login_name, p_login_pwd, p_status, user_c_id, p_certificates_code, p_email, p_referee_code, p_profit, p_withdraw_money, p_eq_num, p_level, teamInfo, createTime, updateTime
        from yy_partner_info
        limit #{offset}, #{limit}
    </select>

    <select id="statPartnerByYearMonth" resultType="com.jmt.modules.commission.model.vo.PartnerYearMonthVO">
       select DATE_FORMAT(createTime,'%Y-%m') as createTime,count(*) as allnum  from yy_partner_info
            WHERE partner_type!=2
                GROUP BY DATE_FORMAT(createTime,'%Y-%m')
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="CmsPartnerInfoMap">
        select
        id, p_name, p_contract_num, p_contract_star_time, p_contract_end_time, p_contacts_name, p_recommender_code, p_sex, p_phone_num, p_login_name, p_login_pwd, p_status, user_c_id, p_certificates_code, p_email, p_referee_code, p_profit, p_withdraw_money, p_eq_num, p_level, teamInfo, createTime, updateTime
        from yy_partner_info
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="pId != null">
                and p_id = #{pId}
            </if>
            <if test="pName != null and pName != ''">
                and p_name = #{pName}
            </if>
            <if test="pContractNum != null and pContractNum != ''">
                and p_contract_num = #{pContractNum}
            </if>
            <if test="pContractStarTime != null">
                and p_contract_star_time = #{pContractStarTime}
            </if>
            <if test="pContractEndTime != null">
                and p_contract_end_time = #{pContractEndTime}
            </if>
            <if test="pContactsName != null and pContactsName != ''">
                and p_contacts_name = #{pContactsName}
            </if>
            <if test="pRecommenderCode != null">
                and p_recommender_code = #{pRecommenderCode}
            </if>
            <if test="pSex != null">
                and p_sex = #{pSex}
            </if>
            <if test="pPhoneNum != null and pPhoneNum != ''">
                and p_phone_num = #{pPhoneNum}
            </if>
            <if test="pLoginName != null and pLoginName != ''">
                and p_login_name = #{pLoginName}
            </if>
            <if test="pLoginPwd != null and pLoginPwd != ''">
                and p_login_pwd = #{pLoginPwd}
            </if>
            <if test="pStatus != null">
                and p_status = #{pStatus}
            </if>
            <if test="userCId != null">
                and user_c_id = #{userCId}
            </if>
            <if test="pCertificatesCode != null and pCertificatesCode != ''">
                and p_certificates_code = #{pCertificatesCode}
            </if>
            <if test="pEmail != null and pEmail != ''">
                and p_email = #{pEmail}
            </if>
            <if test="pRefereeCode != null and pRefereeCode != ''">
                and p_referee_code = #{pRefereeCode}
            </if>
            <if test="pProfit != null">
                and p_profit = #{pProfit}
            </if>
            <if test="pWithdrawMoney != null">
                and p_withdraw_money = #{pWithdrawMoney}
            </if>
            <if test="pEqNum != null">
                and p_eq_num = #{pEqNum}
            </if>
            <if test="pLevel != null">
                and p_level = #{pLevel}
            </if>
            <if test="teaminfo != null and teaminfo != ''">
                and teamInfo = #{teaminfo}
            </if>
            <if test="createtime != null">
                and createTime = #{createtime}
            </if>
            <if test="updatetime != null">
                and updateTime = #{updatetime}
            </if>
        </where>
    </select>

    <!--新增所有列-->
<!--    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into yy_partner_info(p_id, p_name, p_contract_num, p_contract_star_time, p_contract_end_time, p_contacts_name, p_recommender_code, p_sex, p_phone_num, p_login_name, p_login_pwd, p_status, user_c_id, p_certificates_code, p_email, p_referee_code, p_profit, p_withdraw_money, p_eq_num, p_level, teamInfo, payment_password,createTime, updateTime)
        values (#{pId}, #{pName}, #{pContractNum}, #{pContractStarTime}, #{pContractEndTime}, #{pContactsName}, #{pRecommenderCode}, #{pSex}, #{pPhoneNum}, #{pLoginName}, #{pLoginPwd}, #{pStatus}, #{userCId}, #{pCertificatesCode}, #{pEmail}, #{pRefereeCode}, #{pProfit}, #{pWithdrawMoney}, #{pEqNum}, #{pLevel}, #{teaminfo},#{paymentPassword}, #{createtime}, #{updatetime})
    </insert>-->

    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into yy_partner_info
        (
        <trim suffixOverrides=",">
            <if test="uaaId != null">
                uaa_id,
            </if>
            <if test="pId != null">
                p_id,
            </if>
            <if test="pName != null and pName != ''">
                p_name,
            </if>
            <if test="pContractNum != null and pContractNum != ''">
                p_contract_num,
            </if>
            <if test="pContractNumMoney != null and pContractNumMoney != ''">
                p_contract_num_money,
            </if>
            <if test="pContractStarTime != null">
                p_contract_star_time,
            </if>
            <if test="pContractEndTime != null">
                p_contract_end_time,
            </if>
            <if test="pContactsName != null and pContactsName != ''">
                p_contacts_name,
            </if>
            <if test="pRecommenderCode != null">
                p_recommender_code,
            </if>
            <if test="pSex != null">
                p_sex,
            </if>
            <if test="pPhoneNum != null and pPhoneNum != ''">
                p_phone_num,
            </if>
            <if test="pLoginName != null and pLoginName != ''">
                p_login_name,
            </if>
            <if test="pLoginPwd != null and pLoginPwd != ''">
                p_login_pwd,
            </if>
            <if test="pStatus != null">
                p_status,
            </if>
            <if test="userCId != null">
                user_c_id,
            </if>
            <if test="pCertificatesCode != null and pCertificatesCode != ''">
                p_certificates_code,
            </if>
            <if test="pEmail != null and pEmail != ''">
                p_email,
            </if>
            <if test="pRefereeCode != null and pRefereeCode != ''">
                p_referee_code,
            </if>
            <if test="pProfit != null">
                p_profit,
            </if>
            <if test="pWithdrawMoney != null">
                p_withdraw_money,
            </if>
            <if test="pEqNum != null">
                p_eq_num,
            </if>
            <if test="pLevel != null">
                p_level,
            </if>
            <if test="teaminfo != null and teaminfo != ''">
                teamInfo,
            </if>
            <if test="paymentPassword != null">
                payment_password,
            </if>
            <if test="pIsRegister != null">
                p_is_register,
            </if>
            <if test="partnerType != null">
                partner_type,
            </if>
            <if test="maxCommissionAmount != null">
                max_commission_amount,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time
            </if>
        </trim>
        ) values(
        <trim suffixOverrides=",">
            <if test="uaaId != null">
                #{uaaId},
            </if>
            <if test="pId != null">
                #{pId},
            </if>
            <if test="pName != null and pName != ''">
                #{pName},
            </if>
            <if test="pContractNum != null and pContractNum != ''">
                #{pContractNum},
            </if>
            <if test="pContractNumMoney != null and pContractNumMoney != ''">
                #{pContractNumMoney},
            </if>
            <if test="pContractStarTime != null">
                #{pContractStarTime},
            </if>
            <if test="pContractEndTime != null">
                #{pContractEndTime},
            </if>
            <if test="pContactsName != null and pContactsName != ''">
                #{pContactsName},
            </if>
            <if test="pRecommenderCode != null">
                #{pRecommenderCode},
            </if>
            <if test="pSex != null">
                #{pSex},
            </if>
            <if test="pPhoneNum != null and pPhoneNum != ''">
                #{pPhoneNum},
            </if>
            <if test="pLoginName != null and pLoginName != ''">
                #{pLoginName},
            </if>
            <if test="pLoginPwd != null and pLoginPwd != ''">
                #{pLoginPwd},
            </if>
            <if test="pStatus != null">
                #{pStatus},
            </if>
            <if test="userCId != null">
                #{userCId},
            </if>
            <if test="pCertificatesCode != null and pCertificatesCode != ''">
                #{pCertificatesCode},
            </if>
            <if test="pEmail != null and pEmail != ''">
                #{pEmail},
            </if>
            <if test="pRefereeCode != null and pRefereeCode != ''">
                #{pRefereeCode},
            </if>
            <if test="pProfit != null">
                #{pProfit},
            </if>
            <if test="pWithdrawMoney != null">
                #{pWithdrawMoney},
            </if>
            <if test="pEqNum != null">
                #{pEqNum},
            </if>
            <if test="pLevel != null">
                #{pLevel},
            </if>
            <if test="teaminfo != null and teaminfo != ''">
                #{teaminfo},
            </if>
            <if test="paymentPassword != null">
                #{paymentPassword},
            </if>
            <if test="pIsRegister != null">
                #{pIsRegister},
            </if>
            <if test="partnerType != null">
                #{partnerType},
            </if>
            <if test="maxCommissionAmount != null">
                #{maxCommissionAmount},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateTime != null">
                #{updateTime}
            </if>
        </trim>
        )
    </insert>





    <!--通过合伙人编号修改数据-->
    <update id="update" parameterType="com.jmt.modules.commission.entity.CmsPartnerInfo">
        update yy_partner_info
        <set>
            <if test="uaaId != null and uaaId != ''">
                uaa_id = #{uaaId},
            </if>
            <if test="pName != null and pName != ''">
                p_name = #{pName},
            </if>
            <if test="pContractNum != null and pContractNum != ''">
                p_contract_num = #{pContractNum},
            </if>
            <if test="pContractNumMoney != null and pContractNumMoney != ''">
                p_contract_num_money = #{pContractNumMoney},
            </if>
            <if test="pContractStarTime != null">
                p_contract_star_time = #{pContractStarTime},
            </if>
            <if test="pContractEndTime != null">
                p_contract_end_time = #{pContractEndTime},
            </if>
            <if test="pContactsName != null and pContactsName != ''">
                p_contacts_name = #{pContactsName},
            </if>
            <if test="pRecommenderCode != null">
                p_recommender_code = #{pRecommenderCode},
            </if>
            <if test="pSex != null">
                p_sex = #{pSex},
            </if>
            <if test="pPhoneNum != null and pPhoneNum != ''">
                p_phone_num = #{pPhoneNum},
            </if>
            <if test="pLoginName != null and pLoginName != ''">
                p_login_name = #{pLoginName},
            </if>
            <if test="pLoginPwd != null and pLoginPwd != ''">
                p_login_pwd = #{pLoginPwd},
            </if>
            <if test="pStatus != null">
                p_status = #{pStatus},
            </if>
            <if test="userCId != null">
                user_c_id = #{userCId},
            </if>
            <if test="pCertificatesCode != null and pCertificatesCode != ''">
                p_certificates_code = #{pCertificatesCode},
            </if>
            <if test="pEmail != null and pEmail != ''">
                p_email = #{pEmail},
            </if>
            <if test="pRefereeCode != null and pRefereeCode != ''">
                p_referee_code = #{pRefereeCode},
            </if>
            <if test="pProfit != null">
                p_profit = #{pProfit},
            </if>
            <if test="pWithdrawMoney != null">
                p_withdraw_money = #{pWithdrawMoney},
            </if>
            <if test="pEqNum != null">
                p_eq_num = #{pEqNum},
            </if>
            <if test="pLevel != null">
                p_level = #{pLevel},
            </if>
            <if test="teaminfo != null and teaminfo != ''">
                teamInfo = #{teaminfo},
            </if>
            <if test="paymentPassword != null">
                payment_password = #{paymentPassword},
            </if>
            <if test="partnerType != null">
                partner_type = #{partnerType},
            </if>
            <if test="maxCommissionAmount != null">
                max_commission_amount = #{maxCommissionAmount},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
        </set>
        where p_id = #{pId}
    </update>

    <update id="updatePstatus">
        update yy_partner_info set p_status = #{pStatus},uaa_id=#{uaaId}
        <where>
            p_id = #{pId}
            <if test="reject == 1">
            and p_status != 2
            </if>
        </where>
    </update>
    <!--通过主键删除-->
    <delete id="deleteById">
        delete from yy_partner_info where id = #{id}
    </delete>

    <!--列表查询-->
    <select id="listCmsPartnerInfo" resultType="com.jmt.modules.commission.model.vo.CmsPartnerInfoListVo">
        SELECT CONCAT_WS("",p_area_province,p_area_city,p_area) AS address,
        p.`id`,
        p.`p_id`,
        p.`p_name`,
        p.`p_phone_num`,
        p.`p_contract_star_time`,
        p.`p_contract_num`,
        p.`p_status`,
        c.`user_c_name`,
        p.`partner_type`
         FROM yy_partner_info p
         LEFT JOIN cms_area a ON p.`p_id`=a.`p_id`
         LEFT JOIN cms_user_project c ON p.`user_c_id`=c.id
         <where>
             <if test="cmsPartnerInfoListDto.pAreaProvinceNum !=null">
                and a.p_area_province_num = #{cmsPartnerInfoListDto.pAreaProvinceNum}
             </if>
             <if test="cmsPartnerInfoListDto.pAreaCityNum !=null">
                 and a.p_area_city_num = #{cmsPartnerInfoListDto.pAreaCityNum}
             </if>
             <if test="cmsPartnerInfoListDto.pAreaNum !=null">
                 and a.p_area_num = #{cmsPartnerInfoListDto.pAreaNum}
             </if>
             <if test="cmsPartnerInfoListDto.userCid !=null and cmsPartnerInfoListDto.userCid!=''">
                 and p.user_c_id = #{cmsPartnerInfoListDto.userCid}
             </if>
             <if test="cmsPartnerInfoListDto.startTime !=null and cmsPartnerInfoListDto.startTime!=''">
                 and p.p_contract_star_time &gt;=CONCAT(#{cmsPartnerInfoListDto.startTime},' 00:00:00')
             </if>
             <if test="cmsPartnerInfoListDto.sendTime !=null and cmsPartnerInfoListDto.sendTime!=''">
                 and p.p_contract_star_time &lt;=CONCAT(#{cmsPartnerInfoListDto.sendTime},' 23:59:59')
             </if>
             <if test="cmsPartnerInfoListDto.pStatus !=null">
                 and p.p_status =#{cmsPartnerInfoListDto.pStatus}
             </if>
             <if test="cmsPartnerInfoListDto.contractNum !=null and cmsPartnerInfoListDto.contractNum!=''">
                 and p.p_contract_num like CONCAT('%',#{cmsPartnerInfoListDto.contractNum},'%')
             </if>
             <if test="cmsPartnerInfoListDto.like !=null and cmsPartnerInfoListDto.like!=''">
                 and (p.p_phone_num  like CONCAT('%',#{cmsPartnerInfoListDto.like},'%')
                 or p.p_name like CONCAT('%',#{cmsPartnerInfoListDto.like},'%')
                 )
             </if>
             <if test="cmsPartnerInfoListDto.partnerType !=null">
                 and p.partner_type = #{cmsPartnerInfoListDto.partnerType}
             </if>
             and p.p_status !=5
         </where>
        order by p.create_time desc
    </select>
    <!--查询合伙人信息-->
    <select id="getCmsPartnerInfoByPid" resultType="com.jmt.modules.commission.model.vo.CmsPartnerInfoVo">
        SELECT * FROM yy_partner_info p
        LEFT JOIN
        cms_user_project up ON p.`user_c_id`=up.`id`
        where p.p_id=#{id}
    </select>
    <!--查询合伙人信息-->
    <select id="queryByPId" resultMap="CmsPartnerInfoMap">
        SELECT * FROM yy_partner_info  where p_id=#{pId}
    </select>
    <!--查询合伙人保证金-->
    <select id="queryDeposit" resultType="decimal">
        SELECT SUM(p_money) FROM cms_partner_guarantee
        WHERE p_code = #{pid} AND auditStatus = 1
    </select>
    <!--查询合伙人区域和设备信息-->
    <select id="queryPartnerInfoByPid" resultMap="cmsPartnerProfitInfoMap">
        SELECT p.p_id,p_name,p_recommender_code,p_eq_num,CONCAT_WS('-',p_area_province,p_area_city) AS address,(
            SELECT IFNULL(SUM(CASE m_eq_ststus WHEN 1 THEN 1 ELSE 0 END),0) FROM cms_eq WHERE m_code IN (
                SELECT m_code FROM cms_shop_info WHERE p_sub_code = p.`p_id`)
            ) AS onlineEq,
            (SELECT IFNULL(COUNT(*),0) FROM cms_eq WHERE m_code IN (
                SELECT m_code FROM cms_shop_info WHERE p_sub_code = p.`p_id`)
            )AS eqTotal,
            (SELECT IFNULL(COUNT(*),0) FROM cms_shop_info WHERE p_sub_code = p.`p_id`) AS shopNum
         FROM
         yy_partner_info p LEFT JOIN cms_area a ON p.`p_id`=a.`p_id`
          WHERE p.`p_id` = #{pid}
    </select>

    <!--搜索合伙人-->
    <select id="queryAllByPname" resultType="com.jmt.modules.commission.model.vo.CmsPartnerInfoListsVo">
        select p_id,p_name,p_phone_num from yy_partner_info where 1= 1
        <if test="pName != null and pName !=''">
            AND p_name like concat('%',#{pName},'%')
        </if>
        LIMIT 10
    </select>

    <select id="getCmsPartnerInfoByUserName" resultMap="CmsPartnerInfoMap">
        SELECT * FROM yy_partner_info  where  p_login_name = #{userName}
    </select>

    <select id="getCmsPartnerInfoById" resultMap="CmsPartnerInfoMap">
        SELECT * FROM yy_partner_info where id = #{id}
    </select>

    <select id="queryReferraByReferraId" resultType="com.jmt.modules.commission.model.vo.CmsPartnerReferralList">
         SELECT p_id,p_name,createTime FROM yy_partner_info WHERE p_recommender_code = #{pId}
    </select>

    <select id="queryCmsPartnerInfo" resultMap="CmsPartnerInfoMap">
        SELECT * FROM yy_partner_info WHERE p_id = (
        SELECT p.`p_code` FROM cms_shop_info s,cms_partner_sub p
        WHERE s.promoter_No = p.`p_sub_code` AND s.`m_code` = #{mCode}
    ) or p_id = (SELECT p_sub_code FROM cms_shop_info WHERE `m_code` = #{mCode})
    </select>

    <select id="queryCmsPartnerInfoByMCode" resultMap="CmsPartnerInfoMap">
    SELECT * FROM yy_partner_info WHERE p_id = (
        SELECT p_sub_code FROM cms_shop_info s
        WHERE  `m_code` = #{mCode}
    )
    </select>

    <select id="queryCmsPartnerBySubCode" resultMap="CmsPartnerInfoMap">
        SELECT * FROM yy_partner_info WHERE p_id = (
            SELECT p_code FROM cms_partner_sub WHERE p_sub_code = #{subCode}
        ) or p_id = #{subCode}
    </select>


    <select id="queryCmsPartnerBySubCodeOrPCode" resultMap="CmsPartnerInfoMap">
        SELECT * FROM yy_partner_info WHERE p_id = (
            SELECT p_code FROM cms_partner_sub WHERE p_sub_code = #{subCode}
        ) or p_id = #{pCode}
    </select>


    <select id="listRegister" resultType="com.jmt.modules.commission.model.vo.RegisterPantnerVo">
        select
          t1.id,t1.p_id,t1.p_phone_num,t1.p_status,t1.createTime,t2.p_name,t1.p_recommender_code
        from yy_partner_info t1 left join yy_partner_info t2 on t1.p_recommender_code = t2.p_id where t1.p_is_register = 1 and t1.p_status = 5
        <if test="param.phone!=null and param.phone!=''">
            and t1.p_phone_num = #{param.phone}
        </if>
        <if test="param.startTime !=null and param.startTime!=''">
            and t1.createTime &gt;=CONCAT(#{param.startTime},' 00:00:00')
        </if>
        <if test="param.sendTime !=null and param.sendTime!=''">
            and t1.createTime &lt;=CONCAT(#{param.sendTime},' 23:59:59')
        </if>
    </select>

    <select id="listRecommendPeople" resultType="com.jmt.modules.commission.model.vo.RecommendPeopleVo">
        select p_id,p_name,p_contacts_name,p_sex,p_phone_num,createTime from yy_partner_info where p_recommender_code = #{param.pId}
    </select>

    <select id="queryByPhone" resultMap="CmsPartnerInfoMap">
        select * from yy_partner_info where p_phone_num = #{mobile};
    </select>

    <select id="getCmsPartnerInfoByUaaId" resultMap="CmsPartnerInfoMap">
        select * from yy_partner_info where uaa_id = #{currentUserId};
    </select>
    <select id="queryOperator" resultMap="operatorMap">
        select id,p_id,p_contacts_name,p_profit,p_withdraw_money from yy_partner_info where partner_type = #{partnerType};
    </select>
</mapper>
