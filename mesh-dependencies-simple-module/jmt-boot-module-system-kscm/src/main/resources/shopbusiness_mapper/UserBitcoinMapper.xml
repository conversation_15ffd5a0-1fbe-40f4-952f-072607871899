<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.modules.shopbusiness.mapper.UserBitcoinMapper">

    <select id="getList" resultType="com.jmt.modules.shopbusiness.entity.UserBitcoinzRcord">
        select * from user_bitcoinz_record
        <where>
            <if test="type != null">
                and record_type = #{type}
            </if>
            <if test="like != null">
                and (user_phone like #{like} or record_remark like  #{like} or order_number like  #{like})
            </if>
        </where>
        order by id desc
    </select>


</mapper>