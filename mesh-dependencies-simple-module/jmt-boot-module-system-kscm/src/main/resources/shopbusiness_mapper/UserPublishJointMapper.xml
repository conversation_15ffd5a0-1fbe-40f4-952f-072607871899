<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.modules.shopbusiness.mapper.UserPublishJointMapper">

    <select id="queryValidateMessage" resultType="com.jmt.modules.shopbusiness.entity.UserPublishJoint">
        select * from user_publishjoint WHERE status>0 order by id desc
    </select>

    <select id="queryByUserIdAndRoost" resultType="com.jmt.modules.shopbusiness.entity.UserPublishJoint">
        SELECT * FROM user_publishjoint WHERE UserId = #{userId} and Status = 1 and payType = 0 and commission = 0
    </select>

    <update id="updateStatusById">
        UPDATE user_publishjoint SET commission = 1 WHERE Id = #{id}
    </update>

    <select id="getJointCount" resultType="java.lang.Integer">
        SELECT IFNULL(count(*),0) FROM user_publishjoint
    </select>

    <select id="getList" resultType="com.jmt.modules.shopbusiness.entity.UserPublishJoint">
        select * from user_publishjoint WHERE status=#{status} order by id desc
    </select>

    <select id="getStatusCount" resultType="java.lang.Integer">
        select count(*) from user_publishjoint where Status=#{status}
    </select>
</mapper>