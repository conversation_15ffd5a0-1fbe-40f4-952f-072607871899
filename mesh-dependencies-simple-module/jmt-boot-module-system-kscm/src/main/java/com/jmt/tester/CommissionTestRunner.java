package com.jmt.tester;


import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jmt.common.api.vo.Result;
import com.jmt.modules.castingapplet.service.CmsShopInfoDetaiService;
import com.jmt.modules.client.platform.PlatformClient;
import com.jmt.modules.commission.mapper.CmsWorkorderMapper;
import com.jmt.modules.commission.entity.CmsShopInfo;
import com.jmt.modules.commission.entity.NideshopChargeOrder;
import com.jmt.modules.commission.mapper.*;
import com.jmt.modules.commission.model.dto.CmsEqAppListDto;
import com.jmt.modules.commission.model.dto.CmsOrderRevenueAppListDto;
import com.jmt.modules.commission.model.dto.CmsShopInfoListAppDto;
import com.jmt.modules.commission.model.vo.*;
import com.jmt.modules.commissionapi.service.CmsOrderRevenueAppService;
import com.jmt.modules.commissionapi.service.CmsPartnerInfoAppService;
import com.jmt.modules.commissionapi.service.CmsPartnerSubService;
import com.jmt.modules.commissionapi.service.OrderTimingTaskService;
import com.jmt.modules.commission.mapper.CmsShopInfoMapper;
import com.jmt.modules.commissionapi.service.CmsShopInfoAppService;
import com.jmt.modules.commissionapi.service.impl.CmsWorkorderAppServiceImpl;
import com.jmt.modules.devicemanage.service.ShopInfo2ServiceImpl;
import com.jmt.modules.devicemanage.service.ShopInfoAppServiceImpl;
import com.jmt.modules.devicemanage.vto.ShopAuditListVO;
import com.jmt.modules.devicemanage.vto.ShopInfoListAppDto;
import com.jmt.modules.devicemanage.vto.ShopInfoListAppVo;
import com.jmt.modules.devicemanage.vto.ShopInfoListDto;
import com.jmt.modules.devicemanage.vto.ShopInfoListVo;
import com.jmt.modules.operateteam.service.PartnerInfoServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

//暂时用作单元测试
@Component
@Slf4j
public class CommissionTestRunner  {

    @Resource
    private CmsEqMapper cmsEqMapper;
    @Resource
    private CmsEqInfoMapper cmsEqInfoMapper;
    @Resource
    private CmsWorkorderAppServiceImpl workorderAppService;
    @Resource
    private CmsWorkorderMapper workorderMapper;
    @Resource
    private CmsOrderRevenueAppService cmsOrderRevenueAppServiceImpl;
    @Resource
    private ShopInfoAppServiceImpl shopInfoAppService;
    @Resource
    private CmsPartnerInfoAppService partnerInfoAppService;

    @Resource
    private PartnerInfoServiceImpl partnerInfoService;
    @Resource
    private ShopInfo2ServiceImpl shopInfo2Service;

    public void test() {

        try {

        }catch (Exception err){
            log.error(err.getMessage());
        }

    }


    private void delInvestor() throws Exception {
        String msg=this.shopInfo2Service.delInvestor(2249);
        log.info(msg);
    }

    private void setInvestor() throws Exception {
        String msg=this.shopInfo2Service.setInvestor(227,"13900000001");
        log.info(msg);
    }

    private void testListShopInfo()  {
        ShopInfoListDto cmsShopInfoListDto = new ShopInfoListDto();
        Result<Page<ShopInfoListVo>> res=this.shopInfo2Service.listShopInfo(cmsShopInfoListDto);
        log.info(res.toString());
    }

    private void testTransferTo() throws Exception {
        String msg= this.partnerInfoService.transferTo(1242270548,"15980702311");
        log.info(msg);
    }



    private void testStatIncomeByDate(){
        List<IncomeListVo>  list= this.partnerInfoAppService.statIncomeGroupbyDate(1243342914,1);
        System.out.println(list.toString());
    }
    private void testHomePage() {

        Result<PartnerHomePageAppVo>  result =this.partnerInfoAppService.getPartnerHomePage(1245544303);
        System.out.println(result.toString());
    }

    private  void testWorkOrderDevideIncome(){
        String orderId="WO1715592258";
        Result<Object> res= workorderAppService.divCommission(orderId);
        System.out.println(res.toString());
    }

    private void testPageMine() {
        Map map= new HashMap(); map.put("pId","1243342914");
        Result<CmsHomePartnerInfoAppVo>  result =this.partnerInfoAppService.getPartnerMine(map);
        System.out.println(result.toString());
    }


    private void testListshop() {
        ShopInfoListAppDto dto= new ShopInfoListAppDto();
        dto.setPId(1245544303);
        Result<Page<ShopInfoListAppVo>> list =this.shopInfoAppService.listShopInfo(dto);
        System.out.println(list.toString());
    }

    private  void testQueryCityEq(){
        Date dd = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(dd);
        calendar.add(Calendar.DAY_OF_MONTH, -2);
        Integer offline = cmsEqMapper.queryCityEq(1243342914, 1, calendar.getTime());
        System.out.println(offline.toString());

    }

    private void testEqAppListDto() {
        CmsEqAppListDto cmsEqAppListDto= new CmsEqAppListDto();
        cmsEqAppListDto.setPid(1243342914);
        Page<CmsEqAppListVo> page = new Page<>(cmsEqAppListDto.getPageNo(), cmsEqAppListDto.getPageSize());
        List<CmsEqAppListVo> listVoList = cmsEqMapper.queryAllByLike2(page, cmsEqAppListDto);
        System.out.println(listVoList.toString());
    }

    private void testQueryAreaByPCode(){
        Integer areaCmsEqInfo = cmsEqInfoMapper.queryAreaByPCode(1243342914);
        System.out.println(areaCmsEqInfo.toString());
    }

    private  void testLoadWorkOrderEqInfo(){
        String orderId="WO1715592258"; String mCode ="M106707";
        List<CmsShopEqListAppVo> eqList=workorderAppService.loadWorkOrderEqInfo(orderId,mCode);
        System.out.println(eqList.toString());
    }

    private  void testCommission(){
        Result<Object> res =workorderAppService.divCommission("WO1715592258");
        System.out.println(res.toString());
    }

    private  void testWorkOrderCount(){
        Integer count=workorderMapper.getOrderCountBySubCode("2","1244557222");
        count=workorderMapper.getOrderCountByPid("2","1243342914");
        System.out.println(count.toString());
    }

    private  void testOrdernv(){
        CmsOrderRevenueAppListDto cmsOrderRevenueAppListDto= new CmsOrderRevenueAppListDto();
        cmsOrderRevenueAppListDto.setPageNo(1);cmsOrderRevenueAppListDto.setPageSize(10);cmsOrderRevenueAppListDto.setPid(1243342914);
        cmsOrderRevenueAppListDto.setDate("2024-05"); cmsOrderRevenueAppListDto.setProjectName("部署收益");
        Result<Map<String, Object>> res=cmsOrderRevenueAppServiceImpl.listOrderRevenue(cmsOrderRevenueAppListDto);

        System.out.println(res.toString());
    }
}
