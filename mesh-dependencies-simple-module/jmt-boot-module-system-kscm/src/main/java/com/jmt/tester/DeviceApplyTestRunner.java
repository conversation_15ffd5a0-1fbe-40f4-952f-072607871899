package com.jmt.tester;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jmt.modules.devicemanage.entity.ShopInfo;
import com.jmt.modules.devicemanage.mapper.WorkOrderMapper;
import com.jmt.modules.devicemanage.service.DeviceSiteApplyServiceImpl;
import com.jmt.modules.devicemanage.service.ShopInfoAppServiceImpl;
import com.jmt.modules.devicemanage.service.WorkOrderAppServiceImpl;
import com.jmt.modules.devicemanage.vto.*;
import com.jmt.modules.shopbusiness.service.UserProfileServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

//暂时用作单元测试
@Component
@Slf4j
public class DeviceApplyTestRunner {
    @Resource
    private DeviceSiteApplyServiceImpl applyService;

    @Resource
    private WorkOrderAppServiceImpl workOrderAppService;
    @Resource
    private WorkOrderMapper workOrderMapper;
    @Resource
    private UserProfileServiceImpl userProfileService;
    @Resource
    private ShopInfoAppServiceImpl shopInfoAppService;

    public void test() {
        try {
            queryByPartner();
        }
        catch (Exception err){
            log.error(err.getMessage());
        }
    }

    private void queryByPartner(){
        ShopInfoQueryPageCond cond = new ShopInfoQueryPageCond();
        cond.setPCode(1243342914);
        cond.setSubCode(0);
        cond.setAreaCode(0);
        Page<ShopInfoListAppVo> page = this.shopInfoAppService.queryByPartner(cond);
        log.info(page.toString());
    }

    private void setShopInfoStarLevel() throws Exception{
        ShopInfoSetStarLevelTO dto = new ShopInfoSetStarLevelTO();
        dto.setShopId(224); dto.setStarLevel(3);
        this.shopInfoAppService.setShopInfoStarLevel(dto);
    }
    private void testApplySave(){
        userProfileService.SetMockPhone("15980702367");
        ShopInfo apply= new ShopInfo();
        apply.setMCuisine("特色小吃");
        apply.setMMeasure(69.0);
        apply.setMTraffic(100);
        apply.setMConsume("60.0");
        apply.setMachineA(10);
        apply.setMAmountDeskF(10);
        applyService.applyDeviceSave(apply,true);
        System.out.println(apply.toString());

    }
    private void testDeviceApplyCheck() throws Exception {
        DeviceApplyCheckDto  dto = new DeviceApplyCheckDto();
        dto.setId(2306L);
        dto.setAction(2);
        dto.setDeployerCode(1241262020);
        applyService.oparatorCheck(dto);
    }

    private void testWorkOrderCheck() throws Exception {
        DelpoyInfo dto = new DelpoyInfo();
        dto.setWorkOrderId(373L);
        dto.setAction(1);
        workOrderAppService.commitDelpoyInfo(dto);
    }

    private  void testWorkOrderList(){
        WorkOrderListAppDto dao = new WorkOrderListAppDto();
        dao.setPId(1241262020);
        Page<WorkOrderListAppVo> page=new Page<>(dao.getPageNo(),dao.getPageSize());
        List<WorkOrderListAppVo> res=this.workOrderMapper.queryAllByPartnerSubIdApp(page,dao);
        System.out.println(res.toString());
    }

}
