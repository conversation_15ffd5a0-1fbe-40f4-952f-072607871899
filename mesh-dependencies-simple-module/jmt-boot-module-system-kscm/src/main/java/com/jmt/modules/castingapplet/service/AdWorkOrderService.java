package com.jmt.modules.castingapplet.service;

import com.jmt.modules.castingapplet.entity.AdvertisingOrder;
import com.jmt.modules.castingapplet.entity.AdvertisingOrderDetailed;
import com.jmt.modules.castingapplet.entity.UserVo;
import com.jmt.modules.commission.entity.CmsFile;
import com.jmt.modules.commission.entity.CmsShopInfo;
import com.jmt.modules.commission.model.vo.CmsEqNum;
import java.util.List;

public interface AdWorkOrderService {
    boolean saveWorkOrder(AdvertisingOrder orderInfo, UserVo queryById, List<CmsEqNum> cmsEqNum, CmsFile cmsFile);

    boolean saveWorkOrderArea(AdvertisingOrderDetailed detais, List<CmsEqNum> eqNum, CmsShopInfo cmsShopInfo);

    boolean updateWorkOrder(AdvertisingOrder advertisingOrder);

    void query();
}
