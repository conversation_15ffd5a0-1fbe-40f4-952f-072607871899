package com.jmt.modules.commission.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jmt.common.api.vo.Result;
import com.jmt.modules.commission.entity.CmsEqApplyOrder;
import com.jmt.modules.commission.model.dto.CmsEqApplyOrderListDto;
import com.jmt.modules.commission.model.vo.CmsEqApplyOrderInfoVo;
import com.jmt.modules.commission.model.vo.CmsEqApplyOrderListVo;

/**
 *  @author: hanshangrong
 *  @Date: 2020/4/20 0020 15:31
 *  @Description:类注释
 *  设备申请订单业务接口
 */
public interface CmsEqApplyOrderService {

    /**
     * 描述:  设备申请订单列表
     * @method  listEqApplyOrder
     * @date: 2020/4/20 0020
     * @author: hanshangrong
     * @param cmsEqApplyOrderListDto
     * @return com.jmt.common.api.vo.Result<com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.jmt.modules.commission.model.vo.CmsEqApplyOrderListVo>>
     */
    Result<Page<CmsEqApplyOrderListVo>> listEqApplyOrder(CmsEqApplyOrderListDto cmsEqApplyOrderListDto);

    /**
     * @param id: id
     * @return Result<Object>
     * <AUTHOR>
     * @description 删除设备申请订单
     * @date 2023/7/1 14:24
     */
    Result<Object> deleteCmsEqApplyOrder(Integer id);

    /**
     * 描述:  设备申请订单详情
     * @method  getApplyOrderInfo
     * @date: 2020/4/20 0020
     * @author: hanshangrong
     * @param orderId
     * @return com.jmt.common.api.vo.Result<com.jmt.modules.commission.model.vo.CmsEqApplyOrderInfoVo>
     */
    Result<CmsEqApplyOrderInfoVo> getApplyOrderInfo(String orderId);

    /**
     * 描述:  审核
     * @method  auditCmsEqApplyOrder
     * @date: 2020/4/20 0020
     * @author: hanshangrong
     * @param orderId
     * @param status
     * @param remarks
     * @return com.jmt.common.api.vo.Result<java.lang.Object>
     */
    Result<Object> auditCmsEqApplyOrder(String orderId, Integer status, String remarks);


    /**
     * 描述:  发货
     * @method  sendOutGoods
     * @date: 2020/4/21 0021
     * @author: hanshangrong
     * @param orderId
     * @return com.jmt.common.api.vo.Result<java.lang.Object>
     */
    Result<Object> sendOutGoods(String orderId,String logisticsNumber,String logisticsName);

    /***
     * 描述:  订单确认收货
     * @method: confirmReceipt
     * @author: HSR
     * @date: 2020/10/20
     * @param order
     * @return: void
     * @exception:
    **/
    void confirmReceipt(CmsEqApplyOrder order);
}
