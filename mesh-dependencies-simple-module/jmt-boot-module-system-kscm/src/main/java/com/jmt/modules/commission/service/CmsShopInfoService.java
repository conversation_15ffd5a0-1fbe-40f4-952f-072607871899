package com.jmt.modules.commission.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jmt.common.api.vo.Result;
import com.jmt.modules.commission.entity.CmsShopInfo;
import com.jmt.modules.commission.model.dto.CmsShopInfoListDto;
import com.jmt.modules.commission.model.dto.ShopInfoDto;
import com.jmt.modules.commission.model.dto.UpdateCmsShopDto;
import com.jmt.modules.commission.model.vo.CmsShopInfoListVo;
import com.jmt.modules.commission.model.vo.CmsShopInfoVo;
import com.jmt.modules.commission.model.vo.ShopInfoListVo;
import com.jmt.modules.commission.model.vo.ShopInfoVo;

import java.util.List;

/**
 *  @author: hanshangrong
 *  @Date: 2020/4/24 0024 11:41
 *  @Description:类注释
 *  商户接口
 */
public interface CmsShopInfoService extends IService<CmsShopInfo> {

    /**
     * 描述:  商户列表
     * @method  listShopInfo
     * @date: 2020/4/24 0024
     * @author: hanshangrong
     * @param cmsShopInfoListDto
     * @return com.jmt.common.api.vo.Result<com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.jmt.modules.commission.model.vo.CmsShopInfoListVo>>
     */
    Result<Page<CmsShopInfoListVo>> listShopInfo(CmsShopInfoListDto cmsShopInfoListDto);

    /**
     * @param id: 商户id
     * @return void
     * <AUTHOR>
     * @description 删除
     * @date 2023/6/30 15:31
     */
    void deleteShopInfo(Integer id);

    Result<List<CmsShopInfo>> listShopInfo2();

    /**
     * 描述:  分配商户
     * @method  distributionShopInfo
     * @date: 2020/4/24 0024
     * @author: hanshangrong
     * @param pId
     * @param mCode
     * @return com.jmt.common.api.vo.Result<java.lang.Object>
     */
    Result<Object> distributionShopInfo(String mCode,Integer pId);

    /**
     * 描述:  商户详情
     * @method  getShopInfo
     * @date: 2020/4/25 0025
     * @author: hanshangrong
     * @param mCode
     * @return com.jmt.common.api.vo.Result<com.jmt.modules.commission.model.vo.CmsShopInfoVo>
     */
    Result<CmsShopInfoVo> getShopInfo(String mCode);

    /**
     * 描述:  同步商户详情到广告机
     * @method  aSynShopInfo
     * @date: 2020/5/8 0008
     * @author: hanshangrong
     * @param param
     * @return com.jmt.common.api.vo.Result<java.lang.Object>
     */
    Result<Object> aSynShopInfo(JSONObject param);

    /**
     * 描述:  修改商户信息
     * @method  updateShop
     * @date: 2020/5/16 0016
     * @author: hanshangrong
     * @param updateCmsShopDto
     * @return com.jmt.common.api.vo.Result<java.lang.Object>
     */
    Result<Object> updateShop(UpdateCmsShopDto updateCmsShopDto);


    /**
     * 描述:  获取商户详情
     * @method: queryShopInfo
     * @author: HSR
     * @date: 2020/9/30
     * @param param
     * @return: com.jmt.modules.shopbusiness.entity.CmsShopInfo
     * @exception:
    **/
    CmsShopInfo queryShopInfo(JSONObject param);


    /**
     * 描述:  商户列表
     * @method: listShop
     * @author: HSR
     * @date: 2020/9/30
     * @param shopInfoDto
     * @return: com.jmt.common.api.vo.Result<com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.jmt.modules.commission.model.vo.ShopInfoListVo>>
     * @exception:
    **/
    Result<Page<ShopInfoListVo>> listShop(ShopInfoDto shopInfoDto);

    /**
     * 描述:  获取商户信息
     * @method: getShopInfos
     * @author: HSR
     * @date: 2020/9/30
     * @param param
     * @return: com.jmt.common.api.vo.Result<com.jmt.modules.commission.model.vo.ShopInfoVo>
     * @exception:
    **/
    Result<ShopInfoVo> getShopInfos(JSONObject param);

    /**
     * 描述:  修改商户信息
     * @method: modifyPoint
     * @author: HSR
     * @date: 2020/9/30
     * @param shopInfoVo
     * @return: com.jmt.common.api.vo.Result<java.lang.Object>
     * @exception:
    **/
    Result<Object> modifyPoint(ShopInfoVo shopInfoVo);

    /**
     * 描述:  通过编号查询商户信息
     * @method: queryShopByMcode
     * @author: HSR
     * @date: 2020/9/30
     * @param merchantCode 商户编号
     * @return: com.jmt.modules.shopbusiness.entity.CmsShopInfo
     * @exception:
    **/
    CmsShopInfo queryShopByMcode(String merchantCode);

    /**
     *  根据设备号查询商户信息
     * @param mcode
     * @return
     */
    CmsShopInfo queryShopByEqCode(String mcode);

    /***
     * 描述:  通过商户编号查询用户信息
     * @method: getShopInfoByEqCode
     * @author: HSR
     * @date: 2020/11/3
     * @param eqCode
     * @return: com.jmt.modules.shopbusiness.entity.CmsShopInfo
     * @exception:
    **/
    CmsShopInfo getShopInfoByEqCode(String eqCode);

    /***
     * 描述: 修改商户充电积分
     * @method: updateIntegralByLevel
     * @author: HSR
     * @date: 2020/11/19
     * @param lowestValue
     * @param starRating
     * @return: void
     * @exception:
    **/
    void updateIntegralByLevel(Integer lowestValue, Integer starRating);
}
