package com.jmt.modules.commission.model.dto;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;
/**
 *  @author: hanshangrong
 *  @Date: 2020/4/28 0028 15:11
 *  @Description:类注释
 *  新增合伙人迁移
 */
@Data
public class AddCmsTransferLogAppDto {
    /**
     * 迁出人编号
     */
    @JsonProperty("removeCode")
    @NotBlank(message = "迁出人编号为空")
    private String removeCode;
    /**
     * 迁出人名字
     */
    @JsonProperty("removeName")
    @NotBlank(message = "迁出人名字为空")
    private String removeName;
    /**
     * 迁入人编号
     */
    @JsonProperty("increaseCode")
    @NotBlank(message = "迁入人编号为空")
    private String increaseCode;
    /**
     * 备注
     */
    @JsonProperty("remarks")
    @NotBlank(message = "备注为空")
    private String remarks;

}
