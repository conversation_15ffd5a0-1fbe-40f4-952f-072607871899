package com.jmt.modules.commission.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.jmt.modules.commission.entity.CmsPushmsg;
import java.util.Date;

public class CmsPushmsgListAppVo{


    @JsonProperty("id")
    private Integer id;
    /**
     * 内容
     */
    @JsonProperty("content")
    private String content;
    /**
     * 标题
     */
    @JsonProperty("title")
    private String title;
    /**
     * 推送对象
     */
    @JsonProperty("pushtarg")
    private Integer pushtarg;
    /**
     * 推送类型 0、单人 1、全体
     */
    @JsonProperty("pushType")
    private Integer pushType;
    /**
     * 创建时间
     */
    @JsonProperty("createTime")
    private Date createTime;

    private Integer userId;

    private boolean isReadMessage = true;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getPushtarg() {
        return pushtarg;
    }

    public void setPushtarg(Integer pushtarg) {
        this.pushtarg = pushtarg;
    }

    public Integer getPushType() {
        return pushType;
    }

    public void setPushType(Integer pushType) {
        this.pushType = pushType;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
        if(userId == null){
            this.isReadMessage = false;
        }
    }

    public boolean isReadMessage() {
        return isReadMessage;
    }

    public void setReadMessage(boolean readMessage) {
        isReadMessage = readMessage;
    }
}
