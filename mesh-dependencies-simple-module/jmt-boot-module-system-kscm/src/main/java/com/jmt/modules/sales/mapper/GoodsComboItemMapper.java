package com.jmt.modules.sales.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jmt.modules.sales.entity.GoodsComboItem;
import com.jmt.modules.sales.vto.GoodsComboItemVO;

import java.util.List;

/**
 * 普通餐厅(Business)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-09-15 13:55:45
 */
public interface GoodsComboItemMapper extends BaseMapper<GoodsComboItem> {
    List<GoodsComboItemVO> queryComboItems(Long comboGoodsId);
    void clearComboItems(Long comboGoodsId);
}