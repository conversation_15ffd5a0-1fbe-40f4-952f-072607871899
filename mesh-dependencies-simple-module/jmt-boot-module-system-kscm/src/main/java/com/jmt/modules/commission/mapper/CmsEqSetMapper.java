package com.jmt.modules.commission.mapper;

import org.apache.ibatis.annotations.Param;
import com.jmt.modules.commission.entity.CmsEqSet;

import java.util.List;

/**
 * 商户设备设置表(CmsEqSet)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-04-27 11:08:34
 */
public interface CmsEqSetMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    CmsEqSet queryById(Integer id);

    /**
     * 通过mCode
     *
     * @param mCode
     * @return 实例对象
     */
    CmsEqSet queryByMCode(String mCode);

    /**
     * 通过实体作为筛选条件查询
     *
     * @param cmsEqSet 实例对象
     * @return 对象列表
     */
    List<CmsEqSet> queryAll(CmsEqSet cmsEqSet);

    /**
     * 新增数据
     *
     * @param cmsEqSet 实例对象
     * @return 影响行数
     */
    int insert(CmsEqSet cmsEqSet);

    /**
     * 修改数据
     *
     * @param cmsEqSet 实例对象
     * @return 影响行数
     */
    int update(CmsEqSet cmsEqSet);


    /**
     * 修改数据
     *
     * @param cmsEqSet 实例对象
     * @return 影响行数
     */
    int updateByMcode(CmsEqSet cmsEqSet);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Integer id);

}