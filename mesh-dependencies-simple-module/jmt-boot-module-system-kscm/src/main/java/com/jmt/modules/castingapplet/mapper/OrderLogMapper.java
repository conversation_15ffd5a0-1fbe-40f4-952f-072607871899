package com.jmt.modules.castingapplet.mapper;

import com.jmt.modules.castingapplet.entity.OrderLog;
import java.util.List;

/**
 * (OrderLog)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-05-22 09:46:13
 */
public interface OrderLogMapper {
    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    OrderLog queryById(Integer id);


    /**
     * 通过实体作为筛选条件查询
     *
     * @param orderLog 实例对象
     * @return 对象列表
     */
    List<OrderLog> queryAll(OrderLog orderLog);

    /**
     * 新增数据
     *
     * @param orderLog 实例对象
     * @return 影响行数
     */
    int insert(OrderLog orderLog);

    /**
     * 修改数据
     *
     * @param orderLog 实例对象
     * @return 影响行数
     */
    int update(OrderLog orderLog);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Integer id);

}