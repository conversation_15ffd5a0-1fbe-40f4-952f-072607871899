package com.jmt.modules.commission.controller;


import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.jmt.common.api.vo.Result;
import com.jmt.modules.commission.mapper.CmsShopEqMapper;
import com.jmt.modules.commission.mapper.CmsWorkorderMapper;
import com.jmt.modules.shopbusiness.entity.Business;
import com.jmt.modules.commission.mapper.*;
import com.jmt.modules.commission.model.vo.BigViewPramaVO;
import com.jmt.modules.commission.model.vo.EqAreaVO;
import com.jmt.modules.commission.model.vo.EqStatusChangeVO;
import com.jmt.modules.commission.model.vo.PartnerYearMonthVO;
import com.jmt.modules.commission.service.*;
import com.jmt.modules.commission.mapper.CmsShopInfoMapper;
import com.jmt.modules.shopbusiness.service.BusinessServiceImpl;
import com.jmt.modules.shopbusiness.service.UserPublishJointServiceImpl;
import com.jmt.modules.shopbusiness.service.UserPublishMessageServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import java.util.List;

@RestController
@Slf4j
@RequestMapping("public/bigView")
public class BigViewController {

    @Resource
    private CmsShopEqMapper cmsShopEqMapper;
    @Resource
    private UserAccountService userAccountService;
    @Resource
    private BusinessServiceImpl businessService;
    @Resource
    private UserPublishMessageServiceImpl userPublishMessageService;
    @Resource
    private UserPublishJointServiceImpl userPublishJointService;
    @Resource
    private CmsWorkorderMapper cmsWorkorderMapper;
    @Resource
    private CmsPartnerInfoMapper cmsPartnerInfoMapper;
    @Resource
    private LiliShopStatService  liliShopStatService;
    @Resource
    private CmsEqMapper   cmsEqMapper;
    @Resource
    private PlantChargeOrderService   plantChargeOrderService;
    @Resource
    CmsShopInfoMapper cmsShopInfoMapper;

    @RequestMapping(value = "/getData", method = RequestMethod.GET)
    public Result getData(BigViewPramaVO vo) {
        log.info("=====进入大屏取数：getData");
        JSONObject resultObj = new JSONObject();

        // 设备分布图
        if (StrUtil.isEmpty(vo.getProName())){
            List<EqAreaVO> eqList= cmsShopEqMapper.getByPro();
            resultObj.put("eqArea",eqList);
        } else {
            List<EqAreaVO> eqList= cmsShopEqMapper.getByCity(vo.getProName());
            resultObj.put("eqArea",eqList);
        }

        //设备总览
        JSONObject json=getEqInfoJson();
        resultObj.put("devNav",json);

        //聚合伙
        List<PartnerYearMonthVO> partnerYearMonthVOList= cmsPartnerInfoMapper.statPartnerByYearMonth();
        resultObj.put("partnerInfo",partnerYearMonthVOList);

        //设备提醒
        List<EqStatusChangeVO>  statusChangeVOlist= this.cmsEqMapper.queryDeviceStausChange();
        resultObj.put("deviceStatusChange",statusChangeVOlist);

        //筷圣补电
//        java.util.Map<String,Integer> countyChargeMap= getCountyCharge();
//        resultObj.put("countyCharge",countyChargeMap);

        //筷圣投广
        Integer eqSum= cmsShopEqMapper.getEqSum();
        Integer installedEqSum= cmsShopEqMapper.getInstalledEqSum();
        JSONObject eqObj = new JSONObject();
        eqObj.put("eqSum",eqSum);
        eqObj.put("installedEqSum",installedEqSum);
        resultObj.put("eqSumInfo",eqObj);

        //筷圣名品
        JSONObject famousGoodsInfo= getFamousGoodsInfo();
        resultObj.put("famousGoods",famousGoodsInfo);

       return  Result.ok(resultObj);
    }

    private JSONObject getEqInfoJson(){
        int userSum = userAccountService.getAllNumer();
        int shopSum= 0;
        List<Business> list=businessService.queryAll(new Business());
        if (list !=null ) shopSum = list.size();
        int msgSum=userPublishMessageService.getMsgCount();
        int jointSum= userPublishJointService.getJointCount();
        int orderSum= cmsWorkorderMapper.getOrderCount();
        JSONObject json= new JSONObject();
        json.put("userSum",userSum);
        json.put("shopSum",shopSum);
        json.put("msgSum",msgSum);
        json.put("jointSum",jointSum);
        json.put("orderSum",orderSum);
        return  json;
    }

    private java.util.Map<String,Integer> getCountyCharge(){
        java.util.Map<String,Integer> countyChargeMap = cmsShopInfoMapper.getCountyMap();
        java.util.Map<String,String> eqCountyMap = cmsShopEqMapper.getEqCounty();
        java.util.Map<String,Integer> eqChargeMap =plantChargeOrderService.statEqChargeOrder();

        for (java.util.Map.Entry<String, Integer> entry : countyChargeMap.entrySet()) {
            String eqCode=entry.getKey();
            Integer chargeTime= entry.getValue();
            String county= eqCountyMap.get(eqCode);
            Integer allCharge= countyChargeMap.get(county);
            countyChargeMap.put(county,allCharge+chargeTime);
        }

        return countyChargeMap;
    }

    private  JSONObject getFamousGoodsInfo(){
        Integer tradeSum= liliShopStatService.getTradeSum();
        Integer memberCount= liliShopStatService.getMemberCount();
        Integer registerCount= liliShopStatService.getRegisterCount();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("tradeSum",tradeSum);
        jsonObject.put("memberCount",memberCount);
        jsonObject.put("registerCount",registerCount);
        return  jsonObject;
    }
}
