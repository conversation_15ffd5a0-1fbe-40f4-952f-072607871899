package com.jmt.modules.shopbusiness.vto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 *   业务员各类用户查询条件
 */
@Data
public class SubCodeUserPageCond<T> extends com.jmt.modules.operateteam.vto.PageCond {
    /**
     * 状态: -1：全部，0：待审核；1:已审核；2:已驳回
     */
    @JsonProperty("status")
    private Integer status=0;

    /**
     * 所属业务员编号
     */
    @JsonProperty("subCode")
    private Integer subCode;

    /**
     * 用户名（商家名/商户名）
     */
    private String name;
    /**
     * 排序方式:0正序，1倒序
     */
    private Integer orderType=0;


    public void formatPhonelikeCluse(){
        if (this.name!=null&& !this.name.isEmpty())
            this.name= "%"+ this.name+"%";
    }
}