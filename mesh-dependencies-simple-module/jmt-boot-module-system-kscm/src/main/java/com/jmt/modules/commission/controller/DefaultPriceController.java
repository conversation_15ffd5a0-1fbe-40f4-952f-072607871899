package com.jmt.modules.commission.controller;

import com.jmt.common.api.vo.Result;
import com.jmt.modules.commission.entity.DefaultPrice;
import com.jmt.modules.commission.service.DefaultPriceService;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;

@RestController
@RequestMapping("/defaultPrice")
public class DefaultPriceController {

    @Resource
    private DefaultPriceService defaultPriceServiceImpl;
    @RequestMapping(value = "getDefaultPrice",method = RequestMethod.POST)
    public Result<DefaultPrice> getDefaultPrice(){
        return defaultPriceServiceImpl.getDefaultPrice();
    }

    @RequestMapping(value = "updateDefaultPrice",method = RequestMethod.POST)
    public Result<Object> updateDefaultPrice(@RequestBody DefaultPrice defaultPrice){
        return defaultPriceServiceImpl.updateDefaultPrice(defaultPrice);
    }
}
