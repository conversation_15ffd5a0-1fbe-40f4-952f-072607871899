package com.jmt.modules.shopbusiness.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jmt.modules.shopbusiness.entity.Business;
import com.jmt.modules.shopbusiness.entity.UserCode;
import com.jmt.modules.shopbusiness.entity.UserProfile;
import com.jmt.modules.shopbusiness.entity.UserPublishJoint;
import com.jmt.modules.shopbusiness.vto.OperatorUserPageCond;
import com.jmt.modules.shopbusiness.vto.SubCodeUserPageCond;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 注册用户（游客）基本信表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-09-17 11:53:59
 */
public interface UserProfileMapper extends BaseMapper<UserProfile> {

    /**
     * 分页查询
     * @param page
     * @param keyword
     * @return
     */
    IPage<UserProfile> getList(IPage<UserProfile> page, String keyword);
    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    UserProfile queryById(Long id);

    /**
     * 按电话查询客户信息
     * @param phone
     * @return
     */
    UserProfile queryByPhone(String phone);

    /**
     * 按运营商分页查询用户
     * @param page
     * @param cond
     * @return
     */
    IPage<UserProfile> queryByOperator(IPage<UserProfile> page, OperatorUserPageCond cond);
    /**
     * 查询指定行数据
     *
     * @param offset 查询起始位置
     * @param limit 查询条数
     * @return 对象列表
     */
    List<UserProfile> queryAllByLimit(@Param("offset") int offset, @Param("limit") int limit);


    /**
     * 通过实体作为筛选条件查询
     *
     * @param userProfile 实例对象
     * @return 对象列表
     */
    List<UserProfile> queryAll(UserProfile userProfile);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Long id);

    /**
     * 统计各种角色用户总数
     * @param roleType
     * @param operatorCode
     * @return
     */
    int countByRoleType(Integer roleType,Integer operatorCode);

}