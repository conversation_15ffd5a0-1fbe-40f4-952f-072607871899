package com.jmt.modules.commission.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jmt.modules.commission.entity.CmsOrderRevenue;
import com.jmt.modules.commission.entity.CmsPartnerSub;
import com.jmt.modules.commission.entity.CmsSubProjectInfo;
import com.jmt.modules.commission.mapper.CmsOrderRevenueMapper;
import com.jmt.modules.commission.mapper.CmsPartnerSubMapper;
import com.jmt.modules.commission.mapper.CmsSubProjectInfoMapper;
import com.jmt.modules.commission.service.CmsSubProjectInfoService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.lang.model.type.IntersectionType;
import java.math.BigDecimal;
import java.util.List;

@Service
public class CmsSubProjectInfoServiceImpl extends ServiceImpl<CmsSubProjectInfoMapper, CmsSubProjectInfo> implements CmsSubProjectInfoService {

    @Resource
    private CmsSubProjectInfoMapper cmsSubProjectInfoMapper;

    @Resource
    private CmsOrderRevenueMapper cmsOrderRevenueMapper;

    @Resource
    private CmsPartnerSubMapper cmsPartnerSubMapper;

    @Override
    public List<CmsOrderRevenue> listProject(int subId, String revenueItems, int row, int page) {
        List<CmsOrderRevenue> cmsOrderRevenues = cmsOrderRevenueMapper.queryAllByRefereeCode(subId, revenueItems, row *  (page - 1), row);

       return cmsOrderRevenues;
    }

    @Override
    public BigDecimal totalProject(int subId) {
        BigDecimal layingIncome = cmsOrderRevenueMapper.queryBySum(subId, "部署收益");
        BigDecimal deploymentIncome = cmsOrderRevenueMapper.queryBySum(subId, "推广收益");
        BigDecimal informationIncome = cmsOrderRevenueMapper.queryBySum(subId, "卖光光发布收益");
        BigDecimal informationIncome2 = cmsOrderRevenueMapper.queryBySum(subId, "农餐对接发布收益");

        BigDecimal result = layingIncome.add(deploymentIncome).add(informationIncome).add(informationIncome2);

        return result;
    }
}
