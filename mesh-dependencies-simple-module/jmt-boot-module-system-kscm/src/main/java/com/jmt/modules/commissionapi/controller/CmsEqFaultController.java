package com.jmt.modules.commissionapi.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.jmt.common.api.vo.Result;
import com.jmt.modules.commission.entity.CmsEqFault;
import com.jmt.modules.commission.model.dto.AddCmsEqFaultDto;
import com.jmt.modules.commission.model.dto.CmsEqFaultAppListDto;
import com.jmt.modules.commission.model.vo.CmsEqFaultInfoAppVo;
import com.jmt.modules.commissionapi.service.CmsEqFaultService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

@RestController
@CrossOrigin
@RequestMapping("/app/cmsEqFault")
@Api("设备维护")
public class CmsEqFaultController {
    @Resource
    private CmsEqFaultService cmsEqFaultServiceImpl;

    @ApiOperation("设备维护列表")
    @RequestMapping(value = "/listCmsEqFault",method = RequestMethod.POST)
    public Result<Page<CmsEqFault>> listCmsEqFault(@RequestBody CmsEqFaultAppListDto cmsEqFaultAppListDto){
        return cmsEqFaultServiceImpl.listCmsEqFault(cmsEqFaultAppListDto);
    }


    @ApiOperation("设备维护详情")
    @RequestMapping(value = "/getCmsEqFaultInfo",method = RequestMethod.POST)
    public Result<CmsEqFaultInfoAppVo> getCmsEqFaultInfo(@RequestBody Map<String,String> map){
        return cmsEqFaultServiceImpl.getEqFaultInfo(map);
    }

    @ApiOperation("设备维护状态变更")
    @RequestMapping(value = "/stateChange",method = RequestMethod.POST)
    public Result<Object> stateChange(@RequestBody Map<String,String> map){
        return cmsEqFaultServiceImpl.stateChange(map);
    }

    @ApiOperation("B端设备维护信息同步")
    @RequestMapping(value = "/newSynEqFault",method = RequestMethod.POST)
    public Result<Object> newSynEqFault(@RequestBody AddCmsEqFaultDto addCmsEqFaultDto){
        return cmsEqFaultServiceImpl.newSynEqFault(addCmsEqFaultDto);
    }

}
