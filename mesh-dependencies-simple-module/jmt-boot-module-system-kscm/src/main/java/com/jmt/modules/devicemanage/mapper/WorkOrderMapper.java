package com.jmt.modules.devicemanage.mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jmt.modules.devicemanage.entity.WorkOrder;
import com.jmt.modules.devicemanage.vto.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 部署工单表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-04-24 10:58:21
 */
public interface WorkOrderMapper extends BaseMapper<WorkOrder> {
    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    WorkOrder queryById(Integer id);

    //工单分页查询
    List<WorkOrderListAppVo> queryAllByPartnerIdApp(Page<WorkOrderListAppVo> page, WorkOrderListAppDto cms);
    //工单业务员分页查询
    List<WorkOrderListAppVo> queryAllByPartnerSubIdApp(Page<WorkOrderListAppVo> page, WorkOrderListAppDto cms);

    // 根据商部署点编号获取部署人员信息o
    WorkOrderInfoVo queryByMcode(String mCode);
    //按部署点编号删除
    void  deleteByMCode(String  mcode);
    //  根据部署订单号查询
    WorkOrderInfoAppVo queryByOrderId(String orderId);
    //通过部署工单号修改部署状态
    int updateStatusByOrderId(@Param("mWorkorder") String mWorkorder, @Param("status") Integer status);
    //按业务员和状态统计工单数
    Integer getOrderCountBySubCode(String orderStatus,String subCode);
    //统计运营商的部署工单数
    Integer getOrderCountByPid(String orderStatus,String pId);

   // List<WorkOrder> queryByOrderStatus(String mCode);
    //WorkOrder queryByOrderNo(String orderNo);
   // Integer[] queryWordIdByPidAndStatus(@Param("pId") String pId,@Param("status") int status);
   // Integer getOrderCount();

}