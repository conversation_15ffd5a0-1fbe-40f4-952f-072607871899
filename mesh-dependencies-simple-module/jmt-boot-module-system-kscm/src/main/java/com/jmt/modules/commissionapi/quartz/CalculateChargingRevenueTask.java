package com.jmt.modules.commissionapi.quartz;

import com.jmt.modules.castingapplet.util.DateUtils;
import com.jmt.modules.client.platform.PlatformClient;
import com.jmt.modules.commission.entity.CmsOrderRevenue;
import com.jmt.modules.commission.entity.UserPoints;
import com.jmt.modules.commission.service.UserPointsService;
import com.jmt.modules.shopbusiness.entity.UserScoreLog;
import com.jmt.modules.shopbusiness.service.common.UserScoreLogServiceImpl;
import lombok.extern.slf4j.Slf4j;
import com.jmt.modules.commission.entity.NideshopChargeOrder;
import com.jmt.modules.commissionapi.service.OrderTimingTaskService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

@Slf4j
@Component
public class CalculateChargingRevenueTask {

    @Resource
    private OrderTimingTaskService orderTimingTaskServiceImpl;

    @Resource
    private PlatformClient platformClient;

    @Resource
    private UserPointsService userPointsService;

    @Resource
    private UserScoreLogServiceImpl userScoreLogService;

   // @Scheduled(cron = "0/11 * * * * ?")
    public void execute() {
        log.info("同步充电数据定时任务执行--->>{}", DateUtils.format(new Date(),DateUtils.DATE_TIME_PATTERN));
        List<NideshopChargeOrder> listChargeOrder = platformClient.listUndividedOrders();
        if (listChargeOrder == null && listChargeOrder.isEmpty()) return;

        log.info("开始同步充电数据{}条",listChargeOrder.size());
        for (NideshopChargeOrder listOrder : listChargeOrder) {
            try {
                CmsOrderRevenue orderRevenue = orderTimingTaskServiceImpl.chargingIncome(listOrder);
                if (orderRevenue!=null) {
                    addPorint(orderRevenue);
                    platformClient.updateOrderIncome(listOrder.getChargeOrder());
                }

            } catch (Exception e) {
                e.printStackTrace();
                log.error("充电收益同步失败" + e.getMessage());
            }

        }
    }

    private  void addPorint(CmsOrderRevenue orderRevenue ){
        if (orderRevenue==null || orderRevenue.getMerchantCode()==null) return;

        String userIdStr = orderRevenue.getMerchantCode().replace("M", "");
        Integer userId=Integer.parseInt(userIdStr);

        // 更新用户积分
        UserPoints userPoints = userPointsService.queryById(userId);
        BigDecimal oldPoints = new BigDecimal(userPoints.getPoints().toString());
        BigDecimal score = new BigDecimal("100").multiply(orderRevenue.getRestaurantRevenue());
        BigDecimal points = score.add(oldPoints);
        if (userPoints == null) {
            userPoints = new UserPoints();
            userPoints.setId(userId);
            userPoints.setPoints(points.intValue());
            userPointsService.insert(userPoints);
        } else {
            userPoints.setPoints(points.intValue());
            userPointsService.update(userPoints);
        }

        // 添加积分记录
        UserScoreLog userScoreLog = new UserScoreLog();
        userScoreLog.setUserId(userId);
        userScoreLog.setType(1);
        userScoreLog.setScore(score.intValue());
        userScoreLog.setCreateTime(new Date());
        userScoreLog.setRemark("充电收益获得积分");
        userScoreLog.setCurScore(points.intValue());
        userScoreLogService.insert(userScoreLog);
    }
}
