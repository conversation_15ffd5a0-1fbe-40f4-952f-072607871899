package com.jmt.modules.operateteam.service;

import com.alibaba.fastjson.JSONObject;
import com.jmt.common.api.vo.Result;
import com.jmt.common.util.DySmsEnum;
import com.jmt.common.util.DySmsHelper;
import com.jmt.common.util.RedisUtil;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 放短信
 */
@Service
public class SendSMSServer {
    @Resource
    private RedisUtil redisUtil;
    /**
     * redis短信验证key
     */
    public static final String SMS_CACHE_KEY = "phone:associate:code:";
    /**
     * 限制重复获取短信验证
     */
    public static final String SMS_SEND_STINT = "phone:associate:stint";
    /**
     * 短信验证码 5分钟过期
     */
    private static final long SMS_CACHE_EXPIRE_DATA = 5 * 60;
    /**
     * 10秒内禁止重复获取验证码
     */
    private static final long SMS_SEND_STINT_EXPIRE_DATA = 10;


    /**
     * 发送短信服务:传入参数最好改为对象
     * @param json
     * @return
     */
    public Result<Object> sendSms(JSONObject json) {
        try {
            String regexp = "^((13[0-9])|(14[0-9])|(15([0-3]|[5-9]))|(16([0-3]|[5-9]))|(17[013678])|(18[0-9]))\\d{8}$";
            Pattern pattern = Pattern.compile(regexp);
            Matcher matcher = pattern.matcher(json.getString("phone"));
            if (!matcher.matches()) {
                return Result.error("手机号格式错误");
            }
            boolean doesItExist = redisUtil.hasKey(SMS_SEND_STINT + json.getString("phone"));
            if (doesItExist) {
                return Result.error("请稍后在发送短信");
            }
            JSONObject jsonObject = new JSONObject();
            String verifyCode = RandomStringUtils.randomNumeric(6);
            jsonObject.put("code", verifyCode);
            boolean setStr = redisUtil.set(SMS_CACHE_KEY + json.getString("phone"), verifyCode, SMS_CACHE_EXPIRE_DATA);
            if (setStr) {
                boolean whetherSucceed = DySmsHelper.sendSms(json.getString("phone"), jsonObject,
                        "0".equals(json.getString("codeType")) ? DySmsEnum.ASSOCIATE_REGISTERED_CODE : "1".equals(json.getString("codeType")) ? DySmsEnum.CHANGE_PASSWORD_CODE : DySmsEnum.AUTHENTICATION_CODE);
                if (whetherSucceed) {
                    redisUtil.set(SMS_SEND_STINT + json.getString("phone"), json.getString("phone"), SMS_SEND_STINT_EXPIRE_DATA);
                    return Result.ok("短信发送成功");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Result.error("短信发送失败");
    }
}
