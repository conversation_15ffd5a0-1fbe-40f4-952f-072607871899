package com.jmt.modules.shopbusiness.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jmt.modules.shopbusiness.entity.UserBusinessCode;
import com.jmt.modules.shopbusiness.entity.UserCard;
import com.jmt.modules.shopbusiness.entity.UserPublishJoint;

/**
 * 用户银行卡表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-09-15 13:55:45
 */
public interface UserCardMapper extends BaseMapper<UserCard> {
    /**
     * 分页查询
     * @param page
     * @param status
     * @param like
     * @return
     */
    IPage<UserCard> getList(IPage<UserCard> page, Integer status,String like);

    /**
     * 状态总数统计
     * @param status
     * @return
     */
    Integer getStatusCount(int status);

}