package com.jmt.modules.shopbusiness.entity.config;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jmt.modules.shopbusiness.entity.supper.EntityBase2;
import lombok.Data;

import java.io.Serializable;


/**
 * 管理員用戶(Business)实体类
 *
 * <AUTHOR>
 * @since 2020-09-15 13:55:45
 */
@Data
@TableName("user_admin_user")
public class AdminUser extends EntityBase2 implements Serializable {
    private static final long serialVersionUID = -47893025022045970L;
    
    private String username="";
    /**
    * 餐厅名称
    */
    private String password="";
    /**
    * 联系人
    */
    private String name="";
    /**
    * 联系电话
    */
    private String avatar="";

    private String phone="";
    /**
    * 所在区域
    */
    private String email="";
    
    private Integer sex=1;
    /**
    * 餐厅地址
    */
    private Integer locked=0;


}