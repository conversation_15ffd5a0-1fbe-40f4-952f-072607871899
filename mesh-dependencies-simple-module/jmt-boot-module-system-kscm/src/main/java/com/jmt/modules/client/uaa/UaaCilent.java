package com.jmt.modules.client.uaa;

import com.alibaba.fastjson.JSONObject;
import com.ihomeui.mesh.client.AuthorizedFeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;


@AuthorizedFeignClient(name = "uaa",decode404 = true)
public interface UaaCilent {
   @PostMapping("/api/users")
   ResponseEntity<UaaUser> createUser(@RequestBody ManagedUserVM userVM);

   @PutMapping("/api/users")
   ResponseEntity<?> updateUser(@RequestBody ManagedUserVM userVM);

   @DeleteMapping("/api/users/{id}?byId=true")
   ResponseEntity<Void> deleteUser(@PathVariable Long id);

   @GetMapping(value = "/api/users/{id}?byId=true")
   ResponseEntity<UserInfo> getUserById(@PathVariable long id);

   @PostMapping(value = "/oauth/token",headers = "Authorization=Basic d2ViX2FwcDo=")
   ResponseEntity<?> token(@RequestParam("username") String username,@RequestParam("password") String password,@RequestParam("userType") Integer userType,
                           @RequestParam("rememberMe") String rememberMe,@RequestParam("grant_type") String grant_type);

   @PostMapping("/api/users/userInfoByPhone")
   User getUserInfoByPhone(@RequestParam String phone);

   @PostMapping("/api/users/{login}/change_user_password")
   ResponseEntity<?> changePassword(@PathVariable String login,@RequestParam String password);

   @PostMapping("/api/users/{id}/verify_password")
   String verifyPassword(@RequestParam String password, @PathVariable Long id);

   @PostMapping("/api/user-open-auth/listUserOpenAuthInfo")
   JSONObject listUserOpenAuthInfo(@RequestBody JSONObject params);

   @PostMapping("/api/users/phone/update-integral")
   ResponseEntity<?> updateIntegral(@RequestBody UserPointsRecordDTO userPointsRecord);

   //lsw add
   @GetMapping("/api/user-open-auth/user_auth_info/{id}")
   UserOpenAuthInfoDto getUserAuthInfo(@PathVariable Long id, @RequestParam String appletsType);

}
