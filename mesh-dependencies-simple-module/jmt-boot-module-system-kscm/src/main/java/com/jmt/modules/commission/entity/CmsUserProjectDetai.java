package com.jmt.modules.commission.entity;

import lombok.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 用户分佣项目明细表(CmsUserProjectDetai)实体类
 *
 * <AUTHOR>
 * @since 2020-04-13 10:14:49
 */
@Data
public class CmsUserProjectDetai implements Serializable {
    private static final long serialVersionUID = -31753952592317328L;
    /**
    * id
    */
    private Integer id;
    /**
    * 用户分佣编号
    */
    private Integer userCId;
    /**
    * 分佣项目编号
    */
    @NotNull(message = "分佣项目编号为空")
    private Integer cid;
    /**
    * 分佣项目名称
    */
    @NotEmpty(message = "分佣项目名为空")
    private String cname;
    /**
    * 最小分佣比例
    */
    @NotNull(message = "最小分佣比例为空")
    private Double minScale;
    /**
    * 最大分佣比例
    */
    @NotNull(message = "最大分佣比例为空")
    private Double maxScale;
    /**
     * 是否分佣商家为空
     */
    @NotNull(message = "是否分给商家为空")
    private Integer subMerchants;


}