package com.jmt.modules.shopbusiness.entity.config;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;


/**
 * 消息发布参数设置
 */
@Data
@TableName("user_information")
public class UserInformation implements Serializable {
    private static final long serialVersionUID = -47893025022045970L;
    /**
    * 用户ID
    */
    @TableId
    private Integer id;

    /**
     * 模块名称
     */
    private String informationName;
    /**
     * 收款筷圣币
     */
    private Integer collectionBitcoin;
    /**
     * 收费金额
     */
    private Integer collectionAmount;
    /**
     * 持续时长/小时
     */
    private Integer duration;

    public static String MESSAGE_YTPE_MGG="卖光光";
    public static String MESSAGE_YTPE_NC="农餐对接";


}