package com.jmt.modules.shopbusiness.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jmt.modules.shopbusiness.entity.supper.EntityBase2;
import lombok.Data;

import java.io.Serializable;


/**
 * 优选商户
 */
@Data
@TableName("user_code")
public class UserCode extends EntityBase2 implements Serializable {
    private static final long serialVersionUID = -47893025022045970L;

    /**
     * userProfile里面的ID
     */
    private Long userId;

    /**
     * 编码
     */
    private String code;
    /**
    * 名称
    */
    private String name;
    /**
    * 身份证正面
    */
    private String codePositive;
    /**
    * 身份证背面
    */
    private String codeBack;
    /**
    * 性别
    */
    private short sex;


    /**
     * 联系地址
     */
    private  String address;

    /**
     * 运营商编码
     */
    private Integer operatorCode=0;
    /**
     * 推广员编码
     */
    private String promoter="";

    /**
     * 电话号码
     */
    private String phone="";
    /**
    * 状态：0待审核1通过2拒绝
    */
    private Integer isStatus;
    /**
     * 备注
     */
    private String remark;


    /**
     * 国别：没用
     */
    private  String nation;

}