package com.jmt.modules.commission.entity;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 合伙人信息表(CmsPartnerInfo)实体类
 *
 * <AUTHOR>
 * @since 2020-04-13 15:52:53
 */
@Data
@JsonAutoDetect(getterVisibility=JsonAutoDetect.Visibility.NONE)
public class CmsPartnerInfo implements Serializable {
    private static final long serialVersionUID = 581383649632731639L;
    private Integer id;
    /**
     * 合伙人编号
     */
    @JsonProperty("pId")
    private Integer pId;
    /**
     *  uaaId
     */
    @JsonProperty("uaaId")
    private Long uaaId;
    /**
     * 合伙人公司名称
     */
    @JsonProperty("pName")
    @NotEmpty(message = "合伙人公司名称为空")
    private String pName;
    /**
     * 合同编号
     */
    @JsonProperty("pContractNum")
    @NotEmpty(message = "合同编号为空")
    private String pContractNum;
    /**
     * 合同金额
     */
    @JsonProperty("pContractNumMoney")
    private String pContractNumMoney;
    /**
     * 合同起始时间
     */
    @JsonProperty("pContractStarTime")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date pContractStarTime;
    /**
     * 合同结束时间
     */
    @JsonProperty("pContractEndTime")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date pContractEndTime;
    /**
     * 联系人名字
     */
    @JsonProperty("pContactsName")
    @NotEmpty(message = "联系人名字为空")
    private String pContactsName;
    /**
     * 推荐人编码
     */
    @JsonProperty("pRecommenderCode")
    private Integer pRecommenderCode;
    /**
     * 性别 0：男 1：女
     */
    @JsonProperty("pSex")
    @NotNull(message = "性别为空")
    private Integer pSex;

    /**
     * 手机号
     */
    @JsonProperty("pPhoneNum")
    @NotEmpty(message = "手机号为空")
    private String pPhoneNum;
    /**
     * 登陆账号
     */
    @JsonProperty("pLoginName")
    @NotEmpty(message = "登陆账号")
    private String pLoginName;
    /**
     * 登陆密码
     */
    @JsonProperty("pLoginPwd")
    @NotEmpty(message = "登陆密码为空")
    private String pLoginPwd;
    /**
     * 账号状态 0 待审核、1 审核中、2 驳回、3 冻结、4 正常
     */
    @JsonProperty("pStatus")
    private Integer pStatus;
    /**
     * 用户分佣表编号
     */
    @JsonProperty("userCId")
    @NotNull(message = "用户分佣表编号为空")
    private Integer userCId;
     /**
      * 证件编号
      */
     @JsonProperty("pCertificatesCode")
     private String pCertificatesCode;
     /**
      * 电子邮箱
      */
     @JsonProperty("pEmail")
     private String pEmail;
     /**
      * 推荐码
      */
     @JsonProperty("pRefereeCode")
     private String pRefereeCode;
    /**
     * 用户总收益
     */
    @JsonProperty("pProfit")
    private BigDecimal pProfit;
    /**
     * 用户可提现
     */
    @JsonProperty("pWithdrawMoney")
    private BigDecimal pWithdrawMoney;
     /**
      * 设备总数
      */
     @JsonProperty("pEqNum")
     private Integer pEqNum;
     /**
      * 级别
      */
     @JsonProperty("pLevel")
     private Integer pLevel;
     /**
      * 团队说明
      */
     @JsonProperty("teaminfo")
     private String teaminfo;

    /**
     * 支付密码
     */
     @JsonProperty("paymentPassword")
     @JsonIgnore
     private Integer paymentPassword;
    /**
     * 是否注册
     */
    @JsonProperty("pIsRegister")
    @JsonIgnore
    private Boolean pIsRegister;
    /**
     *  合伙人类型  1 收益权所有人 2 运营商
     */
    @NotNull(message = "合伙人类型为空")
    @JsonProperty("partnerType")
    private Integer partnerType;
    /**
     *  投广用户最大分佣金额
     */
    @JsonProperty("maxCommissionAmount")
    private BigDecimal maxCommissionAmount;
    /**
     * 创建时间
     */
    @JsonProperty("createTime")
    private Date createTime;
    /**
     * 更新时间
     */
    @JsonProperty("updateTime")
    private Date updateTime;

}
