package com.jmt.modules.commission.model.vo;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@JsonAutoDetect(getterVisibility=JsonAutoDetect.Visibility.NONE)
public class CmsPartsInfoOperatorVo {
    /**
     * 合伙人编号
     */
    @JsonProperty("id")
    private Integer id;

    @JsonProperty("pId")
    private Integer pId;

    @JsonProperty("pContactsName")
    private String pContactsName;

    @JsonProperty("pProfit")
    private BigDecimal pProfit;

    @JsonProperty("pWithdrawMoney")
    private BigDecimal pWithdrawMoney;
}
