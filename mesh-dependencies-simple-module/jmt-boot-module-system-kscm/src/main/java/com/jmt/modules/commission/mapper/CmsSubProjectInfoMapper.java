package com.jmt.modules.commission.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jmt.modules.commission.entity.CmsSubProjectInfo;
import com.jmt.modules.commission.model.vo.CmsSubProjectInfoVo;

import java.util.List;

public interface CmsSubProjectInfoMapper extends BaseMapper<CmsSubProjectInfo> {
    CmsSubProjectInfo queryById(Integer id);

    CmsSubProjectInfoVo queryBySubId(Integer subId);

    List<CmsSubProjectInfo> queryBySubIdLimit(Integer subId, Integer currIndex, Integer pageSize);

    List<CmsSubProjectInfo> queryBySubIdAndDetail(Integer subId, Integer detailId, Integer currIndex, Integer pageSize);

    List<CmsSubProjectInfo> queryAll(CmsSubProjectInfo cmsSubProjectInfo);

    int insert(CmsSubProjectInfo cmsSubProjectInfo);

    int update(CmsSubProjectInfo cmsSubProjectInfo);

    int deleteById(Integer id);

}
