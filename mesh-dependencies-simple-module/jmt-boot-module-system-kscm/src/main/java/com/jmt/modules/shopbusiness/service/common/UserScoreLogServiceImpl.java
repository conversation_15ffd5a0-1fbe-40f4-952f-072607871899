package com.jmt.modules.shopbusiness.service.common;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jmt.modules.shopbusiness.entity.UserProfile;
import com.jmt.modules.shopbusiness.entity.UserScoreLog;
import com.jmt.modules.shopbusiness.mapper.UserScoreLogMapper;
import com.jmt.modules.shopbusiness.service.UserProfileServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 用户积分流水管理服务
 * @description: TODO
 * @date: 2023/11/10 9:48
 */
@Service
public class UserScoreLogServiceImpl {

    @Resource
    private UserProfileServiceImpl userProfileService;
    @Resource
    private UserScoreLogMapper scoreLogMapper;


    public UserScoreLog queryByUserId(int userId) {
        return scoreLogMapper.queryByUserId(userId);
    }


    public int insert(UserScoreLog userScoreLog) {
        return scoreLogMapper.insert(userScoreLog);
    }

    /**
     * 用户使用积分
     * @param score
     * @param remark
     * @return
     * @throws Exception
     */
    public UserScoreLog useScore(Integer score, String remark) throws  Exception{
        UserProfile userProfile= this.userProfileService.getCurUserProfile();
        if (score > userProfile.getPoints()) throw new Exception("积分不够！");

        UserScoreLog log = new UserScoreLog();
        log.setUserId(userProfile.getId().intValue());
        log.setType(UserScoreLog.Type_Sub);
        log.setScore(score);
        log.setRemark(remark);
        log.setCurScore(userProfile.getPoints()-score);
        this.scoreLogMapper.insert(log);

        userProfile.setPoints(userProfile.getPoints()- score);
        this.userProfileService.update(userProfile);
        return log;
    }

    /**
     * 给用户奖励积分
     * @param score
     * @param remark
     * @return
     */
    public UserScoreLog giveScore(Integer score,String remark) {
        UserProfile userProfile= this.userProfileService.getCurUserProfile();

        UserScoreLog log = new UserScoreLog();
        log.setUserId(userProfile.getId().intValue());
        log.setType(UserScoreLog.Type_Add);
        log.setScore(score);
        log.setRemark(remark);
        log.setCurScore(userProfile.getPoints()+score);
        this.scoreLogMapper.insert(log);

        userProfile.setPoints(userProfile.getPoints()+ score);
        this.userProfileService.update(userProfile);
        return log;
    }

    /**
     * APP前端分页查询
     * @param cond
     * @return
     */
    public IPage<UserScoreLog> getAppList(UserBitcoinServiceImpl.KeywordTypePageCond cond){
        UserProfile userProfile= this.userProfileService.getCurUserProfile();
        QueryWrapper<UserScoreLog> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id",userProfile.getId());
        wrapper.eq("type",cond.getType());
        IPage<UserScoreLog> recPage = scoreLogMapper.selectPage(cond.createPager(),wrapper);

        return recPage;
    }

    /**
     * 管理后台分页查询
     * @param cond
     * @return
     */
    public IPage<UserScoreLog> getBackList(UserBitcoinServiceImpl.KeywordTypePageCond cond){
        IPage<UserScoreLog> recPage = scoreLogMapper.getList(cond.createPager(),cond.getType(),cond.likeCluse());

        return recPage;
    }
}
