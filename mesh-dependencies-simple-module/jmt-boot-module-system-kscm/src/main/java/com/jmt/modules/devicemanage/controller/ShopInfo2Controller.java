package com.jmt.modules.devicemanage.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jmt.modules.devicemanage.service.ShopInfo2ServiceImpl;
import com.jmt.modules.client.uaa.UaaCilent;
import com.jmt.modules.commission.entity.CmsShopInfo;
import com.jmt.modules.devicemanage.vto.ShopInfoListDto;
import com.jmt.modules.devicemanage.vto.ShopInfoListVo;
import com.jmt.modules.devicemanage.vto.ShopInfoVo;
import com.jmt.modules.system.util.UserUtil;
import io.swagger.annotations.ApiOperation;
import com.jmt.common.api.vo.Result;
import com.jmt.modules.commission.model.dto.UpdateCmsShopDto;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.util.List;

/**
 *  设备部署点后台管理接口
 */
@RestController
@RequestMapping("/shopInfo")
public class ShopInfo2Controller {
    @Resource
    private ShopInfo2ServiceImpl shopInfo2Service;

    @Resource
    private UaaCilent uaaCilent;

    @ApiOperation("商家列表")
    @RequestMapping(value = "/listShopInfo",method = RequestMethod.POST)
    public Result<Page<ShopInfoListVo>>  listShopInfo(@RequestBody ShopInfoListDto cmsShopInfoListDto){
        Result<Page<ShopInfoListVo>>  res=shopInfo2Service.listShopInfo(cmsShopInfoListDto);
        return  res;
    }

    @ApiOperation("删除商户")
    @RequestMapping(value = "/deleteShopInfo", method = RequestMethod.GET)
    public void deleteShopInfo(Integer id) {
      //  shopInfo2Service.deleteShopInfo(id);
    }



    @ApiOperation("商户详情")
    @RequestMapping(value = "/getShopInfo/{mCode}",method = RequestMethod.GET)
    public Result<ShopInfoVo> getShopInfo(@PathVariable String mCode){
        return shopInfo2Service.getShopInfo(mCode);
    }
    @ApiOperation("分配收益人")
    @RequestMapping(value = "/setInvestor",method = RequestMethod.POST)
    public Result<Object> setInvestor(  @RequestParam Integer id, @RequestParam String targetPhone){
       try {
           String msg=shopInfo2Service.setInvestor(id, targetPhone);
           return  Result.ok(msg);
       }catch (Exception err){
           return Result.error(err.getMessage());
       }
    }

    @ApiOperation("移除收益人")
    @RequestMapping(value = "/delInvestor",method = RequestMethod.POST)
    public Result<Object> delInvestor(  @RequestParam Integer id){
        try {
            String msg=shopInfo2Service.delInvestor(id);
            return  Result.ok(msg);
        }catch (Exception err){
            return Result.error(err.getMessage());
        }
    }


    /*
    @ApiOperation("获取点位信息")
    @RequestMapping(value = "/listShop",method = RequestMethod.POST)
    public Result<Page<ShopInfoListVo>> listShop(@RequestBody ShopInfoDto shopInfoDto){
        return cmsShopInfoServiceImpl.listShop(shopInfoDto);
    }

    @ApiOperation("获取点位详情")
    @RequestMapping(value = "/getShopInfos",method = RequestMethod.POST)
    public Result<ShopInfoVo> getShopInfos(@RequestBody JSONObject param){
        return cmsShopInfoServiceImpl.getShopInfos(param);
    }

    @ApiOperation("修改点位")
    @RequestMapping(value = "/modifyPoint",method = RequestMethod.POST)
    public Result<Object> modifyPoint(@RequestBody ShopInfoVo shopInfoVo){
        return cmsShopInfoServiceImpl.modifyPoint(shopInfoVo);
    }

    @ApiOperation("通过设备编号查询商户信息")
    @RequestMapping(value = "/getShopInfoByEqCode",method = RequestMethod.POST)
    public CmsShopInfo getShopInfoByEqCode(@RequestParam String eqCode){
        return cmsShopInfoServiceImpl.getShopInfoByEqCode(eqCode);
    }


    @ApiOperation("同步商户详情到广告机")
    @RequestMapping(value = "/aSynShopInfo",method = RequestMethod.POST)
    public Result<Object> aSynShopInfo(@RequestBody JSONObject param){
        return cmsShopInfoServiceImpl.aSynShopInfo(param);
    }

        @ApiOperation("修改商户信息")
    @RequestMapping(value = "/updateShop",method = RequestMethod.POST)
    public Result<Object> updateShop(@RequestBody UpdateCmsShopDto updateCmsShopDto){
        return cmsShopInfoServiceImpl.updateShop(updateCmsShopDto);
    }
*/
}
