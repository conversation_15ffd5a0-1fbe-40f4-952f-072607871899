package com.jmt.modules.devicemanage.vto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

/**
 *  @author: hanshangrong
 *  @Date: 2020/4/25 0025 11:29
 *  @Description:类注释
 *  商家设备数信息
 */
public class EqNum implements Serializable {
    /**
     * 设备类型
     */
    @JsonProperty("eqType")
    private String eqType;

    /**
     * 设备类型总数
     */
    @JsonProperty("eqNum")
    private Integer eqNum;

    /**
     * 在线设备数
     */
    @JsonProperty("eqOnlineNum")
    private Integer eqOnlineNum;
    /**
     * 离线 1 0
     */
    @JsonProperty("offline")
    private int offline;

    public String getEqType() {
        return eqType;
    }

    public void setEqType(String eqType) {
        this.eqType = eqType;
    }

    public Integer getEqNum() {
        return eqNum;
    }

    public void setEqNum(Integer eqNum) {
        this.eqNum = eqNum;
    }

    public Integer getEqOnlineNum() {
        return eqOnlineNum;
    }

    public void setEqOnlineNum(Integer eqOnlineNum) {
        this.eqOnlineNum = eqOnlineNum;
        if(eqOnlineNum<eqNum || eqOnlineNum == 0){
            this.offline=1;
        }
    }

    public int getOffline() {
        return offline;
    }

    public void setOffline(int offline) {
        this.offline = offline;
    }
}
