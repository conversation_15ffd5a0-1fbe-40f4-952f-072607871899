package com.jmt.modules.commission.service.impl;


import com.jmt.common.util.MultipartFileUtil;
import com.jmt.common.util.Sample;
import lombok.extern.slf4j.Slf4j;
import com.jmt.common.api.vo.Result;
import com.jmt.common.util.oss.OssBootUtil;
import com.jmt.modules.commission.entity.CmsFile;
import com.jmt.modules.commission.enums.VideoSpecificationsEnum;
import com.jmt.modules.commission.mapper.CmsFileMapper;
import com.jmt.modules.commission.service.CmsFileService;
import com.jmt.modules.commission.util.FileMD5Util;
import com.jmt.modules.commission.util.VideoUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import javax.annotation.Resource;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;


@Service
@Slf4j
public class CmsFileServiceImpl implements CmsFileService {

    @Value(value = "${jmt.oss.fileDir}")
    private String fileDir;
    @Resource
    private CmsFileMapper cmsFileMapper;

    @Override
    public Result<Map<String, String>> upload(MultipartFile files, Integer fileType, Integer imgType) throws Exception {
        Result<Map<String, String>> result = new Result<>();
        Map<String, String> mapTurn = new HashMap<>(3);
        CmsFile cmsFile = new CmsFile();
        if (fileType == 2) {
            if (imgType == null) {
                return result.error500("图片类型不能为空");
            }
        } else if (fileType == 0) {
            Map<String, Object> checkResult = VideoUtils.checkVideo(VideoSpecificationsEnum.VIDEO_ONE, files.getInputStream());
            if (!(boolean) checkResult.get("result")) {
                return result.error500((String) checkResult.get("errorMsg"));
            }
            String md5 = FileMD5Util.getMD5One(files);
            cmsFile.setFileMd5(md5);
            String sconse = MultipartFileUtil.parseDuration(files);
            System.out.println("上传时间==::"+sconse);
            cmsFile.setFileDuration(Integer.valueOf(sconse));
          //  cmsFile.setFileDuration(Integer.parseInt((String) checkResult.get("Duration")));
        }
        try {
            Map<String, String> map = OssBootUtil.uploads(files, fileDir);
            if (map == null) {
                return result.error500("文件上传失败");
            }

            int lastIndex = map.get("fileName").lastIndexOf('/');
            String fileName = map.get("fileName").substring(lastIndex + 1);

            String oldFileurl = map.get("fileUrl");
            if (Objects.requireNonNull(files.getContentType()).startsWith("image")) {
                cmsFile.setFileName(fileName);
                cmsFile.setFileUrl(oldFileurl);
            } else if (files.getContentType().startsWith("video")){
                String newFileName = "C" + fileName;
                System.out.println("filename:" + map.get("fileName"));
                System.out.println(map.get("fileName").replace(fileName, newFileName));
                //转码
                String video = Sample.changeVideo("file/video/" + map.get("fileName"), "file/video/" + map.get("fileName").replace(fileName, newFileName));


                String fileUrlNew = map.get("fileUrl").replace(fileName, newFileName);

                System.out.println("fileurl======" + map.get("fileUrl").replace(fileName, newFileName));
                if (video != null) {
                    map.put("fileName", newFileName);
                    map.put("fileUrl", fileUrlNew);
                }

                cmsFile.setFileName(newFileName);
                cmsFile.setFileUrl(fileUrlNew);
            }

            cmsFile.setFileRealName(files.getOriginalFilename());
            cmsFile.setFileType(fileType);
            cmsFile.setImgType(imgType);
            //视频上传成功 新增片源库
            cmsFileMapper.insert(cmsFile);
            mapTurn.put("id", cmsFile.getId().toString());
            mapTurn.put("url",oldFileurl);
            result.setResult(mapTurn);
            result.setCode(200);
        } catch (Exception e) {
            result.error500("文件上传失败");
            log.error("文件上传失败", e);
        }
        return result;
    }

    @Override
    public Result<Map<String, String>> uploads(MultipartFile multipartFile) {
        Result<Map<String, String>> result = new Result<>();
        Map<String, String> mapTurn = new HashMap<>(2);
        try {
            Map<String, String> map = OssBootUtil.uploads(multipartFile, fileDir);
            if (map == null) {
                return result.error500("文件上传失败");
            }

            int lastIndex = map.get("fileName").lastIndexOf('/');
            String fileName = map.get("fileName").substring(lastIndex + 1);

            String newFileName = "C" + fileName;
            System.out.println("filename:" + map.get("fileName"));
            System.out.println(map.get("fileName").replace(fileName, newFileName));
            //转码
            String video = Sample.changeVideo("file/video/" + map.get("fileName"), "file/video/" + map.get("fileName").replace(fileName, newFileName));
//            String oldFileurl = map.get("fileUrl");

            String fileUrlNew = map.get("fileUrl").replace(fileName, newFileName);

            if (video != null) {
                map.put("fileName", newFileName);
                map.put("fileUrl", fileUrlNew);
            }



            mapTurn.put("url", fileUrlNew);
            result.setResult(mapTurn);
            result.setCode(200);
        } catch (Exception e) {
            result.error500("文件上传失败");
            log.error("文件上传失败", e);
        }
        return result;
    }

    @Override
    public CmsFile queryById(Integer fileId) {
        return cmsFileMapper.queryById(fileId);
    }

    @Override
    public CmsFile queryByUserIdAndMd5(String userId, String orderMp4Md5) {
        return cmsFileMapper.queryByUserIdAndMd5(userId, orderMp4Md5);
    }
}
