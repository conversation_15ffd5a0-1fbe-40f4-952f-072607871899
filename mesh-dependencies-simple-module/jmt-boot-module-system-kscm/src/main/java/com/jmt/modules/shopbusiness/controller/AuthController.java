package com.jmt.modules.shopbusiness.controller;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import com.jmt.modules.client.uaa.UaaCilent;
import com.jmt.modules.client.uaa.User;
import com.jmt.modules.shopbusiness.entity.Business;
import com.jmt.modules.shopbusiness.entity.UserCode;
import com.jmt.modules.shopbusiness.service.BusinessServiceImpl;
import com.jmt.modules.shopbusiness.service.UserCodeServiceImpl;
import com.jmt.modules.shopbusiness.utils.WxPayJSAPI;
import com.jmt.modules.shopbusiness.vto.ThinkJSResult;
import com.jmt.modules.shopbusiness.entity.config.AdminUser;
import com.jmt.modules.shopbusiness.mapper.config.AdminUserMapper;
import com.jmt.modules.shopbusiness.service.UserProfileServiceImpl;
import com.jmt.modules.shopbusiness.vto.LogInResponse;
import com.jmt.modules.shopbusiness.vto.RegisterTO;
import com.jmt.modules.shopbusiness.entity.UserProfile;
import com.jmt.modules.shopbusiness.service.common.PhoneCheckNumberService;
import com.jmt.modules.shopbusiness.service.common.UserTokenService;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.client.RestTemplate;
/**
 *  餐谋登录控制类
 */
@Slf4j
@RestController
//@CrossOrigin
public class AuthController {

    @Data
    public static class QuickLogInTO {
        private String openid;
        private String access_token;
        private String recommend;
    }

    @Data
    public static class LogInTO {
        private String phone;
        private String code;
        private String recommend;
    }
    @Data
    public static class WXLogInTO {
        private String code;
        private String recommend;
    }

    @Data
    public static class LogInBack {
        private String username;
        private String password;
    }
    @Data
    public static class TokenInfo {
        private String token;
        private String refreshToken;
        private String expireIn;
    }

    @Resource
    private UaaCilent uaaCilent;
    @Resource
    private PhoneCheckNumberService phoneCheckNumberService;
    @Resource
    private UserProfileServiceImpl userProfileService;
    @Resource
    private UserTokenService userTokenService;
    @Resource
    private BusinessServiceImpl businessService;
    @Resource
    private UserCodeServiceImpl userCodeService;

    @Resource
    private AdminUserMapper adminUserMapper;


    private String domain = "http://uni.loginbyphonenumber.jmingt.com/loginByPhoneNumber";
    // private String domain = 'http://api.bclub.jmingt.com/loginByPhoneNumber'
    private String secret = "cGtHbVjRHmFMQszYhINhGPzJulVvuJUc";


    private static String DEF_PSW = "123456";

    @ApiOperation("注册")
    @RequestMapping(value = "/register", method = RequestMethod.POST)
    public ThinkJSResult register(@RequestBody @Valid RegisterTO registerTO) {
        ThinkJSResult<Object> result = new ThinkJSResult<>();
        String msg = phoneCheckNumberService.checkCode(registerTO.getPhone(), registerTO.getCoce());
        if (msg != null) {
            result.error500(msg);
            return result;
        }

        UserProfile userProfile = this.userProfileService.InsertUserProfile(0l, registerTO.getPhone(), registerTO.getPwd(),registerTO.getRecommend());

        LogInResponse response = LogInResponse.createByUserProfile(userProfile);
        String token = userTokenService.createToken(userProfile.getPhone());

       // response.setToken(token);

        return ThinkJSResult.ok("注册成功", response);
    }


    @ApiOperation("登录发送短信验证码")
    @RequestMapping(value = "/public/send/sms", method = RequestMethod.GET)
    public ThinkJSResult sendSMS(String phone) {
        phoneCheckNumberService.sendSMS(phone);
        return ThinkJSResult.ok("");
    }


    @ApiOperation("验证码登录")
    @RequestMapping(value = "/public/loginByCode", method = RequestMethod.POST)
    public ThinkJSResult<Object> loginByCode(@RequestBody LogInTO logInTO)  {
        log.warn("==========enter loginByCode");
        if (logInTO.getPhone() == null || logInTO.getPhone().isEmpty()) return ThinkJSResult.error("手机号不能为空！");
        if (logInTO.getCode() == null || logInTO.getCode().isEmpty()) return ThinkJSResult.error("验证码不能为空！");

        try {
            String phone = logInTO.getPhone();
            String checkCode = logInTO.getCode();
            String msg = phoneCheckNumberService.checkCode(phone, checkCode);
            if (msg != null)
                return ThinkJSResult.error(msg);
            else
                return logInByPhone(phone, logInTO.getRecommend());
        }catch (Exception err){
            return  ThinkJSResult.error(err.getMessage());
        }
    }


    @ApiOperation("app一键快捷登录")
    @RequestMapping(value = "/public/appLoginByPhone", method = RequestMethod.POST)
    public ThinkJSResult<Object> appLoginByPhone(@RequestBody @Valid QuickLogInTO logInTO) {
        log.warn("==========appLoginByPhone");

        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("openid", logInTO.openid);
        paramMap.put("access_token", logInTO.access_token);
        paramMap.put("sign", "cGtHbVjRHmFMQszYhINhGPzJulVvuJUc");//暂时将密钥做签名

        String url = "http://uni.getphonenumber.jmingt.com/loginByPhoneNumber";
        String result = HttpUtil.get(url, paramMap);
        log.warn("==========appLoginByPhone"+result);
        JSONObject json = new JSONObject(result);
        Integer errCode= json.getInt("errCode");
        String errMSG = json.getStr("errMsg");
        if (errCode>0&& !errMSG.isEmpty()){
            return ThinkJSResult.error(errMSG);
        }


        String phone = json.getStr("phoneNumber");
        return logInByPhone(phone,logInTO.getRecommend());

    }

    @ApiOperation("微信小程序一键快捷登录")
    @RequestMapping(value = "/public/miniAppLoginByPhone", method = RequestMethod.POST)
    public ThinkJSResult<Object> miniAppLoginByPhone(@RequestBody @Valid WXLogInTO logInTO)  {
        log.warn("==========miniAppLoginByPhone");

        String APPID = "wxbcc089a15f54bc75";
        String APPSECRET = "89ae1e6499737e463b10a02cad26f520";

        //通过appid和secret来获取token
        //WXContent.APPID是自定义的全局变量
        String tokenUrl = String.format("https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s", APPID, APPSECRET);
        JSONObject resJSON = new JSONObject(HttpUtil.get(tokenUrl));
        String tokenStr = resJSON.getStr("access_token");

        String url = "https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=" + tokenStr;

        //封装请求体
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("code", logInTO.code);

        //封装请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Map<String, String>> httpEntity = new HttpEntity<>(paramMap,headers);

        //通过RestTemplate发送请求，获取到用户手机号码
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<Object> response = restTemplate.postForEntity(url, httpEntity, Object.class);

        LinkedHashMap<String,Object> bodyMap = (LinkedHashMap<String,Object>) response.getBody();
        Integer errCode= (Integer)bodyMap.get("errcode");
        String errMSG = bodyMap.get("errmsg").toString();
        if (errCode>0&& !errMSG.isEmpty()){
            return ThinkJSResult.error(errMSG);
        }
        LinkedHashMap<String,Object> phoneMap =(LinkedHashMap<String,Object>) bodyMap.get("phone_info");
        String phone=phoneMap.get("purePhoneNumber").toString();

        return logInByPhone(phone,logInTO.getRecommend());

    }

    /**
     * 内部登录服务
     * @param phone
     * @param recommand
     * @return
     */
    private ThinkJSResult<Object>  logInByPhone(String phone,String recommand) {
        if (phone ==null || phone.isEmpty())
            return ThinkJSResult.error("电话号码为空！");

        log.warn("==========logInByPhone============");
        User user = uaaCilent.getUserInfoByPhone(phone);
        Long uaaId;
        if (user == null) {
            uaaId = userProfileService.saveUaa(phone, DEF_PSW);
            log.warn("==========saveUaa pass");
        } else uaaId = user.getId();

        UserProfile profile = userProfileService.getByPhone(phone);
        if (profile == null)
            profile = userProfileService.InsertUserProfile(uaaId, phone, DEF_PSW,recommand);
        log.warn("==========getByPhone pass");

        LogInResponse logInResponse = LogInResponse.createByUserProfile(profile);
        //if (loadToken){
        //    TokenInfo tokenInfo=  getToken(phone, DEF_PSW);
       //     BeanUtil.copyProperties(tokenInfo,logInResponse);
       // }
        String userName= getUserName(profile);
        logInResponse.setNickName(userName);
        logInResponse.setPsw(DEF_PSW);
        log.warn("==========getToken pass");

        return ThinkJSResult.ok("登录成功。", logInResponse);
    }

    /**
     * 根据用户资料返回用户名字
     * @param profile
     * @return
     */
    private String getUserName(UserProfile profile){
        if (profile.getRoleType()==1){
            Business business= this.businessService.getBusinessByUserId(profile.getId());
            if (business !=null) return business.getName();
        }
        else if (profile.getRoleType()==2){
            UserCode userCode= this.userCodeService.getUserCodeByUserId(profile.getId());
            if (userCode !=null) return userCode.getName();
        }

        if (profile.getNickname()!=null&& !profile.getNickname().isEmpty())
            return profile.getNickname();
        else if (profile.getName()!=null&& !profile.getName().isEmpty())
            return profile.getName();
        else return   profile.getAccount();
    }

    /**
     * 从UAA获取TOKEN后返回前端
     * 由于通过FC获取UAA令牌的方法不稳定，偶尔出问题，因此该方法基本废除。转为前端直接到UAA申请令牌。
     * @param phone
     * @param psw
     * @return
     */
    public TokenInfo getToken(String phone, String psw) {
        ResponseEntity<?> res = uaaCilent.token(phone, psw, 0, "true", "password");
        LinkedHashMap<String, String> body = (LinkedHashMap<String, String>) res.getBody();

        TokenInfo tokenInfo= new TokenInfo();

        String token = body.get("access_token");
        tokenInfo.setToken(token);
        String refresh_token = body.get("refresh_token");
        tokenInfo.setRefreshToken(token);
        Object expires_in = body.get("expires_in");
        tokenInfo.setExpireIn(expires_in.toString());

        return tokenInfo;
    }

    @ApiOperation("后台登录")
    @RequestMapping(value = "/public/cm/login", method = RequestMethod.POST)
    public ThinkJSResult login(@RequestBody LogInBack form) {
        log.warn("=============enter login: "+form.toString());
        try {
            TokenInfo tokenInfo = this.getToken(form.username, form.password);
            return ThinkJSResult.ok("登录成功", tokenInfo.getToken());
        } catch (Exception err){
            return ThinkJSResult.error("登录失败");
        }
    }

    @ApiOperation("后台用戶信息")
    @RequestMapping(value = "/public/cm/auth/info", method = RequestMethod.GET)
    public ThinkJSResult getUserInfo() {
        AdminUser adminUser = adminUserMapper.selectById(10014L);
        return ThinkJSResult.ok(adminUser);
    }


    @RequestMapping(value = "/auth/getAuthRouters", method = RequestMethod.GET)
    public ThinkJSResult getAuthRouters() {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("roleId", 1);
        map.put("menu", null);
        return ThinkJSResult.ok(map);
    }

    /**
     * 通过卫星接口获取openId（code由前端传入）
     * @param form
     * @return
     */
    @RequestMapping(value = "/public/cm/getOpenId", method = RequestMethod.POST)
    public ThinkJSResult getOpenId(@RequestBody WXLogInTO form) {
        try {
            String openId= WxPayJSAPI.getOpenID(form.code);
            return ThinkJSResult.ok("成功",openId);
        }catch (Exception err){
            return ThinkJSResult.error(err.getMessage());
        }
    }

}
