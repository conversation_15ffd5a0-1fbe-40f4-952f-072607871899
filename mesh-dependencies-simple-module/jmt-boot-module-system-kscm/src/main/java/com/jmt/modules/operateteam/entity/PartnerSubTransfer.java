package com.jmt.modules.operateteam.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.jmt.modules.shopbusiness.entity.supper.EntityBase;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 业务员账号迁移记录
 */
@Data
@TableName("yy_partner_sub_transfer")
public class PartnerSubTransfer extends EntityBase {
    /**
     * 子账号
     */
    private Integer subCode;
    /**
      原名字
     */
    private String fromName;
    /**
     * 原电话
     */
    private String fromPhone;
    /**
     * 新名字
     */
    private String toName;

    /**
     * 新电话
     */
    private String toPhone;


}
