package com.jmt.modules.commission.model.vo;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 *  @author: hanshangrong
 *  @Date: 2020/4/23 0023 09:25
 *  @Description:类注释
 */
@Data
@JsonAutoDetect(getterVisibility=JsonAutoDetect.Visibility.NONE)
public class CmsPartnerSubLoginVo {
    @JsonProperty("id")
    private int id;
    @JsonProperty("pId")
    private int pId;
    @JsonProperty("pSubPhone")
    private String pSubPhone;
    @JsonProperty("pSubPwd")
    @JsonIgnore
    private String pSubPwd;
    @JsonProperty("pCode")
    private int pCode;
    @JsonProperty("pSubStatus")
    private int pSubStatus;
    @JsonProperty("pSubName")
    private String pSubName;
    @JsonProperty("pPartnerType")
    private Integer pPartnerType;

}
