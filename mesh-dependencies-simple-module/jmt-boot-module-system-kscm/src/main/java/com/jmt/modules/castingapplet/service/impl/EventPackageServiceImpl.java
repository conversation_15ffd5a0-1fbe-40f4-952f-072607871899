package com.jmt.modules.castingapplet.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.jmt.common.api.vo.Result;
import com.jmt.modules.castingapplet.entity.EventPackage;
import com.jmt.modules.castingapplet.mapper.EventPackageMapper;
import com.jmt.modules.castingapplet.service.EventPackageService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service
public class EventPackageServiceImpl implements EventPackageService {
    @Resource
    private EventPackageMapper eventPackageMapper;


    @Override
    public Result<List<EventPackage>> listEventPackage(JSONObject param) {
        Result<List<EventPackage>> result=new Result<>();
        List<EventPackage> list=eventPackageMapper.queryByRegion(param);
        result.setCode(200);
        result.setResult(list);
        return result;
    }

    @Override
    public EventPackage queryEventPackageById(Integer discountId) {
        return eventPackageMapper.queryById(discountId);
    }
}
