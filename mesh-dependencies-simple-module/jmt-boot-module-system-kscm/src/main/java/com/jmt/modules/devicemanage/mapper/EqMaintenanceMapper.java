package com.jmt.modules.devicemanage.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jmt.modules.devicemanage.entity.EqMaintenance;
import com.jmt.modules.devicemanage.vto.EqMaintenanceOperatorPageCond;
import com.jmt.modules.devicemanage.vto.EqMaintenanceSubCodePageCond;
import com.jmt.modules.shopbusiness.vto.OperatorUserPageCond;
import com.jmt.modules.shopbusiness.vto.SubCodeUserPageCond;


/**
 * 设备排查（维护）记录Mapper
 */
public interface EqMaintenanceMapper extends BaseMapper<EqMaintenance> {
    //运营商设备排查（维护）分页查询
    Page<EqMaintenance> queryByOperator(IPage<EqMaintenance> page, EqMaintenanceOperatorPageCond cond);
    //业务员设备排查（维护）分页查询
    Page<EqMaintenance> queryBySubCode(IPage<EqMaintenance> page, EqMaintenanceSubCodePageCond cond);
}