package com.jmt.modules.commission.model.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Data
public class AddCmsFile {
    /**
     * 用户编号（合伙人和商户）
     */
    @NotBlank(message = "商户编号为空")
    private String userId;
    /**
     * 文件名称
     */
    @NotBlank(message = "文件名称为空")
    private String fileName;
    /**
     * 文件存放地址
     */
    @NotBlank(message = "文件存放地址为空")
    private String fileUrl;
    /**
     * 文件类型 0：视频 2：图片
     */
    @NotNull(message = "文件类型为空")
    private Integer fileType;
    /**
     * 图片类型 1:合同 2:运营执照
     */
    private Integer imgType;
    /**
     * 视频mD5
     */
    private String fileMd5;
    /**
     *  时长
     */
    private Integer fileDuration;
}
