package com.jmt.modules.sales.service;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jmt.modules.sales.entity.Goods;
import com.jmt.modules.sales.entity.Printer;
import com.jmt.modules.sales.entity.SaleOrder;
import com.jmt.modules.sales.entity.SaleOrderItem;
import com.jmt.modules.sales.mapper.GoodsMapper;
import com.jmt.modules.sales.mapper.PrinterMapper;
import com.jmt.modules.sales.mapper.SaleOrderItemMapper;
import com.jmt.modules.sales.mapper.SaleOrderMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class OrderPrintServiceImpl {
    @Resource
    private PrinterMapper printerMapper;

    @Resource
    private SaleOrderMapper saleOrderMapper;
    @Resource
    private SaleOrderItemMapper saleOrderItemMapper;

    @Resource
    private GoodsMapper  goodsMapper;



    public void printOrder(Long orderId) throws  Exception{
        SaleOrder order= saleOrderMapper.selectById(orderId);
        Printer printer = this.getPrinter();

      //  JSONObject addressJSON = JSONUtil.parseObj(order.getReceiverAddress());
        String content= getContent(order);

        String result = PrinterUtil.print(printer.getSn(), content, 1);//暂时改为单联的

    }

    private Printer getPrinter() throws Exception{
        QueryWrapper<Printer> wrapper = new QueryWrapper<>();
        wrapper.eq("online",1);
        List<Printer> list= this.printerMapper.selectList(wrapper);
        if (list.size() <1)
            throw new Exception("打印机未配置!");
        else return list.get(0);
    }

    private List<SaleOrderItem> getOrderItems(SaleOrder order) {
        QueryWrapper<SaleOrderItem> wrapper = new QueryWrapper<>();
        wrapper.eq("order_id",order.getId());
        List<SaleOrderItem> list= this.saleOrderItemMapper.selectList(wrapper);
        return  list;
    }

    private  String getContent(SaleOrder order){
        List<SaleOrderItem> items = getOrderItems(order);

        String content = "<C><L2>餐界助手</L2></C>\n" +
                //    "<C><L1>----" + countNum + "----</L1></C>\n" +
                "单号：" + order.getOrderNo() + "<BR>\n\n"+
                "品名　　　　　 单价   数量 金额<BR>\n" +
                "--------------------------------<BR>\n";
        BigDecimal totalPrice = new BigDecimal("0"), realPrice = new BigDecimal("0");
        for (SaleOrderItem item : items) {
            Goods goods = goodsMapper.selectById(item.getGoodsId());
            content += (goods.getCode() + "<BR>\n" +
                    goods.getName() + "   " +
                    item.getTradePrice() + "  " +
                    "×" + item.getQuantity() + " ￥" +
                    item.getTradePrice()*item.getQuantity() + "<BR>\n" +
                    "--------------------------------<BR>\n");
        }

        content += "商品合计：               ￥" + order.getAmount() + "<BR>\n" +
                "积分抵扣：               ￥" + order.getPointsCut() + "<BR>\n" +
                "实付金额：               ￥" + order.getPayment() + "<BR>\n";

        content += "送货地点：" + order.getReceiverAddress() + "<BR>\n" +
                "收件人：" + order.getReceiverName() + "<BR>\n" +
                "手机号：" + order.getReceiverPhone()  + "\n" +
                "<BR>\n" +
                "<C><L1>扫码确认收货</L1></C><BR>\n" +
                "<QRCODE>"+ order.getId()+"_" + order.getOrderNo()+ "</QRCODE><BR>\n" +

                "<BR>\n" +
                " <C>" + DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss") + "</C>";
        return  content;
    }



}
