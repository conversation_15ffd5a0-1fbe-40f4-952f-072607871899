package com.jmt.modules.commission.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import com.jmt.modules.commission.entity.CmsEqSet;
import com.jmt.modules.commission.entity.CmsFile;
import com.jmt.modules.commission.entity.CmsShopProjectInfo;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
public class UpdateShopInfoAppDto {

    /**
     * 商户编号
     */
    @JsonProperty("mCode")
    @NotBlank(message = "商户编号为空")
    private String mCode;
    /**
     * 每分钟充电消耗积分数
     */
    @JsonProperty("mPaymentIntegral")
    private Integer mPaymentIntegral;

    @JsonProperty("cmsEqSet")
    private CmsEqSet cmsEqSet;

    @JsonProperty("projectInfoList")
    private List<CmsShopProjectInfo> projectInfoList;

    @JsonProperty("cmsFileMp4")
    @Valid
    private CmsFile cmsFileMp4;

}
