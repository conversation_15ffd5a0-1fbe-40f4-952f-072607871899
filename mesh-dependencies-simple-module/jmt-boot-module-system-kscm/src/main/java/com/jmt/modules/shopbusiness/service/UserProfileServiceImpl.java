package com.jmt.modules.shopbusiness.service;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jmt.common.exception.RRException;
import com.jmt.modules.client.uaa.*;
import com.jmt.modules.operateteam.entity.PartnerSub;
import com.jmt.modules.operateteam.mapper.PartnerSubCodeMapper;
import com.jmt.modules.shopbusiness.entity.UserCode;
import com.jmt.modules.shopbusiness.vto.KeyWordPageCond;
import com.jmt.modules.shopbusiness.entity.UserAddress;
import com.jmt.modules.shopbusiness.entity.UserProfile;
import com.jmt.modules.shopbusiness.mapper.UserAddressMapper;
import com.jmt.modules.shopbusiness.mapper.UserProfileMapper;
import com.jmt.modules.shopbusiness.vto.OperatorUserPageCond;
import com.jmt.modules.system.util.UserUtil;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 注册用户（游客）管理服务
 */
@Service
public class UserProfileServiceImpl  {

    @Resource
    private UaaCilent uaaCilent;
    @Resource
    private UserProfileMapper userProfileMapper;
    @Resource
    private UserAddressMapper addressMapper;
    @Resource
    private PartnerSubCodeMapper partnerSubCodeMapper;
    private String mockPhone="";

    /**
     * 这是为了单元测试时能够模拟登录，在生产中无用
     * @param phone
     */
    public void SetMockPhone(String phone){
        this.mockPhone= phone;
    }
    public UserProfile InsertUser(String phone,String psw) {
        Long uaaId = saveUaa(phone, psw);
        UserProfile  userProfile =InsertUserProfile(uaaId,phone,psw,"");
        return userProfile;
    }

    public UserProfile InsertUserProfile(Long uaaId,String phone,String psw) {
        return  InsertUserProfile(uaaId,phone,psw,"");
    }

    /**
     * 保存新的用户信息
     * @param uaaId
     * @param phone
     * @param psw
     * @param recommand
     * @return
     */
    public UserProfile InsertUserProfile(Long uaaId,String phone,String psw,String recommand){
        UserProfile  userProfile = new UserProfile();

        userProfile.setUaaId(uaaId);
        userProfile.setLastLogin(DateTime.now().millsecond()/1000);
        userProfile.setPhone(phone);
        userProfile.setMail(psw);
        userProfile.setHeadImg("http://image.bclub.jmingt.com/image/default_head_1.png");
        this.userProfileMapper.insert(userProfile);
        String account="s" + userProfile.getId().toString();
        userProfile.setAccount(account);
        userProfile.setNickname(account);

        if (recommand==null||recommand.isEmpty()) recommand="**********";
        userProfile.setRecommend(recommand);
        PartnerSub partnerSub= partnerSubCodeMapper.querySubCode(Integer.parseInt(recommand));
        if (partnerSub!=null)
            userProfile.setOperatorCode(partnerSub.getPCode());//关联运营商

        this.userProfileMapper.updateById(userProfile);

        userProfile = this.userProfileMapper.selectById(userProfile.getId());
        return  userProfile;
    }

    public UserProfile getById(Long id){
        return  this.userProfileMapper.selectById(id);
    }

    public UserProfile getByPhone(String phone){
        return  this.userProfileMapper.queryByPhone(phone);
    }

    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRES_NEW)
    public int update(UserProfile profile) {
        return userProfileMapper.updateById(profile);
    }

    /**
     * 为用户增加UAA记录或更新UAA信息
     * @param phone
     * @param psw
     * @return
     */
    public Long saveUaa(String phone,String psw) {
        User uaa = uaaCilent.getUserInfoByPhone(phone);
        ManagedUserVM managedUserVM = new ManagedUserVM();
        if (uaa != null && uaa.getId() != null) {
            //修改用户信息
            managedUserVM.setPassword(psw);
            managedUserVM.setId(uaa.getId());
            managedUserVM.setLogin(uaa.getLogin());
            managedUserVM.setActivated(uaa.getActivated());
            managedUserVM.setMobilePhone(uaa.getMobilePhone());
            managedUserVM.setNickname(uaa.getNickname());
            managedUserVM.setEmail(uaa.getEmail());
            managedUserVM.setName(uaa.getName());
            managedUserVM.setSex(uaa.getSex());
            managedUserVM.setBirthday(uaa.getBirthday());
            managedUserVM.setPhoto(uaa.getPhoto());
            uaaCilent.updateUser(managedUserVM);
            return uaa.getId();
        } else {
            //新增用户信息
            managedUserVM.setLogin(phone);
            managedUserVM.setPassword(psw);
            managedUserVM.setCreatedBy("admin");
            managedUserVM.setMobilePhone(phone);
            managedUserVM.setName(psw);
            managedUserVM.setActivated(true);
            managedUserVM.setCreatedDate(LocalDateTime.now());
            ResponseEntity<UaaUser> responseEntity = uaaCilent.createUser(managedUserVM);
            UaaUser uaaUser = responseEntity.getBody();

            if (uaaUser == null) {
                throw new RRException("创建uaa账户失败");
            } else
                return uaaUser.getId();
        }
    }

    /**
     * 获得当前登录用户的手机号码（其实也是登录名）
     * 注意在单元测试中，返回的是通过SetMockPhone方法设置的mockPhone
     * @return
     */
    public String getCurUserPhone(){
        if (!mockPhone.isEmpty())
            return  this.mockPhone;
        else {
            Long userId = UserUtil.getLoginUserId();
            ResponseEntity<UserInfo> response = uaaCilent.getUserById(userId);
            return response.getBody().getMobilePhone();
        }
    }

    /**
     * 获得当前登录的用户的用户信息
     * @return
     */
    public UserProfile getCurUserProfile(){
        String phone= getCurUserPhone();
        return this.getByPhone(phone);
    }

    /**
     * 获得运营商下的注册用户（分页）
     * @param cond
     * @return
     */
    public IPage<UserProfile> queryByOperator(OperatorUserPageCond cond){
        cond.formatPhonelikeCluse();
        IPage<UserProfile> page= this.userProfileMapper.queryByOperator(cond.createPager(),cond);
        return page;
    }

    /**
     * 获得业务员发展的用户注册信息（分页）
     * @param recommandId
     * @param roleType
     * @return
     */
    public List<UserProfile> getUserByRecommander(String recommandId,int roleType){
        QueryWrapper<UserProfile> wrapper = new QueryWrapper<>();
        wrapper.eq("recommend",recommandId);
        wrapper.eq("role_type",roleType);
        wrapper.orderByDesc("create_time");
        List<UserProfile> list= this.userProfileMapper.selectList(wrapper);
        return list;
    }

    /**
     * 用户信息分页查询
     * @param cond
     * @return
     */
    public IPage<UserProfile> getList(KeyWordPageCond cond){
        IPage<UserProfile> page= this.userProfileMapper.getList(cond.createPager(),cond.likeCluse());
        return page;
    }

    /**
     * 修改用户昵称
     * @param nikeName
     */
    @Transactional(propagation = Propagation.REQUIRED)
    public void modifyUserNick(String nikeName)   {
        UserProfile profile = this.getCurUserProfile();
        profile.setNickname(nikeName);
        this.userProfileMapper.updateById(profile);
    }

    /**
     * 修改用户头像图片
     * @param url
     */
    @Transactional(propagation = Propagation.REQUIRED)
    public void modifyHeadImg(String url)   {
        UserProfile profile = this.getCurUserProfile();
        profile.setHeadImg(url);
        this.userProfileMapper.updateById(profile);
    }

    /**
     * 删除。注意如果已经升级，则不能删除
     * @param id
     * @throws Exception
     */
    @Transactional(propagation = Propagation.REQUIRED)
    public void delete(Long id) throws Exception{
        UserProfile userProfile= userProfileMapper.selectById(id);
        if (userProfile.getRoleType()==0){
            userProfileMapper.deleteById(userProfile.getId());
        }
        else if (userProfile.getRoleType()==1)
            throw new Exception("该用户已经升级为餐厅，不能删除。");
        else if (userProfile.getRoleType()==2){
            throw new Exception("该用户已经升级为优选商户，不能删除。");
        }

    }

    public IPage<UserAddress> getAddressList(KeyWordPageCond cond){
        IPage<UserAddress> page= this.addressMapper.getList(cond.createPager(),cond.likeCluse());
        return page;
    }
}
