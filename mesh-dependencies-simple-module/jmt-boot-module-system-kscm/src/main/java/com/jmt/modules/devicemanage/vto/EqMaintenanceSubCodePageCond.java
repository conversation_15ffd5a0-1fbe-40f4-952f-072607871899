package com.jmt.modules.devicemanage.vto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.jmt.modules.devicemanage.entity.EqMaintenance;
import com.jmt.modules.operateteam.vto.PageCond;
import lombok.Data;

/**
 *  @author: hanshangrong
 *  @Date: 2020/4/27 0027 20:58
 *  @Description:类注释
 *  app 设备管理
 */
@Data
public class EqMaintenanceSubCodePageCond extends PageCond {
    /**
     * 状态：-1:全部，0：待审核；1：已审核；2：已驳回
     */
    private Integer status=-1;

    /**
     * 所属业务员编号
     */
    @JsonProperty("subCode")
    private Integer subCode;

    /**
     * 名字
     */
    private String name;
    /**
     * 排序方式:0正序，1倒序
     */
    private Integer orderType=0;


    public void formatPhonelikeCluse(){
        if (this.name!=null&& !this.name.isEmpty())
            this.name= "%"+ this.name+"%";
    }
}
