package com.jmt.modules.shopbusiness.controller;


import com.jmt.modules.shopbusiness.vto.PageCond;
import com.jmt.modules.shopbusiness.vto.ThinkJSResult;
import com.jmt.modules.sales.entity.UserGoodsCategory;
import com.jmt.modules.shopbusiness.entity.UserShop;
import com.jmt.modules.shopbusiness.entity.config.PageLink;
import com.jmt.modules.shopbusiness.service.HomePageServiceImpl;
import com.jmt.modules.sales.service.UserGoodServiceImpl;
import com.jmt.modules.shopbusiness.service.UserShopServiceImpl;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Hashtable;
import java.util.List;
import java.util.Map;

/**
 *  @author: hanshangrong
 *  @Date: 2020/4/24 0024 11:21
 *  @Description:类注释
 *  商家
 */
@RestController
public class UserShopController {
    @Resource
    private HomePageServiceImpl homePageService;

    @Resource
    private UserShopServiceImpl userShopService;

    @Resource
    private UserGoodServiceImpl userGoodService;


    @ApiOperation("详情")
    @RequestMapping(value = "/supplies/getUserShopInfo",method = RequestMethod.GET)
    public ThinkJSResult<Object> getCurUserShop(Long id){
        UserShop shop = this.userShopService.getCurUserShop();
        if (shop == null)
            shop = new UserShop();
        return ThinkJSResult.ok(shop);
    }
    @ApiOperation("详情")
    @RequestMapping(value = "/supplies/getShopInfo",method = RequestMethod.GET)
    public ThinkJSResult<Object> getShopInfo(Long id){
        UserShop shop = this.userShopService.getUserShop(id);
        return ThinkJSResult.ok(shop);
    }


    @ApiOperation("创建店铺")
    @CrossOrigin
    @RequestMapping(value = "/apply/shop",method = RequestMethod.POST)
    public ThinkJSResult<Object> addOrUpdate(@RequestBody UserShop userShop){
        try {
            this.userShopService.saveUserShop(userShop);
            return ThinkJSResult.ok("创建店成功！");
        }catch (Exception err){
            return ThinkJSResult.error(err.getMessage());
        }

    }
    @ApiOperation("详情")
    @RequestMapping(value = "/supplies/getCategory",method = RequestMethod.GET)
    public ThinkJSResult<Object> getCategory(Integer isSelf) {
        List<UserGoodsCategory>  list= this.userGoodService.getCategory(isSelf);
        return ThinkJSResult.ok(list);
    }


    @ApiOperation("详情")
    @RequestMapping(value = "/supplies/GetShopClassify",method = RequestMethod.GET)
    public ThinkJSResult<Object> getShopClassify(Long id) {
        UserGoodsCategory  category= this.userShopService.getShopCategroy(id);
        List<UserGoodsCategory>  list = new ArrayList<>();
        list.add(category);
        return ThinkJSResult.ok(list);
    }

    @ApiOperation("详情")
    @RequestMapping(value = "/supplies/GetClassifyGoods",method = RequestMethod.GET)
    public ThinkJSResult<Object> GetClassifyGoods(UserGoodServiceImpl.GoodsSearcnCond cond) {
        List<UserGoodServiceImpl.GoodsSKUVO>  list= this.userGoodService.getShopGoodsInfo(cond);
        return ThinkJSResult.ok(list);
    }


    @ApiOperation("详情")
    @RequestMapping(value = "/supplies/getRecommendShop",method = RequestMethod.GET)
    public ThinkJSResult<Object> getRecommendShop(PageCond cond){
        Map<String,Object> resMap= new Hashtable<String,Object>();
        List<UserShopServiceImpl.UserShopVO> shopList = this.userShopService.getRecommendShop();//还要加上地区过滤
        resMap.put("data",shopList);
        List<PageLink> bannerList= this.homePageService.getBannerList(4);
        resMap.put("banner",bannerList);
        return ThinkJSResult.ok(resMap);
    }


}
