package com.jmt.modules.revenueshare.vto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
public class PartnerWithdrawListDto {
    private int id;

    private String pOrderCode;
    @JsonProperty("pCode")
    private int pCode;
    @JsonProperty("searchTime")
    private String searchTime;

    private String time;

    private String pBankCode;

    private String pBankName;

    @NotNull(message = "输入的金额有误！")
    private Double pWithdrawMoney;

    //查询使用的开始时间
    private Date pWithdrawTimeStart;

    //查询使用的结束时间
    private Date pWithdrawTimeEnd;

    private Integer pStatus;

    /**
     * 起始页
     */
    private Integer pageNo=1;
    /**
     * 页大小
     */
    private Integer pageSize=10;
}
