package com.jmt.modules.commission.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jmt.common.api.vo.Result;
import com.jmt.modules.commission.model.dto.CmsEqPartsOrderListDto;
import com.jmt.modules.commission.model.vo.CmsEqPartsOrderInfoVo;
import com.jmt.modules.commission.model.vo.CmsEqPartsOrderListVo;

/**
 *  @author: hanshangrong
 *  @Date: 2020/4/20 0020 12:59
 *  @Description:类注释
 *  配件订单业务接口
 */
public interface CmsEqPartsOrderService {

    /**
     * 描述:  配件订单列表
     * @method  listCmsEqPartsOrder
     * @date: 2020/4/20 0020
     * @author: hanshangrong
     * @param cmsEqPartsOrderListDto
     * @return com.jmt.common.api.vo.Result<com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.jmt.modules.commission.model.vo.CmsEqPartsOrderListVo>>
     */
    Result<Page<CmsEqPartsOrderListVo>> listCmsEqPartsOrder(CmsEqPartsOrderListDto cmsEqPartsOrderListDto);

    /**
     * 描述:  获取配件订单详情
     * @method  getPartsOrderInfo
     * @date: 2020/4/20 0020
     * @author: hanshangrong
     * @param orderId
     * @return com.jmt.common.api.vo.Result<com.jmt.modules.commission.model.vo.CmsEqPartsOrderInfoVo>
     */
    Result<CmsEqPartsOrderInfoVo> getPartsOrderInfo(String orderId);


    /**
     * 描述:  发货
     * @method  sendOutGoods
     * @date: 2020/4/20 0020
     * @author: hanshangrong
     * @param orderId
     * @param logisticsNumber
     * @param logisticsName
     * @return com.jmt.common.api.vo.Result<java.lang.Object>
     */
    Result<Object> sendOutGoods(String orderId, String logisticsNumber, String logisticsName);
}
