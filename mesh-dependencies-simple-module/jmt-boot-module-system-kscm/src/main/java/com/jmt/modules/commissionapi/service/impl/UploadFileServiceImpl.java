package com.jmt.modules.commissionapi.service.impl;

import com.jmt.common.util.MultipartFileUtil;
import com.jmt.common.util.Sample;
import lombok.extern.slf4j.Slf4j;
import com.jmt.common.api.vo.Result;
import com.jmt.common.util.oss.OssBootUtil;
import com.jmt.modules.commission.entity.CmsFile;
import com.jmt.modules.commission.enums.VideoSpecificationsEnum;
import com.jmt.modules.commission.mapper.CmsFileMapper;
import com.jmt.modules.commission.util.FileMD5Util;
import com.jmt.modules.commission.util.VideoUtils;
import com.jmt.modules.commissionapi.service.UploadFileService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
@Service
@Slf4j
public class UploadFileServiceImpl implements UploadFileService {
    @Value(value = "${jmt.oss.fileDir}")
    private String fileDir;

    @Resource
    private CmsFileMapper cmsFileMapper;

    @Override
    public Result<Map<String, String>> uploadFile(MultipartFile file, Integer type) {
        Result<Map<String, String>> result = new Result<>();
        Map<String, String> mapTurn=new HashMap<>(3);
        CmsFile cmsFile=new CmsFile();
        try {
//            if(type == 0){
//                Map<String,Object> checkResult=VideoUtils.checkVideo(VideoSpecificationsEnum.VIDEO_ONE,file.getInputStream());
//                if(!(boolean)checkResult.get("result")){
//                    return result.error500((String) checkResult.get("errorMsg"));
//                }
//            }
            Map<String,String> map = OssBootUtil.uploads(file,fileDir);
            if(map == null){
                return result.error500("文件上传失败");
            }

            int lastIndex = map.get("fileName").lastIndexOf('/');
            String fileName = map.get("fileName").substring(lastIndex + 1);

            String newFileName = "C" + fileName;
            System.out.println("filename:" + map.get("fileName"));
            System.out.println(map.get("fileName").replace(fileName, newFileName));
            //转码
            String video = Sample.changeVideo("file/video/" + map.get("fileName"), "file/video/" + map.get("fileName").replace(fileName, newFileName));
            String oldFileurl = map.get("fileUrl");

            String fileUrlNew = map.get("fileUrl").replace(fileName, newFileName);

            System.out.println("fileurl======" + map.get("fileUrl").replace(fileName, newFileName));
            if (video != null) {
                map.put("fileName", newFileName);
                map.put("fileUrl", fileUrlNew);
            }

            cmsFile.setFileName(newFileName);
            cmsFile.setFileRealName(file.getOriginalFilename());
            cmsFile.setFileUrl(fileUrlNew);
            cmsFile.setFileType(type);

            String sconse = MultipartFileUtil.parseDuration(file);
            System.out.println("上传时间==::"+sconse);
            cmsFile.setFileDuration(Integer.valueOf(sconse));
            //   cmsFile.setFileDuration(Integer.parseInt((String) checkResult.get("Duration")));
            if(type == 0){
                String md5= FileMD5Util.getMD5One(file);
                cmsFile.setFileMd5(md5);
            }
            //视频上传成功 新增片源库
            cmsFileMapper.insert(cmsFile);
            mapTurn.put("id",cmsFile.getId().toString());
            mapTurn.put("url",oldFileurl);
            result.setResult(mapTurn);
            result.setCode(200);
        } catch (Exception e) {
            e.printStackTrace();
            result.error500("文件上传失败");
            log.error("文件上传失败",e);
        }
        return result;
    }

    @Override
    public Result<Map<String, String>> uploadFiles(MultipartFile file, Integer type, Integer imgType) {
        Result<Map<String, String>> result = new Result<>();
        Map<String, String> mapTurn=new HashMap<>(3);
        try {
            Map<String,String> map= OssBootUtil.uploads(file,fileDir);
            if(map == null){
                return result.error500("文件上传失败");
            }



            int lastIndex = map.get("fileName").lastIndexOf('/');
            String fileName = map.get("fileName").substring(lastIndex + 1);

            String newFileName = "C" + fileName;
            System.out.println("filename:" + map.get("fileName"));
            System.out.println(map.get("fileName").replace(fileName, newFileName));
            //转码
            String video = Sample.changeVideo("file/video/" + map.get("fileName"), "file/video/" + map.get("fileName").replace(fileName, newFileName));
            String oldFileurl = map.get("fileUrl");

            String fileUrlNew = map.get("fileUrl").replace(fileName, newFileName);

            System.out.println("fileurl======" + map.get("fileUrl").replace(fileName, newFileName));
            if (video != null) {
                map.put("fileName", newFileName);
                map.put("fileUrl", fileUrlNew);
            }


            CmsFile cmsFile=new CmsFile();
            cmsFile.setFileName(newFileName);
            cmsFile.setFileRealName(file.getOriginalFilename());
            cmsFile.setFileUrl(fileUrlNew);
            cmsFile.setFileType(type);
            cmsFile.setImgType(imgType);
            //文件上传成功
            cmsFileMapper.insert(cmsFile);
            mapTurn.put("id",cmsFile.getId().toString());
            mapTurn.put("url",oldFileurl);
            result.setResult(mapTurn);
            result.setCode(200);
        } catch (Exception e) {
            result.error500("文件上传失败");
            log.error("文件上传失败",e);
        }
        return result;
    }
}
