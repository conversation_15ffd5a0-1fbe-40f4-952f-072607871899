package com.jmt.modules.operateteam.vto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 *  @author: hanshangrong
 *  @Date: 2020/4/13 0013 16:48
 *  @Description:类注释
 *  合伙人列表
 */
@Data
public class PartnerInfoListDto {
    /**
     * 省份编码
     */
    @JsonProperty("pAreaProvinceNum")
    private Integer pAreaProvinceNum;
    /**
     * 市区编号
     */
    @JsonProperty("pAreaCityNum")
    private Integer pAreaCityNum;
    /**
     * 区
     */
    @JsonProperty("pAreaNum")
    private Integer pAreaNum;
    /**
     * 分佣编号
     */
    private Integer userCid;
    /**
     * 合伙人类型
     */
    @JsonProperty("partnerType")
    private Integer partnerType;
    /**
     * 开始时间
     */
    private String startTime;
    /**
     * 结束时间
     */
    private String sendTime;

    /**
     * 审核状态  0 待审核、1 审核中、2 驳回、3 冻结、4 正常
     */
    @JsonProperty("pStatus")
    private Integer pStatus;
    /**
     * 合同编号
     */
    private String contractNum;
    /**
     * 手机号or合伙人名称
     */
    private String like;
    /**
     * 起始页
     */
    private Integer pageNo=1;
    /**
     * 页大小
     */
    private Integer pageSize=10;
}
