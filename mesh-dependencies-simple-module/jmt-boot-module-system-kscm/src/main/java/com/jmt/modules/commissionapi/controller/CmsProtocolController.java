package com.jmt.modules.commissionapi.controller;

import io.swagger.annotations.ApiOperation;
import com.jmt.common.api.vo.Result;
import com.jmt.modules.commission.entity.CmsProtocol;
import com.jmt.modules.commissionapi.service.CmsProtocolService;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;


@RestController
@CrossOrigin
@RequestMapping("/app/protocol")
public class CmsProtocolController {

    @Resource
    private CmsProtocolService cmsProtocolServiceImpl;

    @ApiOperation("获取协议信息")
    @RequestMapping(value = "/getProtocolInfo",method = RequestMethod.GET)
    public Result<CmsProtocol> getProtocolInfo(){
        return cmsProtocolServiceImpl.getProtocolInfo();
    }
}
