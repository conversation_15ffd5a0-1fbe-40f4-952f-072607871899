package com.jmt.modules.commission.mapper;

import org.apache.ibatis.annotations.Param;
import com.jmt.modules.commission.entity.DefaultPrice;

import java.util.List;

/**
 * (DefaultPrice)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-05-27 18:17:33
 */
public interface DefaultPriceMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param  id
     * @return 实例对象
     */
    DefaultPrice queryById(Integer id);


    /**
     * 通过实体作为筛选条件查询
     *
     * @param defaultPrice 实例对象
     * @return 对象列表
     */
    List<DefaultPrice> queryAll(DefaultPrice defaultPrice);

    /**
     * 新增数据
     *
     * @param defaultPrice 实例对象
     * @return 影响行数
     */
    int insert(DefaultPrice defaultPrice);

    /**
     * 修改数据
     *
     * @param defaultPrice 实例对象
     * @return 影响行数
     */
    int update(DefaultPrice defaultPrice);

    /**
     * 通过主键删除数据
     *
     * @param  主键
     * @return 影响行数
     */
    int deleteById( );

}