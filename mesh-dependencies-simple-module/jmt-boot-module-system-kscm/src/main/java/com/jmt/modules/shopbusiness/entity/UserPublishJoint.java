package com.jmt.modules.shopbusiness.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jmt.modules.shopbusiness.entity.supper.EntityBase;
import lombok.Data;

import java.util.Date;

/*
农餐对接消息类
 */
@Data
@TableName("user_publishjoint")
public class UserPublishJoint extends EntityBase {

    /**
     * 用户id，对应userProfile里面的ID
     */
    @TableField("userId")
    private Long userId;
    /**
     * 单号，主要用于分润
     */
    private String orderNo;
    /**
     * 发布者名字
     */
    private String name;
    /**
     * 联系电话
     */
    private String phone;
    /**
     * 标题
     */
    private String title;
    /**
     * 图片("|"分割)
     */
    private String imgs;
    /**
     * 预售开始时间
     */
    private Date beginTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 具体内容
     */
    private String content;
    /**
     * 筷圣币支付
     */
    private int useBitCoin;
    /**
     * 收费
     */
    private int charge;
    /**
     * 实际支付
     */
    private int payment;
    /**
     * 状态0待审核1上架2下架
     */
    private int status;
    /**
     * 状态0待审核1上架2下架
     */
    private int commission;
    /**
     * 备注：主要用于审核意见
     */
    private String remark;

}
