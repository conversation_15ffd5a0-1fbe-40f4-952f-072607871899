package com.jmt.modules.commission.entity;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;
@ToString
@JsonAutoDetect(getterVisibility=JsonAutoDetect.Visibility.NONE)
public class CmsUserReceiving {
    @JsonProperty("id")
    private Integer id;
    @JsonProperty("uCode")
    private Integer  uCode;
    @JsonProperty("uUser")
    private String uUser;
    @JsonProperty("uPhone")
    private String uPhone;
    /**
     * 区域
     */
    @JsonProperty("area")
    @JsonIgnore
    private String area;
    @JsonProperty("uAdress")
    private String uAdress;
    @JsonProperty("uDefault")
    private Integer uDefault;
    @JsonProperty("address")
    private String address;
    @JsonProperty("areaArray")
    private String[] areaArray;
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getuCode() {
        return uCode;
    }

    public void setuCode(Integer uCode) {
        this.uCode = uCode;
    }

    public String getuUser() {
        return uUser;
    }

    public void setuUser(String uUser) {
        this.uUser = uUser;
    }

    public String getuPhone() {
        return uPhone;
    }

    public void setuPhone(String uPhone) {
        this.uPhone = uPhone;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
        this.areaArray=area.split("::");
    }

    public String getuAdress() {
        return uAdress;
    }

    public Integer getuDefault() {
        return uDefault;
    }

    public void setuDefault(Integer uDefault) {
        this.uDefault = uDefault;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public void setuAdress(String uAdress) {
        this.uAdress = uAdress;
        if(StringUtils.isNotBlank(this.area)){
            String[] strings=area.split("::");
            StringBuilder stringBuilder=new StringBuilder();
            for(int i =0,length=strings.length;i<length;i++){
                stringBuilder.append(strings[i]);
            }
            this.address=stringBuilder.toString()+uAdress;
        }
    }

}
