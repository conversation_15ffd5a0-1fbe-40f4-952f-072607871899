package com.jmt.modules.revenueshare.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jmt.common.api.vo.Result;
import com.jmt.common.constant.CommonConstant;
import com.jmt.modules.commission.entity.CmsProject;
import com.jmt.modules.commission.mapper.CmsProjectMapper;
import com.jmt.modules.commission.model.vo.CmsProjectTree;
import com.jmt.modules.commission.service.CmsProjectService;
import com.jmt.modules.revenueshare.entity.Project;
import com.jmt.modules.revenueshare.entity.PromotionFee;
import com.jmt.modules.revenueshare.mapper.ProjectMapper;
import com.jmt.modules.revenueshare.mapper.PromotionFeeMapper;
import com.jmt.modules.revenueshare.vto.ProjectTree;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;


/**
 * 分佣项目信息：可以有分类
 */
@Service
public class ProjectServiceImpl extends ServiceImpl<ProjectMapper, Project> {


    /**
     * 返回树状结构
     * @return
     */
    public Result<List<ProjectTree>> getCmsProjectTree() {
        Result<List<ProjectTree>> result=new Result<>();
        //查询根节点
        List<Project> children=baseMapper.queryAllByParentId(0);
        List<ProjectTree> cmsProjectTreeList=getNodes(children);
        result.setCode(200);
        result.setResult(cmsProjectTreeList);
        return result;
    }

    /**
     * 返回查询结果
     * @return
     */
    public Result<List<Project>> listProject() {
        List<Project> roots= baseMapper.queryRootProjects();
        Result<List<Project>> result=new Result<>();
        result.setResult(roots);
        result.setCode(200);
        return result;
    }


    /**
     *     封装节点数据
     */
    private List<ProjectTree> getNodes(List<Project> cmsProjectList){
        List<ProjectTree> cmsProjectTrees=new ArrayList<>();
        for (Project cms:cmsProjectList) {
            ProjectTree cmsProjectTree=new ProjectTree();
            cmsProjectTree.setId(cms.getId());
            cmsProjectTree.setLabel(cms.getCName());
            if(cms.getIsParent() == 1){
                List<Project> cmsProject=baseMapper.queryAllByParentId(cms.getId());
                List<ProjectTree> cmsProjectTrees1=new ArrayList<>();
                for (Project cms1:cmsProject) {
                    ProjectTree cmsProjectTree1=new ProjectTree();
                    cmsProjectTree1.setId(cms1.getId());
                    cmsProjectTree1.setLabel(cms1.getCName());
                    cmsProjectTrees1.add(cmsProjectTree1);
                }
                cmsProjectTree.setChildren(cmsProjectTrees1);
                cmsProjectTrees.add(cmsProjectTree);
                getNodes(cmsProject);
            }else{
                cmsProjectTrees.add(cmsProjectTree);
            }
        }
        return cmsProjectTrees;
    }
}
