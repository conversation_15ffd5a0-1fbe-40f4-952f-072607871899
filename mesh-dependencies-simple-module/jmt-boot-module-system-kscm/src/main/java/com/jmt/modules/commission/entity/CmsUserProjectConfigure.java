package com.jmt.modules.commission.entity;

import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 设备数量配置表(CmsUserProjectConfigure)实体类
 *
 * <AUTHOR>
 * @since 2020-04-21 16:51:46
 */
@Data
public class CmsUserProjectConfigure implements Serializable {
    private static final long serialVersionUID = 417060276829899858L;
    /**
    * id
    */    
    @JsonProperty("id")
    private Integer id;
    /**
    * 配置名称
    */    
    @JsonProperty("configureName")
    @NotBlank(message = "配置名称为空")
    private String configureName;
    /**
    * 配置设备数量
    */    
    @JsonProperty("configureEqNum")
    @NotNull(message = "配置设备数量为空")
    private Integer configureEqNum;
    /**
    * 配置缴费金额
    */    
    @JsonProperty("configureAmount")
    @NotNull(message = "配置缴费金额为空")
    private Double configureAmount;
    /**
    * 状态0：不可用 1：可用
    */    
    @JsonProperty("stauts")
    private Integer stauts;




}