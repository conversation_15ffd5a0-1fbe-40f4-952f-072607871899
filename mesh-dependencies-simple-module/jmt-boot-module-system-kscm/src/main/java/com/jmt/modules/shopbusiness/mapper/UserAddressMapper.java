package com.jmt.modules.shopbusiness.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jmt.modules.shopbusiness.entity.News;
import com.jmt.modules.shopbusiness.entity.UserAddress;
import com.jmt.modules.shopbusiness.entity.UserCard;

import java.util.List;

/**
 * 用户联系地址表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-09-15 13:55:45
 */
public interface UserAddressMapper extends BaseMapper<UserAddress> {
    /**
     * 用户联系地址列表
     * @param userId
     * @return
     */
    List<UserAddress> queryByUserId(Long userId);

    /**
     * 用户缺省联系地址
     * @param userId
     * @return
     */
    UserAddress queryDefultAddress(Long userId);

    /**
     * 联系地址分页查询
     * @param page
     * @param like
     * @return
     */
    IPage<UserAddress> getList(IPage<UserAddress> page,String like);

}