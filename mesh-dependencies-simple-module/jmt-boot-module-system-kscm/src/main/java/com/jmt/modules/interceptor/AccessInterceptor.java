package com.jmt.modules.interceptor;

import com.alibaba.fastjson.JSON;
import com.jmt.common.api.vo.Result;
import com.jmt.common.util.IPUtils;
import com.jmt.common.util.RedisUtil;
import com.jmt.modules.annotation.AccessLimit;
import com.jmt.modules.system.util.UserUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;


@Component
@Slf4j
public class AccessInterceptor extends HandlerInterceptorAdapter {

    @Resource
    private RedisUtil redisUtil;

    private final String accessKey = "ACCESS:KEY:";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if(handler instanceof HandlerMethod){
            HandlerMethod handlerMethod = (HandlerMethod) handler;
            AccessLimit accessLimit=handlerMethod.getMethodAnnotation(AccessLimit.class);
            if (accessLimit == null) {
                return true;
            }
            String uri=request.getRequestURI();
            int seconds = accessLimit.seconds();
            int maxCount = accessLimit.maxCount();
            String id="";
            String ip= IPUtils.getIpAddr(request);
            log.info("访问ip---->>{}",ip);
            if (ip != null) {
                id=ip;
            }else {
                id= UserUtil.getLoginUserId()+"";
            }
            if(!redisUtil.hasKey(accessKey+id+uri)){
                redisUtil.set(accessKey+id+uri,"1",seconds);
                return true;
            }
            int count=Integer.parseInt(redisUtil.get(accessKey+id+uri))+1;
            if (count > maxCount) {
                render(response);
                return false;
            }else {
                redisUtil.set(accessKey+id+uri,count+"");
                return true;
            }
        }
        return true;
    }

    private void render(HttpServletResponse response)throws Exception {
        response.setContentType("application/json;charset=UTF-8");
        PrintWriter out = response.getWriter();
        out.write(JSON.toJSONString(Result.ok("操作过频繁，请稍后再试")));
        out.flush();
        out.close();
    }
}
