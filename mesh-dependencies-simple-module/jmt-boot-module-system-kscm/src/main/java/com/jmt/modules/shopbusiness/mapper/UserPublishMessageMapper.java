package com.jmt.modules.shopbusiness.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jmt.modules.shopbusiness.entity.UserCode;
import com.jmt.modules.shopbusiness.entity.UserPublishMessage;

import java.util.List;

/**
 * 卖光光消息数据表Mapper
 */
public interface UserPublishMessageMapper extends BaseMapper<UserPublishMessage> {
    /**
     * 查询有效消息列表
     * @return
     */
    List<UserPublishMessage> queryValidateMessage();

    /**
     * 这个没用了
     * @param userId
     * @return
     */
    List<UserPublishMessage> queryByUserIdAndRoost(Integer userId);

    /**
     * 更新状态
     * @param id
     */
    void updateStatusById(Integer id);

    /**
     * 获得消息总数
     * @return
     */
    Integer getMsgCount();

    /**
     * 分页查询
     * @param page
     * @param status
     * @return
     */
    IPage<UserPublishMessageMapper> getList(IPage<UserPublishMessageMapper> page, Integer status);

    /**
     * 按状态统计总数
     * @param status
     * @return
     */
    Integer getStatusCount(int status);
}
