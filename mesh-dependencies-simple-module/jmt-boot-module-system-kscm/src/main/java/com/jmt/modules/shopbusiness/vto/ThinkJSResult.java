package com.jmt.modules.shopbusiness.vto;

import com.jmt.common.constant.CommonConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 *   接口返回数据格式
 * <AUTHOR>
 * @email <EMAIL>
 * @date  2019年1月19日
 */
@Data
@ApiModel(value="接口返回对象", description="接口返回对象")
public class ThinkJSResult<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 返回处理消息
     */
    @ApiModelProperty(value = "返回处理消息")
    private String errmsg = "";

    /**
     * 返回代码
     */
    @ApiModelProperty(value = "返回代码")
    private Integer errno = 0;

    /**
     * 返回数据对象 data
     */
    @ApiModelProperty(value = "返回数据对象")
    private T data;


    public ThinkJSResult<T> success(String message) {
        this.errmsg = message;
        this.errno = 0;
        return this;
    }
    public ThinkJSResult<T> success(String message,T data) {
        this.errmsg = message;
        this.errno = 0;
        this.setData(data);
        return this;
    }
    public ThinkJSResult<T> successPage(String message,T data) {
        this.errmsg = message;
        this.errno = 0;
        this.setData(data);
        return this;
    }


    public static ThinkJSResult<Object> ok() {
        ThinkJSResult<Object> r = new ThinkJSResult<Object>();
        r.setErrno(0);
        r.setErrmsg("成功");
        return r;
    }

    public static ThinkJSResult<Object> ok(String msg) {
        ThinkJSResult<Object> r = new ThinkJSResult<Object>();
        r.setErrno(0);
        r.setErrmsg(msg);
        return r;
    }

    public static ThinkJSResult<Object> ok(Object data) {
        ThinkJSResult<Object> r = new ThinkJSResult<Object>();
        r.setErrno(0);
        r.setData(data);
        return r;
    }

    public static ThinkJSResult<Object> ok(String msg, Object data) {
        ThinkJSResult<Object> r = new ThinkJSResult<Object>();
        r.setErrno(0);
        r.setErrmsg(msg);
        r.setData(data);
        return r;
    }

    public static ThinkJSResult<Object> error(String msg) {
        ThinkJSResult<Object> r = new ThinkJSResult<Object>();
        r.setErrno(200);
        r.setErrmsg(msg);
        return r;
    }

    public static ThinkJSResult<Object> error(int code, String msg) {
        ThinkJSResult<Object> r = new ThinkJSResult<Object>();
        r.setErrno(code);
        r.setErrmsg(msg);

        return r;
    }

    public ThinkJSResult<T> error500(String message) {
        this.errmsg = message;
        this.errno = CommonConstant.SC_INTERNAL_SERVER_ERROR_500;
        return this;
    }

}