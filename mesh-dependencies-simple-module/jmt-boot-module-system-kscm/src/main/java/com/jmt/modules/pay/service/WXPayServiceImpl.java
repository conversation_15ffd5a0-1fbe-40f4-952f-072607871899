package com.jmt.modules.pay.service;

import com.alibaba.fastjson.JSONObject;
import com.jmt.modules.castingapplet.util.DateUtils;
import com.jmt.modules.commission.entity.CmsEqPartsOrder;
import com.jmt.modules.commission.entity.CmsOrderLog;
import com.jmt.modules.commission.mapper.CmsEqPartsOrderDetaiMapper;
import com.jmt.modules.commission.mapper.CmsEqPartsOrderMapper;
import com.jmt.modules.commission.mapper.CmsOrderLogMapper;
import com.jmt.modules.commission.model.vo.CmsEqPartsOrderDetaiListVo;
import com.jmt.modules.commission.util.BigDecimalUtils;
import com.jmt.modules.pay.entity.OrderVO;
import com.jmt.modules.pay.entity.UniformOrderDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import com.jmt.common.api.vo.Result;
import com.jmt.common.constant.CommissionConstant;
import com.jmt.common.exception.RRException;
import com.jmt.common.util.IPUtils;
import com.jmt.modules.pay.config.WXPayConfig;
import com.jmt.modules.pay.util.wx.WXPayConstants;
import com.jmt.modules.pay.util.wx.WXPayRequest;
import com.jmt.modules.pay.util.wx.WXPayUtil;
import com.jmt.modules.pay.util.wx.WebUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

@Slf4j
@Service
public class WXPayServiceImpl implements PayService {

    @Resource
    private CmsEqPartsOrderMapper cmsEqPartsOrderMapper;

    @Resource
    private CmsEqPartsOrderDetaiMapper cmsEqPartsOrderDetaiMapper;

    @Resource
    private CmsOrderLogMapper cmsOrderLogMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<Object> uniformOrder(UniformOrderDTO uniformOrder) throws Exception {
        OrderVO orderVO = new OrderVO();
        if(CommissionConstant.ORDER_TYPE_FITTING == uniformOrder.getOrderType()){
            CmsEqPartsOrder partsOrder= cmsEqPartsOrderMapper.queryByOrderIds(uniformOrder.getOrderId());
            Optional.ofNullable(partsOrder).ifPresent(order->{
                if(partsOrder.getStatus() != CommissionConstant.FITTING_ORDER_STATUS_TO_BE_PAID){
                    throw new RRException("订单不可支付",4001);
                }
                if(partsOrder.getExpireTime().getTime() > System.currentTimeMillis()){
                    throw new RRException("订单已过期",4002);
                }
                orderVO.setUserId(order.getPId().toString());
                List<CmsEqPartsOrderDetaiListVo> orderDetais=cmsEqPartsOrderDetaiMapper.queryAllByOrder(order.getOrderid());
                StringBuilder body=new StringBuilder("配件购买");
                Optional.ofNullable(orderDetais).ifPresent(detais ->{
                    detais.forEach(detai->{
                        body.append("-").append(detai.getPartsName()).append("*").append(detai.getPartsQuantity());
                    });
                });
                orderVO.setBody(body.toString());
                orderVO.setOutTradeNo(order.getOrderid());
                if(CommissionConstant.ORDER_PAY_TYPE_BALANCE == order.getPaymentMethod()){
                    orderVO.setTotalFee(Integer.valueOf(BigDecimalUtils.mulStr(order.getOrderprice().toString(),"100")).toString());
                }else {
                    orderVO.setTotalFee(Integer.valueOf(BigDecimalUtils.mulStr(order.getCashPaymentAmount().toString(),"100")).toString());
                }
            });
        }
        if(StringUtils.isNotBlank(orderVO.getOutTradeNo())){
            Map<String, String> param = new HashMap<>(6);
            param.put("out_trade_no", orderVO.getOutTradeNo());
            param.put("body", orderVO.getBody());
            param.put("total_fee", orderVO.getTotalFee());
            param.put("spbill_create_ip", IPUtils.getIpAddr(WebUtils.getRequest()));
            param = WXPayUtil.fillRequestData(param, WXPayConstants.SignType.MD5);
            String result = WXPayRequest.requestOnce(WXPayConfig.uniformOrder, WXPayUtil.mapToXml(param), 6000, 6000, false);
            param = WXPayUtil.xmlToMap(result);
            //返回对象
            Map<String, String> resultMap = new HashMap<>(9);
            if (WXPayConstants.SUCCESS.equals(param.get("return_code"))) {
                if (WXPayConstants.SUCCESS.equals(param.get("result_code"))) {
                    resultMap.put("appId", WXPayConfig.appId);
                    resultMap.put("partnerid", WXPayConfig.mchId);
                    resultMap.put("prepayid", param.get("prepay_id"));
                    resultMap.put("noncestr", param.get("nonce_str"));
                    resultMap.put("timestamp", DateUtils.timeToStr(System.currentTimeMillis() / 1000, DateUtils.DATE_TIME_PATTERN));
                    resultMap.put("package", "Sign=WXPay");
                    resultMap.put("sign", WXPayUtil.generateSignature(resultMap, WXPayConfig.paySignKey));
                    //插入下单日志
                    CmsOrderLog orderLog = new CmsOrderLog();
                    orderLog.setOrderId(orderVO.getOutTradeNo());
                    orderLog.setUserId(orderVO.getUserId());
                    orderLog.setPayId(param.get("prepay_id"));
                    String describe=String.format("用户%s在%s通过微信下单%s,下单金额%s,微信交易号%s",orderVO.getUserId(), DateUtils.format(new Date(),DateUtils.DATE_TIME_PATTERN),
                            orderVO.getBody(),orderVO.getTotalFee(),param.get("prepay_id"));
                    orderLog.setOrderDescription(describe);
                    orderLog.setOrderType(CommissionConstant.ORDER_TYPE_FITTING);
                    orderLog.setLogType(0);
                    cmsOrderLogMapper.insert(orderLog);
                    return Result.ok("微信统一下单成功",resultMap);
                }
            }
            return Result.error(param.get("return_msg"));
        }
        return Result.error("订单不存在");
    }

    @Override
    public void callback() throws Exception {
        Map<String,String> resultMap=new HashMap<>(3);
        HttpServletResponse response=null;
        try {
            String paramStr=WXPayUtil.getRequestParamToStr(WebUtils.getRequest());
            response=WebUtils.getResponse();
            WXPayUtil.setResponse(response);
            Map<String,String> params=WXPayUtil.xmlToMap(paramStr);
            resultMap.put("return_code","FAIL");
            resultMap.put("return_msg","NO");
            if(WXPayConstants.SUCCESS.equals(params.get("return_code"))){
                if(WXPayConstants.SUCCESS.equals(params.get("result_code"))){
                    //验证签名
                    if(WXPayUtil.isSignatureValid(params,WXPayConfig.paySignKey)){








                    }else {
                        resultMap.put("return_code","FAIL");
                        resultMap.put("return_msg","签名错误");
                    }
                }
            }
        }catch (Exception e) {
            resultMap.put("return_code","FAIL");
            resultMap.put("return_msg","NO");
            log.error("处理支付回调失败",e);
        }finally {
            assert response != null;
            response.getWriter().write(WXPayUtil.mapToXml(resultMap));
        }
    }

    @Override
    public Result<Object> orderQuery(JSONObject params) {
        //查询订单
        return null;
    }
}
