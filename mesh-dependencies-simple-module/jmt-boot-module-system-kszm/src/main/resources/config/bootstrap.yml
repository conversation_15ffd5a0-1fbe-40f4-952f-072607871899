# ===================================================================
# Spring Cloud Config bootstrap configuration for the "dev" profile
# In prod profile, properties will be overwritten by the ones defined in bootstrap-prod.yml
# ===================================================================

mesh:
    application:
        name: jmt-commission-kszm
        version: 2.2.1
        base-package: com.jmt
        server-port: 10008
    registry:
        password: admin
        #server: 127.0.0.1:8867
        server: 172.28.188.105:8867
        uri: http://admin:${mesh.registry.password}@${mesh.registry.server:"172.28.188.105:8761"}/

spring:
    application:
        name: ${mesh.application.name}
    profiles:
        # The commented value for `active` can be replaced with valid Spring profiles to load.
        # Otherwise, it will be filled in by maven when building the JAR file
        # Either way, it can be overridden by `--spring.profiles.active` value passed in the commandline or `-Dspring.profiles.active` set in `JAVA_OPTS`
        active: dev
    cloud:
        config:
            fail-fast: false # if not in "prod" profile, do not force to use Spring Cloud Config
            uri: ${mesh.registry.uri}/config
            # name of the config server's property source (file.yml) that we want to use
            name: ${mesh.application.name}
            profile: dev # profile(s) of the property source
            label: master # toggle to switch to a different version of the configuration as stored in git
            # it can be set to any label, branch or commit of the configuration source Git repository
