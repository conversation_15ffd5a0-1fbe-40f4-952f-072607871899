<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.modules.commission.mapper.CmsOrderRevenueMapper">

    <resultMap type="com.jmt.modules.commission.entity.CmsOrderRevenue" id="CmsOrderRevenueMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="orderid" column="orderId" jdbcType="VARCHAR"/>
        <result property="revenueItems" column="revenue_items" jdbcType="VARCHAR"/>
        <result property="commissionAmount" column="commission_amount" jdbcType="OTHER"/>
        <result property="partnerIncome" column="partner_income" jdbcType="OTHER"/>
        <result property="restaurantRevenue" column="restaurant_revenue" jdbcType="OTHER"/>
        <result property="maintenanceFee" column="maintenance_fee" jdbcType="OTHER"/>
        <result property="recommission" column="recommission" jdbcType="OTHER"/>
        <result property="partnerName" column="partner_name" jdbcType="VARCHAR"/>
        <result property="merchantCode" column="merchant_code" jdbcType="VARCHAR"/>
        <result property="merchantName" column="merchant_name" jdbcType="VARCHAR"/>
        <result property="eqid" column="eqId" jdbcType="VARCHAR"/>
        <result property="partnerCode" column="partner_code" jdbcType="INTEGER"/>
        <result property="refereeCode" column="referee_code" jdbcType="VARCHAR"/>
        <result property="refereeName" column="referee_name" jdbcType="VARCHAR"/>
        <result property="isSynShop" column="is_syn_shop"/>
        <result property="orderDate" column="order_date" jdbcType="TIMESTAMP"/>
        <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
    </resultMap>
    <!--查询单个-->
    <select id="queryById" resultMap="CmsOrderRevenueMap">
        select
          id, orderId, revenue_items, commission_amount, partner_income, restaurant_revenue, maintenance_fee, recommission, partner_name, merchant_code, merchant_name, eqId, partner_code, referee_code, referee_name,order_date, create_date
        from cms_order_revenue
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="CmsOrderRevenueMap">
        select
          id, orderId, revenue_items, commission_amount, partner_income, restaurant_revenue, maintenance_fee, recommission, partner_name, merchant_code, merchant_name, eqId, partner_code, referee_code, referee_name,order_date, create_date
        from cms_order_revenue
        limit #{offset}, #{limit}
    </select>

    <select id="queryAllByRefereeCode" resultMap="CmsOrderRevenueMap">
        SELECT * FROM cms_order_revenue
        <where>
            <if test="refereeCode != null">
                and referee_code = #{refereeCode}
            </if>
            <if test="revenueItems != null and revenueItems!= '' and revenueItems != '全部收益'">
                and revenue_items = #{revenueItems}
            </if>
        </where>
        ORDER BY create_date DESC LIMIT #{page},#{size}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="CmsOrderRevenueMap">
        select
          id, orderId, revenue_items, commission_amount, partner_income, restaurant_revenue, maintenance_fee, recommission, partner_name, merchant_code, merchant_name, eqId, partner_code, referee_code, referee_name,order_date, create_date
        from cms_order_revenue
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="orderid != null and orderid != ''">
                and orderId = #{orderid}
            </if>
            <if test="revenueItems != null and revenueItems != ''">
                and revenue_items = #{revenueItems}
            </if>
            <if test="commissionAmount != null">
                and commission_amount = #{commissionAmount}
            </if>
            <if test="partnerIncome != null">
                and partner_income = #{partnerIncome}
            </if>
            <if test="restaurantRevenue != null">
                and restaurant_revenue = #{restaurantRevenue}
            </if>
            <if test="maintenanceFee != null">
                and maintenance_fee = #{maintenanceFee}
            </if>
            <if test="recommission != null">
                and recommission = #{recommission}
            </if>
            <if test="partnerName != null and partnerName != ''">
                and partner_name = #{partnerName}
            </if>
            <if test="merchantCode != null and merchantCode != ''">
                and merchant_code = #{merchantCode}
            </if>
            <if test="merchantName != null and merchantName != ''">
                and merchant_name = #{merchantName}
            </if>
            <if test="eqid != null and eqid != ''">
                and eqId = #{eqid}
            </if>
            <if test="partnerCode != null">
                and partner_code = #{partnerCode}
            </if>
            <if test="refereeCode != null and refereeCode != ''">
                and referee_code = #{refereeCode}
            </if>
            <if test="isSynShop != null">
                and is_syn_shop = #{isSynShop}
            </if>
            <if test="refereeName != null and refereeName != ''">
                and referee_name = #{refereeName}
            </if>
            <if test="createDate != null">
                and create_date = #{createDate}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into cms_order_revenue(
        <trim suffixOverrides=",">
            <if test="orderid != null and orderid != ''">
                orderId ,
            </if>
            <if test="revenueItems != null and revenueItems != ''">
                revenue_items ,
            </if>
            <if test="commissionAmount != null">
                commission_amount ,
            </if>
            <if test="partnerIncome != null">
                partner_income ,
            </if>
            <if test="restaurantRevenue != null">
                restaurant_revenue ,
            </if>
            <if test="maintenanceFee != null">
                maintenance_fee ,
            </if>
            <if test="recommission != null">
                recommission ,
            </if>
            <if test="partnerName != null and partnerName != ''">
                partner_name ,
            </if>
            <if test="merchantCode != null and merchantCode != ''">
                merchant_code ,
            </if>
            <if test="merchantName != null and merchantName != ''">
                merchant_name ,
            </if>
            <if test="eqid != null and eqid != ''">
                eqId ,
            </if>
            <if test="partnerCode != null">
                partner_code ,
            </if>
            <if test="refereeCode != null and refereeCode != ''">
                referee_code ,
            </if>
            <if test="refereeName != null and refereeName != ''">
                referee_name ,
            </if>
            <if test="revenueItemsId != null">
                revenue_items_id ,
            </if>
            <if test="orderDate != null">
                order_date ,
            </if>
            <if test="createDate != null">
                create_date
            </if>
        </trim>
        )
        values (
        <trim suffixOverrides=",">
            <if test="orderid != null and orderid != ''">
                #{orderid},
            </if>
            <if test="revenueItems != null and revenueItems != ''">
                #{revenueItems},
            </if>
            <if test="commissionAmount != null">
                #{commissionAmount},
            </if>
            <if test="partnerIncome != null">
                #{partnerIncome},
            </if>
            <if test="restaurantRevenue != null">
                #{restaurantRevenue},
            </if>
            <if test="maintenanceFee != null">
                #{maintenanceFee},
            </if>
            <if test="recommission != null">
                #{recommission},
            </if>
            <if test="partnerName != null and partnerName != ''">
                #{partnerName},
            </if>
            <if test="merchantCode != null and merchantCode != ''">
                #{merchantCode},
            </if>
            <if test="merchantName != null and merchantName != ''">
                #{merchantName},
            </if>
            <if test="eqid != null and eqid != ''">
                #{eqid},
            </if>
            <if test="partnerCode != null">
                #{partnerCode},
            </if>
            <if test="refereeCode != null and refereeCode != ''">
                #{refereeCode},
            </if>
            <if test="refereeName != null and refereeName != ''">
                #{refereeName},
            </if>
            <if test="revenueItemsId != null">
                #{revenueItemsId},
            </if>
            <if test="orderDate != null">
                #{orderDate},</if>
            <if test="createDate != null">
                #{createDate}</if>
        </trim>)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update cms_order_revenue
        <set>
            <if test="orderid != null and orderid != ''">
                orderId = #{orderid},
            </if>
            <if test="revenueItems != null and revenueItems != ''">
                revenue_items = #{revenueItems},
            </if>
            <if test="commissionAmount != null">
                commission_amount = #{commissionAmount},
            </if>
            <if test="partnerIncome != null">
                partner_income = #{partnerIncome},
            </if>
            <if test="restaurantRevenue != null">
                restaurant_revenue = #{restaurantRevenue},
            </if>
            <if test="maintenanceFee != null">
                maintenance_fee = #{maintenanceFee},
            </if>
            <if test="recommission != null">
                recommission = #{recommission},
            </if>
            <if test="partnerName != null and partnerName != ''">
                partner_name = #{partnerName},
            </if>
            <if test="merchantCode != null and merchantCode != ''">
                merchant_code = #{merchantCode},
            </if>
            <if test="merchantName != null and merchantName != ''">
                merchant_name = #{merchantName},
            </if>
            <if test="eqid != null and eqid != ''">
                eqId = #{eqid},
            </if>
            <if test="partnerCode != null">
                partner_code = #{partnerCode},
            </if>
            <if test="refereeCode != null and refereeCode != ''">
                referee_code = #{refereeCode},
            </if>
            <if test="refereeName != null and refereeName != ''">
                referee_name = #{refereeName},
            </if>
            <if test="isSynShop != null and isSynShop !=''">
                is_syn_shop= #{isSynShop},
            </if>
            <if test="orderDate != null">
                order_date = #{orderDate},
            </if>
            <if test="createDate != null">
                create_date = #{createDate},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from cms_order_revenue where id = #{id}
    </delete>


    <select id="listCmsOrderRevenue" resultMap="CmsOrderRevenueMap">
        SELECT id, orderId, revenue_items,revenue_items_id,SUM(commission_amount) AS commission_amount, SUM(partner_income) AS partner_income,
        SUM(restaurant_revenue) AS restaurant_revenue, SUM(maintenance_fee) AS maintenance_fee, SUM(recommission) AS recommission, partner_name, merchant_code,
        merchant_name, eqId, partner_code, referee_code, referee_name,order_date,create_date FROM cms_order_revenue
        <where>
            <if test="cms.partner!=null and cms.partner!=''">
                and (partner_name like concat('%',#{cms.partner},'%')
                or partner_code = #{cms.partner})
            </if>
            <if test="cms.startDate != null and cms.startDate != ''">
                and create_date >= concat(#{cms.startDate},' 00.00.00')
            </if>
            <if test="cms.sendDate != null and cms.sendDate != ''">
                and create_date &lt;= concat(#{cms.sendDate},' 23.59.59')
            </if>
            <if test="cms.incomeItem != null and cms.incomeItem !=''">
                and revenue_items = #{cms.incomeItem}
            </if>
        </where>
        group by  partner_code,revenue_items
        ORDER BY order_date DESC
    </select>

    <select id="listRevenueDetails" resultMap="CmsOrderRevenueMap">
        select
          id, orderId, revenue_items, commission_amount, partner_income, restaurant_revenue, maintenance_fee, recommission, partner_name, merchant_code, merchant_name, eqId, partner_code, referee_code, referee_name,order_date as create_date
        from cms_order_revenue
        <where>
            <if test="cms.partnerNumber != null">
               and partner_code = #{cms.partnerNumber}
            </if>
            <if test="cms.incomeItem != null and cms.incomeItem !=''">
                and revenue_items = #{cms.incomeItem}
            </if>
            <if test="cms.startDate != null and cms.startDate != ''">
                and order_date >= concat(#{cms.startDate},' 00.00.00')
            </if>
            <if test="cms.sendDate != null and cms.sendDate != ''">
                and order_date &lt;= concat(#{cms.sendDate},' 23.59.59')
            </if>
            <if test="cms.mname != null and cms.mname !=''">
                and merchant_name like concat('%',#{cms.mname},'%')
            </if>
        </where>
        order by order_date desc
    </select>

    <select id="checkEarnings" resultType="hashmap">
        SELECT  IFNULL(SUM(IFNULL(partner_income,0)),0) AS PartnerIncome,
         IFNULL(SUM(IFNULL(restaurant_revenue,0))+ IFNULL(SUM(IFNULL(recommission,0)),0),0) AS RestaurantRevenue, IFNULL(SUM(IFNULL(maintenance_fee,0)),0) AS MaintenanceFee,
         (SELECT IFNULL(SUM(partner_income),0) FROM cms_order_revenue
          WHERE partner_code = #{pid} AND DATE_FORMAT(create_date,'%Y-%m-%d') = DATE_SUB(CURDATE(),INTERVAL 1 DAY)) AS Yesterday
        FROM cms_order_revenue  WHERE partner_code = #{pid}
    </select>

    <select id="shopYdayIncome" resultType="decimal">
        SELECT IFNULL(SUM(restaurant_revenue),0) AS RestaurantRevenue FROM cms_order_revenue
        WHERE merchant_code = #{mCode} AND DATE_FORMAT(create_date,'%Y-%m-%d') = DATE_SUB(CURDATE(),INTERVAL 1 DAY)
    </select>

    <select id="queryProjectIncome" resultType="decimal">
        SELECT IFNULL(SUM(IFNULL(restaurant_revenue,0)),0) FROM cms_order_revenue
        WHERE merchant_code = #{mCode} AND revenue_items = #{userCName}
    </select>

    <select id="listOrderRevenueApp" resultType="com.jmt.modules.commission.model.vo.CmsOrderRevenueListAppVo">
        SELECT id, orderId, revenue_items,partner_income,
        IFNULL(restaurant_revenue,0)+IFNULL(recommission,0) AS RestaurantRevenue, maintenance_fee, order_date as createDate
        FROM cms_order_revenue
        <where>
            partner_code = #{cms.pid}
            <if test="cms.date != null and cms.date != ''">
               AND DATE_FORMAT(order_date,'%Y-%m') = #{cms.date}
            </if>
            <if test="cms.projectName!=null and cms.projectName!=''">
                and revenue_items = #{cms.projectName}
            </if>
        </where>
        order by order_date desc
    </select>

    <select id="listOrderRevenueApp2" resultType="com.jmt.modules.commission.model.vo.CmsOrderRevenueListAppVo">
        SELECT
        id,
        orderId,
        revenue_items,
        0 AS RestaurantRevenue,
        maintenance_fee as partner_income,
        order_date AS createDate
        FROM
        cms_order_revenue
        <where>
             merchant_code in   (select m_code from view_shop_partner where p_code=#{cms.pid})
            <if test="cms.date != null and cms.date != ''">
                AND DATE_FORMAT(order_date,'%Y-%m') = #{cms.date}
            </if>
            <if test="cms.projectName!=null and cms.projectName!=''">
                and revenue_items = #{cms.projectName}
            </if>
        </where>
        order by order_date desc
    </select>

    <select id="queryPartnerIncome" resultType="hashmap">
        SELECT IFNULL(SUM(partner_income),0) AS totalRevenue,
        IFNULL(SUM(IFNULL(restaurant_revenue,0)),0) AS merchantIncome,
        (
        SELECT IFNULL(SUM(partner_income),0) FROM cms_order_revenue
        WHERE partner_code = #{pId} AND DATE_FORMAT(create_date,'%Y-%m-%d') = DATE_SUB(CURDATE(),INTERVAL 1 DAY)
        ) AS todaySEarnings
         FROM cms_order_revenue WHERE partner_code = #{pId}
    </select>

    <select id="queryMaintenanceIncome" resultType="hashmap">
        SELECT IFNULL(SUM(maintenance_fee),0) AS totalRevenue,
        IFNULL(SUM(IFNULL(restaurant_revenue,0)),0) AS merchantIncome,
        (
        SELECT IFNULL(SUM(maintenance_fee),0) FROM cms_order_revenue
        WHERE merchant_code in   (select m_code from view_shop_partner where p_code=#{cms.pid})
        AND DATE_FORMAT(create_date,'%Y-%m-%d') = DATE_SUB(CURDATE(),INTERVAL 1 DAY)
        ) AS todaySEarnings
         FROM cms_order_revenue
         WHERE merchant_code in   (select m_code from view_shop_partner where p_code=#{cms.pid})
    </select>

    <select id="queryTheYieldCurve" resultType="com.jmt.modules.commission.model.vo.IncomeListVo">
            SELECT
            x.`date`,
            IFNULL( d.PartnerIncome, 0 ) AS PartnerIncome
            FROM
            (
            SELECT (@i :=CASE COUNT(*) WHEN 0 THEN -1 END) FROM cms_order_revenue WHERE orderId = '0000'
            ) n,
            (
            SELECT
            DATE_FORMAT( DATE_SUB( NOW(), INTERVAL ( @i := @i+1) DAY ), '%Y-%m-%d' ) AS `date`
            FROM
            (SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6) xc1,
            (SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6) xc2,
            (SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6) xc3,
            (SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6) xc4
            WHERE
            @i &lt;= #{day}
            ) X
            LEFT JOIN
            (
            SELECT
            DATE( o.create_date ) AS create_date,
            SUM(partner_income) AS PartnerIncome
            FROM
            `cms_order_revenue` o WHERE partner_code = #{pId}
            GROUP BY
            DATE( o.create_date )
            ) d ON TO_DAYS( x.`date` ) = TO_DAYS( DATE( d.create_date ) )
            ORDER BY
            x.`date`

    </select>

    <select id="queryTotalRevenue" resultType="com.jmt.modules.commission.model.vo.RevenueBreakdownVo">
        SELECT c.c_name AS incomeName,SUM(IFNULL(commission_amount,0)) AS totalRevenue,
        SUM(IFNULL(maintenance_fee,0)) AS headquartersRevenue,
        SUM(IFNULL(partner_income,0)) AS partnerIncome,
        SUM(IFNULL(restaurant_revenue,0)) + SUM(IFNULL(recommission,0)) AS merchantIncome
         FROM (
            SELECT * FROM cms_project WHERE c_parent_id = #{id}
        ) c LEFT JOIN cms_order_revenue o ON c.id = revenue_items_id GROUP BY c.id
    </select>

    <select id="queryCommissionProjectIncome" resultType="hashmap">
        SELECT SUM(IFNULL(commission_amount,0)) AS totalRevenue,
        SUM(IFNULL(maintenance_fee,0)) AS headquartersRevenue
         FROM (
            SELECT * FROM cms_project WHERE c_parent_id = #{id}
        ) c LEFT JOIN cms_order_revenue o ON c.id = revenue_items_id
    </select>

    <select id="queryYesterdaySMerchantIncome" resultType="com.jmt.modules.commission.model.vo.BShopIncome">
        SELECT merchant_code AS mCode,SUM(restaurant_revenue)*100 AS money FROM cms_order_revenue
        WHERE is_syn_shop !=1 GROUP BY merchant_code
    </select>

    <select id="queryPartnerYesterDayIncome" resultType="decimal">
        SELECT IFNULL(SUM(partner_income),0) FROM cms_order_revenue
        WHERE DATE_FORMAT(create_date,'%Y-%m-%d') = DATE_SUB(CURDATE(),INTERVAL 1 DAY) and partner_code = #{pSponsorCode}  GROUP BY partner_code
    </select>

    <select id="queryByDate" resultMap="CmsOrderRevenueMap">
        SELECT * FROM cms_order_revenue WHERE revenue_items_id = #{revenueItemsId} AND order_date &gt;= #{startTime} AND order_date &lt;= #{endTime}
    </select>
    
    <select id="queryShopRanking" resultType="com.jmt.modules.commission.model.vo.RankingVo">
        SELECT merchant_code,
               merchant_name,
               ( SELECT count( * ) FROM cms_eq b WHERE b.m_code = a.merchant_code ) AS eq,
               SUM( commission_amount ) / ( SELECT count( * ) FROM cms_eq b WHERE b.m_code = a.merchant_code ) AS money
        FROM cms_order_revenue a
        WHERE merchant_code IS NOT NULL
            AND ( SELECT count( * ) FROM cms_eq b WHERE b.m_code = a.merchant_code ) > 0
            AND merchant_code IN (
                SELECT m_code FROM cms_shop_info WHERE p_sub_code = #{partnerCode}
            )
        GROUP BY merchant_code
        ORDER BY money DESC
    </select>

    <select id="queryBySum" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(commission_amount),0) FROM cms_order_revenue WHERE referee_code = #{pCode} and revenue_items = #{revenueItems}
    </select>

    <select id="statIncomeOfPartener2" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(maintenance_fee),0) FROM cms_order_revenue
        WHERE merchant_code in (select m_code from view_shop_partner WHERE p_code = #{pCode})
        <if test="dateStart != null and dateStart != ''">
            and create_date >= concat(#{dateStart},' 00.00.00')
        </if>
        <if test="dateEnd != null and dateEnd != ''">
            and create_date &lt;= concat(#{dateEnd},' 23.59.59')
        </if>
    </select>

    <select id="statShopIncomeOfPartener2" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(commission_amount),0) FROM cms_order_revenue
        WHERE merchant_code in (select m_code from view_shop_partner WHERE p_code = #{pCode})
        <if test="dateStart != null and dateStart != ''">
            and create_date >= concat(#{dateStart},' 00.00.00')
        </if>
        <if test="dateEnd != null and dateEnd != ''">
            and create_date &lt;= concat(#{dateEnd},' 23.59.59')
        </if>
    </select>

    <select id="statIncomeOfPartener1" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(partner_income),0) FROM cms_order_revenue
        WHERE merchant_code in (select m_code from cms_shop_info WHERE p_sub_code = #{pCode})
        <if test="dateStart != null and dateStart != ''">
            and create_date >= concat(#{dateStart},' 00.00.00')
        </if>
        <if test="dateEnd != null and dateEnd != ''">
            and create_date &lt;= concat(#{dateEnd},' 23.59.59')
        </if>
    </select>

    <select id="statShopIncomeOfPartener1" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(commission_amount),0) FROM cms_order_revenue
        WHERE merchant_code in (select m_code from cms_shop_info WHERE p_sub_code =#{pCode})
        <if test="dateStart != null and dateStart != ''">
            and create_date >= concat(#{dateStart},' 00.00.00')
        </if>
        <if test="dateEnd != null and dateEnd != ''">
        and create_date &lt;= concat(#{dateEnd},' 23.59.59')
        </if>
    </select>
    <select id="statHasIncomeShopCountOfPartener1" resultType="java.lang.Integer">
        SELECT IFNULL(COUNT( DISTINCT merchant_code),0) FROM cms_order_revenue
        WHERE merchant_code in (SELECT m_code FROM cms_shop_info WHERE p_sub_code  = #{pCode})
    </select>

    <select id="statIncomeGroupbyDateOfPartener2" resultType="com.jmt.modules.commission.model.vo.IncomeListVo">
       SELECT SUM(maintenance_fee) as partnerIncome,DATE_FORMAT(create_date,'%Y-%m-%d') as date from cms_order_revenue
        WHERE merchant_code in (select m_code from view_shop_partner WHERE p_code = #{pCode})
        <if test="dateStart != null and dateStart != ''">
            and create_date >= concat(#{dateStart},' 00.00.00')
        </if>
        GROUP BY date
    </select>
    <select id="statIncomeGroupbyDateOfPartener1" resultType="com.jmt.modules.commission.model.vo.IncomeListVo">
        SELECT SUM(maintenance_fee) as partnerIncome,DATE_FORMAT(create_date,'%Y-%m-%d') as date from cms_order_revenue
        WHERE merchant_code in (SELECT m_code FROM cms_shop_info WHERE p_sub_code  = #{pCode})
        <if test="dateStart != null and dateStart != ''">
            and create_date >= concat(#{dateStart},' 00.00.00')
        </if>
        GROUP BY date
    </select>

</mapper>