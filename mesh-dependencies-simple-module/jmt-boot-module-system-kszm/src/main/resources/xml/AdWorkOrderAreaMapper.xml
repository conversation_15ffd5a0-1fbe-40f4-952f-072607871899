<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.modules.castingapplet.mapper.AdWorkOrderAreaMapper">

    <resultMap type="com.jmt.modules.castingapplet.entity.AdWorkOrderArea" id="AdWorkOrderAreaMap">
        <result property="adJobNum" column="AD_JOB_NUM" jdbcType="VARCHAR"/>
        <result property="adAreaProvince" column="AD_AREA_PROVINCE" jdbcType="VARCHAR"/>
        <result property="adAreaCity" column="AD_AREA_CITY" jdbcType="VARCHAR"/>
        <result property="adAreaCounty" column="AD_AREA_COUNTY" jdbcType="VARCHAR"/>
        <result property="adAreaProvinceNum" column="AD_AREA_PROVINCE_NUM" jdbcType="VARCHAR"/>
        <result property="adAreaCityNum" column="AD_AREA_CITY_NUM" jdbcType="VARCHAR"/>
        <result property="adAreaCountyNum" column="AD_AREA_COUNTY_NUM" jdbcType="VARCHAR"/>
        <result property="bCode" column="B_CODE" jdbcType="VARCHAR"/>
        <result property="eqCode" column="EQ_CODE" jdbcType="VARCHAR"/>
        <result property="adEqTypeANum" column="AD_EQ_TYPE_A_NUM" jdbcType="INTEGER"/>
        <result property="adEqTypeBNum" column="AD_EQ_TYPE_B_NUM" jdbcType="INTEGER"/>
        <result property="adEqTypeCNum" column="AD_EQ_TYPE_C_NUM" jdbcType="INTEGER"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="AdWorkOrderAreaMap">
        select
          AD_JOB_NUM, AD_AREA_PROVINCE, AD_AREA_CITY, AD_AREA_COUNTY, AD_AREA_PROVINCE_NUM, AD_AREA_CITY_NUM, AD_AREA_COUNTY_NUM, B_CODE, EQ_CODE, AD_EQ_TYPE_A_NUM, AD_EQ_TYPE_B_NUM, AD_EQ_TYPE_C_NUM
        from ad_work_order_area
        where  = #{adJobNum}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="AdWorkOrderAreaMap">
        select
          AD_JOB_NUM, AD_AREA_PROVINCE, AD_AREA_CITY, AD_AREA_COUNTY, AD_AREA_PROVINCE_NUM, AD_AREA_CITY_NUM, AD_AREA_COUNTY_NUM, B_CODE, EQ_CODE, AD_EQ_TYPE_A_NUM, AD_EQ_TYPE_B_NUM, AD_EQ_TYPE_C_NUM
        from ad_work_order_area
        limit #{offset}, #{limit}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="AdWorkOrderAreaMap">
        select
          AD_JOB_NUM, AD_AREA_PROVINCE, AD_AREA_CITY, AD_AREA_COUNTY, AD_AREA_PROVINCE_NUM, AD_AREA_CITY_NUM, AD_AREA_COUNTY_NUM, B_CODE, EQ_CODE, AD_EQ_TYPE_A_NUM, AD_EQ_TYPE_B_NUM, AD_EQ_TYPE_C_NUM
        from ad_work_order_area
        <where>
            <if test="adJobNum != null and adJobNum != ''">
                and AD_JOB_NUM = #{adJobNum}
            </if>
            <if test="adAreaProvince != null and adAreaProvince != ''">
                and AD_AREA_PROVINCE = #{adAreaProvince}
            </if>
            <if test="adAreaCity != null and adAreaCity != ''">
                and AD_AREA_CITY = #{adAreaCity}
            </if>
            <if test="adAreaCounty != null and adAreaCounty != ''">
                and AD_AREA_COUNTY = #{adAreaCounty}
            </if>
            <if test="adAreaProvinceNum != null and adAreaProvinceNum != ''">
                and AD_AREA_PROVINCE_NUM = #{adAreaProvinceNum}
            </if>
            <if test="adAreaCityNum != null and adAreaCityNum != ''">
                and AD_AREA_CITY_NUM = #{adAreaCityNum}
            </if>
            <if test="adAreaCountyNum != null and adAreaCountyNum != ''">
                and AD_AREA_COUNTY_NUM = #{adAreaCountyNum}
            </if>
            <if test="bCode != null and bCode != ''">
                and B_CODE = #{bCode}
            </if>
            <if test="eqCode != null and eqCode != ''">
                and EQ_CODE = #{eqCode}
            </if>
            <if test="adEqTypeANum != null">
                and AD_EQ_TYPE_A_NUM = #{adEqTypeANum}
            </if>
            <if test="adEqTypeBNum != null">
                and AD_EQ_TYPE_B_NUM = #{adEqTypeBNum}
            </if>
            <if test="adEqTypeCNum != null">
                and AD_EQ_TYPE_C_NUM = #{adEqTypeCNum}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="" useGeneratedKeys="true">
        insert into ad_work_order_area(
        <trim suffixOverrides=",">
            <if test="adJobNum != null and adJobNum != ''">
                AD_JOB_NUM ,
            </if>
            <if test="adAreaProvince != null and adAreaProvince != ''">
                AD_AREA_PROVINCE ,
            </if>
            <if test="adAreaCity != null and adAreaCity != ''">
                AD_AREA_CITY ,
            </if>
            <if test="adAreaCounty != null and adAreaCounty != ''">
                AD_AREA_COUNTY ,
            </if>
            <if test="adAreaProvinceNum != null and adAreaProvinceNum != ''">
                AD_AREA_PROVINCE_NUM ,
            </if>
            <if test="adAreaCityNum != null and adAreaCityNum != ''">
                AD_AREA_CITY_NUM ,
            </if>
            <if test="adAreaCountyNum != null and adAreaCountyNum != ''">
                AD_AREA_COUNTY_NUM ,
            </if>
            <if test="bCode != null and bCode != ''">
                B_CODE ,
            </if>
            <if test="eqCode != null and eqCode != ''">
                EQ_CODE ,
            </if>
            <if test="adEqTypeANum != null">
                AD_EQ_TYPE_A_NUM ,
            </if>
            <if test="adEqTypeBNum != null">
                AD_EQ_TYPE_B_NUM ,
            </if>
            <if test="adEqTypeCNum != null">
                AD_EQ_TYPE_C_NUM </if>
        </trim>
        )
        values (
        <trim suffixOverrides=",">
            <if test="adJobNum != null and adJobNum != ''">
                #{adJobNum},
            </if>
            <if test="adAreaProvince != null and adAreaProvince != ''">
                #{adAreaProvince},
            </if>
            <if test="adAreaCity != null and adAreaCity != ''">
                #{adAreaCity},
            </if>
            <if test="adAreaCounty != null and adAreaCounty != ''">
                #{adAreaCounty},
            </if>
            <if test="adAreaProvinceNum != null and adAreaProvinceNum != ''">
                #{adAreaProvinceNum},
            </if>
            <if test="adAreaCityNum != null and adAreaCityNum != ''">
                #{adAreaCityNum},
            </if>
            <if test="adAreaCountyNum != null and adAreaCountyNum != ''">
                #{adAreaCountyNum},
            </if>
            <if test="bCode != null and bCode != ''">
                #{bCode},
            </if>
            <if test="eqCode != null and eqCode != ''">
                #{eqCode},
            </if>
            <if test="adEqTypeANum != null">
                #{adEqTypeANum},
            </if>
            <if test="adEqTypeBNum != null">
                #{adEqTypeBNum},
            </if>
            <if test="adEqTypeCNum != null">
                #{adEqTypeCNum}</if>
        </trim>)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update ad_work_order_area
        <set>
            <if test="adJobNum != null and adJobNum != ''">
                AD_JOB_NUM = #{adJobNum},
            </if>
            <if test="adAreaProvince != null and adAreaProvince != ''">
                AD_AREA_PROVINCE = #{adAreaProvince},
            </if>
            <if test="adAreaCity != null and adAreaCity != ''">
                AD_AREA_CITY = #{adAreaCity},
            </if>
            <if test="adAreaCounty != null and adAreaCounty != ''">
                AD_AREA_COUNTY = #{adAreaCounty},
            </if>
            <if test="adAreaProvinceNum != null and adAreaProvinceNum != ''">
                AD_AREA_PROVINCE_NUM = #{adAreaProvinceNum},
            </if>
            <if test="adAreaCityNum != null and adAreaCityNum != ''">
                AD_AREA_CITY_NUM = #{adAreaCityNum},
            </if>
            <if test="adAreaCountyNum != null and adAreaCountyNum != ''">
                AD_AREA_COUNTY_NUM = #{adAreaCountyNum},
            </if>
            <if test="bCode != null and bCode != ''">
                B_CODE = #{bCode},
            </if>
            <if test="eqCode != null and eqCode != ''">
                EQ_CODE = #{eqCode},
            </if>
            <if test="adEqTypeANum != null">
                AD_EQ_TYPE_A_NUM = #{adEqTypeANum},
            </if>
            <if test="adEqTypeBNum != null">
                AD_EQ_TYPE_B_NUM = #{adEqTypeBNum},
            </if>
            <if test="adEqTypeCNum != null">
                AD_EQ_TYPE_C_NUM = #{adEqTypeCNum},
            </if>
        </set>
        where  = #{adJobNum}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from ad_work_order_area where  = #{adJobNum}
    </delete>

</mapper>