<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.modules.castingapplet.mapper.AdvertisingOrderMapper">

    <resultMap type="com.jmt.modules.castingapplet.entity.AdvertisingOrder" id="AdvertisingOrderMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="orderId" column="order_id" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="orderMp4Url" column="order_mp4_url" jdbcType="VARCHAR"/>
        <result property="orderMp4Md5" column="order_mp4_md5" jdbcType="VARCHAR"/>
        <result property="orderMp4Duration" column="order_mp4_duration" javaType="INTEGER"/>
        <result property="launchStartTime" column="launch_start_time" jdbcType="OTHER"/>
        <result property="launchSendTime" column="launch_send_time" jdbcType="OTHER"/>
        <result property="launchRestaurant" column="launch_restaurant" jdbcType="INTEGER"/>
        <result property="launchEquipment" column="launch_equipment" jdbcType="INTEGER"/>
        <result property="totalAmount" column="total_amount" jdbcType="OTHER"/>
        <result property="discountId" column="discount_id" jdbcType="INTEGER"/>
        <result property="orderStatus" column="order_status" jdbcType="INTEGER"/>
        <result property="expirationTime" column="expiration_time" jdbcType="TIMESTAMP"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="AdvertisingOrderMap">
        select
          id, order_id, user_id, order_mp4_url, order_mp4_md5,order_mp4_duration, launch_start_time, launch_send_time, launch_restaurant, launch_equipment, total_amount, discount_id, order_status, expiration_time, create_time, update_time
        from advertising_order
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="AdvertisingOrderMap">
        select
          id, order_id, user_id, order_mp4_url, order_mp4_md5, launch_start_time, launch_send_time, launch_restaurant, launch_equipment, total_amount, discount_id, order_status, expiration_time, create_time, update_time
        from advertising_order
        limit #{offset}, #{limit}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="AdvertisingOrderMap">
        select
          id, order_id, user_id, order_mp4_url, order_mp4_md5, launch_start_time, launch_send_time, launch_restaurant, launch_equipment, total_amount, discount_id, order_status, expiration_time, create_time, update_time
        from advertising_order
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="orderId != null and orderId != ''">
                and order_id = #{orderId}
            </if>
            <if test="userId != null and userId != ''">
                and user_id = #{userId}
            </if>
            <if test="orderMp4Url != null and orderMp4Url != ''">
                and order_mp4_url = #{orderMp4Url}
            </if>
            <if test="orderMp4Md5 != null and orderMp4Md5 != ''">
                and order_mp4_md5 = #{orderMp4Md5}
            </if>
            <if test="launchStartTime != null">
                and launch_start_time = #{launchStartTime}
            </if>
            <if test="launchSendTime != null">
                and launch_send_time = #{launchSendTime}
            </if>
            <if test="launchRestaurant != null">
                and launch_restaurant = #{launchRestaurant}
            </if>
            <if test="launchEquipment != null">
                and launch_equipment = #{launchEquipment}
            </if>
            <if test="totalAmount != null">
                and total_amount = #{totalAmount}
            </if>
            <if test="discountId != null">
                and discount_id = #{discountId}
            </if>
            <if test="orderStatus != null">
                and order_status = #{orderStatus}
            </if>
            <if test="expirationTime != null">
                and expiration_time = #{expirationTime}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into advertising_order(
        <trim suffixOverrides=",">
            <if test="orderId != null and orderId != ''">
                order_id ,
            </if>
            <if test="userId != null and userId != ''">
                user_id ,
            </if>
            <if test="orderMp4Url != null and orderMp4Url != ''">
                order_mp4_url ,
            </if>
            <if test="orderMp4Md5 != null and orderMp4Md5 != ''">
                order_mp4_md5 ,
            </if>
            <if test="orderMp4Duration !=null">
                order_mp4_md5 ,
            </if>
            <if test="launchStartTime != null">
                launch_start_time ,
            </if>
            <if test="launchSendTime != null">
                launch_send_time ,
            </if>
            <if test="launchRestaurant != null">
                launch_restaurant ,
            </if>
            <if test="launchEquipment != null">
                launch_equipment ,
            </if>
            <if test="totalAmount != null">
                total_amount ,
            </if>
            <if test="discountId != null">
                discount_id ,
            </if>
            <if test="orderStatus != null">
                order_status ,
            </if>
            <if test="expirationTime != null">
                expiration_time ,
            </if>
            <if test="createTime != null">
                create_time ,
            </if>
            <if test="updateTime != null">
                update_time </if>
        </trim>
        )
        values (
        <trim suffixOverrides=",">
            <if test="orderId != null and orderId != ''">
                #{orderId},
            </if>
            <if test="userId != null and userId != ''">
                #{userId},
            </if>
            <if test="orderMp4Url != null and orderMp4Url != ''">
                #{orderMp4Url},
            </if>
            <if test="orderMp4Md5 != null and orderMp4Md5 != ''">
                #{orderMp4Md5},
            </if>
            <if test="orderMp4Duration != null">
                #{orderMp4Duration} ,
            </if>
            <if test="launchStartTime != null">
                #{launchStartTime},
            </if>
            <if test="launchSendTime != null">
                #{launchSendTime},
            </if>
            <if test="launchRestaurant != null">
                #{launchRestaurant},
            </if>
            <if test="launchEquipment != null">
                #{launchEquipment},
            </if>
            <if test="totalAmount != null">
                #{totalAmount},
            </if>
            <if test="discountId != null">
                #{discountId},
            </if>
            <if test="orderStatus != null">
                #{orderStatus},
            </if>
            <if test="expirationTime != null">
                #{expirationTime},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateTime != null">
                #{updateTime}</if>
        </trim>)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update advertising_order
        <set>
            <if test="orderId != null and orderId != ''">
                order_id = #{orderId},
            </if>
            <if test="userId != null and userId != ''">
                user_id = #{userId},
            </if>
            <if test="orderMp4Url != null and orderMp4Url != ''">
                order_mp4_url = #{orderMp4Url},
            </if>
            <if test="orderMp4Md5 != null and orderMp4Md5 != ''">
                order_mp4_md5 = #{orderMp4Md5},
            </if>
            <if test="launchStartTime != null">
                launch_start_time = #{launchStartTime},
            </if>
            <if test="launchSendTime != null">
                launch_send_time = #{launchSendTime},
            </if>
            <if test="launchRestaurant != null">
                launch_restaurant = #{launchRestaurant},
            </if>
            <if test="launchEquipment != null">
                launch_equipment = #{launchEquipment},
            </if>
            <if test="totalAmount != null">
                total_amount = #{totalAmount},
            </if>
            <if test="discountId != null">
                discount_id = #{discountId},
            </if>
            <if test="orderStatus != null">
                order_status = #{orderStatus},
            </if>
            <if test="expirationTime != null">
                expiration_time = #{expirationTime},
            </if>
            <if test="income != null">
                income = #{income},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from advertising_order where id = #{id}
    </delete>

    <select id="listOrder" resultType="com.jmt.modules.castingapplet.model.vo.AdvertisingOrderListVo">
        select
          id, order_id, user_id, order_mp4_url, order_mp4_md5, launch_start_time, launch_send_time, launch_restaurant, launch_equipment, total_amount, discount_id, order_status, expiration_time, create_time, update_time
        from advertising_order
        <where>
            user_id = #{map.userId}
            <if test="map.status!=null and map.status!=''">
                and order_status = #{map.status}
            </if>
            order by create_time desc
        </where>
    </select>

    <select id="queryByOrderId" resultMap="AdvertisingOrderMap">
        select
        id, order_id, user_id, order_mp4_url, order_mp4_md5,order_mp4_duration, launch_start_time, launch_send_time, launch_restaurant, launch_equipment, total_amount, discount_id, order_status, expiration_time, create_time, update_time
        from advertising_order
        where order_id = #{orderId}
    </select>

    <update id="updateStatusByOrderId" >
        update advertising_order set order_status = #{status} where order_id = #{orderId}
    </update>

    <select id="queryAllByStatus" resultMap="AdvertisingOrderMap">
        select
        id, order_id, user_id, order_mp4_url, order_mp4_md5,order_mp4_duration, launch_start_time, launch_send_time, launch_restaurant, launch_equipment, total_amount, discount_id, order_status, expiration_time, create_time, update_time
        from advertising_order
        where order_status = #{status} and income = 0

    </select>
</mapper>