<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.modules.commission.mapper.UserPointsMapper">

    <resultMap id="BaseResultMap" type="com.jmt.modules.commission.entity.UserPoints">
        <!--@Table user_points-->
        <result property="id" column="Id" jdbcType="INTEGER"/>
        <result property="points" column="Points" jdbcType="INTEGER"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="BaseResultMap">
        select
          Id, Points
        from user_points
        where Id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="BaseResultMap">
        select
          Id, Points
        from jmtbside.user_points
        limit #{offset}, #{limit}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="BaseResultMap">
        select
          Id, Points
        from user_points
        <where>
            <if test="id != null">
                and Id = #{id}
            </if>
            <if test="points != null">
                and Points = #{points}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into user_points(Id,Points)
        values (#{id},#{points})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update user_points
        <set>
            <if test="points != null">
                Points = #{points},
            </if>
        </set>
        where Id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from user_points where Id = #{id}
    </delete>

</mapper>