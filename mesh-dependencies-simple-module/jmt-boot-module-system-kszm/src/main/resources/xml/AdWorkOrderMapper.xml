<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.modules.castingapplet.mapper.AdWorkOrderMapper">

    <resultMap type="com.jmt.modules.castingapplet.entity.AdWorkOrder" id="AdWorkOrderMap">
        <result property="id" column="ID" jdbcType="INTEGER"/>
        <result property="adJobNum" column="AD_JOB_NUM" jdbcType="VARCHAR"/>
        <result property="adContractNum" column="AD_CONTRACT_NUM" jdbcType="VARCHAR"/>
        <result property="adCustomName" column="AD_CUSTOM_NAME" jdbcType="VARCHAR"/>
        <result property="adCustomPhone" column="AD_CUSTOM_PHONE" jdbcType="VARCHAR"/>
        <result property="adCustomLiaison" column="AD_CUSTOM_LIAISON" jdbcType="VARCHAR"/>
        <result property="adBusinessLicense" column="AD_BUSINESS_LICENSE" jdbcType="VARCHAR"/>
        <result property="adAmountMoney" column="AD_AMOUNT_MONEY" jdbcType="NUMERIC"/>
        <result property="adFilmId" column="AD_FILM_ID" jdbcType="INTEGER"/>
        <result property="adEqTypeA" column="AD_EQ_TYPE_A" jdbcType="INTEGER"/>
        <result property="adEqTypeB" column="AD_EQ_TYPE_B" jdbcType="INTEGER"/>
        <result property="adEqTypeC" column="AD_EQ_TYPE_C" jdbcType="INTEGER"/>
        <result property="adCreatorName" column="AD_CREATOR_NAME" jdbcType="VARCHAR"/>
        <result property="adCreatorNum" column="AD_CREATOR_NUM" jdbcType="VARCHAR"/>
        <result property="source" column="source" jdbcType="INTEGER"/>
        <result property="adCreatorTime" column="AD_CREATOR_TIME" jdbcType="TIMESTAMP"/>
        <result property="adStatus" column="AD_STATUS" jdbcType="INTEGER"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="AdWorkOrderMap">
        select
          ID, AD_JOB_NUM, AD_CONTRACT_NUM, AD_CUSTOM_NAME, AD_CUSTOM_PHONE, AD_CUSTOM_LIAISON, AD_BUSINESS_LICENSE, AD_AMOUNT_MONEY, AD_FILM_ID, AD_EQ_TYPE_A, AD_EQ_TYPE_B, AD_EQ_TYPE_C, AD_CREATOR_NAME, AD_CREATOR_NUM, source, AD_CREATOR_TIME, AD_STATUS
        from ad_work_order
        where ID = #{id}
    </select>


    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="AdWorkOrderMap">
        select
          ID, AD_JOB_NUM, AD_CONTRACT_NUM, AD_CUSTOM_NAME, AD_CUSTOM_PHONE, AD_CUSTOM_LIAISON, AD_BUSINESS_LICENSE, AD_AMOUNT_MONEY, AD_FILM_ID, AD_EQ_TYPE_A, AD_EQ_TYPE_B, AD_EQ_TYPE_C, AD_CREATOR_NAME, AD_CREATOR_NUM, source, AD_CREATOR_TIME, AD_STATUS
        from ad_work_order
        <where>
            <if test="id != null">
                and ID = #{id}
            </if>
            <if test="adJobNum != null and adJobNum != ''">
                and AD_JOB_NUM = #{adJobNum}
            </if>
            <if test="adContractNum != null and adContractNum != ''">
                and AD_CONTRACT_NUM = #{adContractNum}
            </if>
            <if test="adCustomName != null and adCustomName != ''">
                and AD_CUSTOM_NAME = #{adCustomName}
            </if>
            <if test="adCustomPhone != null and adCustomPhone != ''">
                and AD_CUSTOM_PHONE = #{adCustomPhone}
            </if>
            <if test="adCustomLiaison != null and adCustomLiaison != ''">
                and AD_CUSTOM_LIAISON = #{adCustomLiaison}
            </if>
            <if test="adBusinessLicense != null and adBusinessLicense != ''">
                and AD_BUSINESS_LICENSE = #{adBusinessLicense}
            </if>
            <if test="adAmountMoney != null">
                and AD_AMOUNT_MONEY = #{adAmountMoney}
            </if>
            <if test="adFilmId != null">
                and AD_FILM_ID = #{adFilmId}
            </if>
            <if test="adEqTypeA != null">
                and AD_EQ_TYPE_A = #{adEqTypeA}
            </if>
            <if test="adEqTypeB != null">
                and AD_EQ_TYPE_B = #{adEqTypeB}
            </if>
            <if test="adEqTypeC != null">
                and AD_EQ_TYPE_C = #{adEqTypeC}
            </if>
            <if test="adCreatorName != null and adCreatorName != ''">
                and AD_CREATOR_NAME = #{adCreatorName}
            </if>
            <if test="adCreatorNum != null and adCreatorNum != ''">
                and AD_CREATOR_NUM = #{adCreatorNum}
            </if>
            <if test="source != null">
                and source = #{source}
            </if>
            <if test="adCreatorTime != null">
                and AD_CREATOR_TIME = #{adCreatorTime}
            </if>
            <if test="adStatus != null">
                and AD_STATUS = #{adStatus}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into ad_work_order(
        <trim suffixOverrides=",">
            <if test="adJobNum != null and adJobNum != ''">
                AD_JOB_NUM ,
            </if>
            <if test="adContractNum != null and adContractNum != ''">
                AD_CONTRACT_NUM ,
            </if>
            <if test="adCustomName != null and adCustomName != ''">
                AD_CUSTOM_NAME ,
            </if>
            <if test="adCustomPhone != null and adCustomPhone != ''">
                AD_CUSTOM_PHONE ,
            </if>
            <if test="adCustomLiaison != null and adCustomLiaison != ''">
                AD_CUSTOM_LIAISON ,
            </if>
            <if test="adBusinessLicense != null and adBusinessLicense != ''">
                AD_BUSINESS_LICENSE ,
            </if>
            <if test="adAmountMoney != null">
                AD_AMOUNT_MONEY ,
            </if>
            <if test="adFilmId != null">
                AD_FILM_ID ,
            </if>
            <if test="adEqTypeA != null">
                AD_EQ_TYPE_A ,
            </if>
            <if test="adEqTypeB != null">
                AD_EQ_TYPE_B ,
            </if>
            <if test="adEqTypeC != null">
                AD_EQ_TYPE_C ,
            </if>
            <if test="adCreatorName != null and adCreatorName != ''">
                AD_CREATOR_NAME ,
            </if>
            <if test="adCreatorNum != null and adCreatorNum != ''">
                AD_CREATOR_NUM ,
            </if>
            <if test="source != null">
                source ,
            </if>
            <if test="adCreatorTime != null">
                AD_CREATOR_TIME ,
            </if>
            <if test="adStatus != null">
                AD_STATUS
            </if>
        </trim>
        )
        values (
        <trim suffixOverrides=",">
            <if test="adJobNum != null and adJobNum != ''">
                #{adJobNum},
            </if>
            <if test="adContractNum != null and adContractNum != ''">
                #{adContractNum},
            </if>
            <if test="adCustomName != null and adCustomName != ''">
                #{adCustomName},
            </if>
            <if test="adCustomPhone != null and adCustomPhone != ''">
                #{adCustomPhone},
            </if>
            <if test="adCustomLiaison != null and adCustomLiaison != ''">
                #{adCustomLiaison},
            </if>
            <if test="adBusinessLicense != null and adBusinessLicense != ''">
                #{adBusinessLicense},
            </if>
            <if test="adAmountMoney != null">
                #{adAmountMoney},
            </if>
            <if test="adFilmId != null">
                #{adFilmId},
            </if>
            <if test="adEqTypeA != null">
                #{adEqTypeA},
            </if>
            <if test="adEqTypeB != null">
                #{adEqTypeB},
            </if>
            <if test="adEqTypeC != null">
                #{adEqTypeC},
            </if>
            <if test="adCreatorName != null and adCreatorName != ''">
                #{adCreatorName},
            </if>
            <if test="adCreatorNum != null and adCreatorNum != ''">
                #{adCreatorNum},
            </if>
            <if test="source != null">
                #{source},
            </if>
            <if test="adCreatorTime != null">
                #{adCreatorTime},
            </if>
            <if test="adStatus != null">
                #{adStatus}
            </if>
        </trim>)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update ad_work_order
        <set>
            <if test="adJobNum != null and adJobNum != ''">
                AD_JOB_NUM = #{adJobNum},
            </if>
            <if test="adContractNum != null and adContractNum != ''">
                AD_CONTRACT_NUM = #{adContractNum},
            </if>
            <if test="adCustomName != null and adCustomName != ''">
                AD_CUSTOM_NAME = #{adCustomName},
            </if>
            <if test="adCustomPhone != null and adCustomPhone != ''">
                AD_CUSTOM_PHONE = #{adCustomPhone},
            </if>
            <if test="adCustomLiaison != null and adCustomLiaison != ''">
                AD_CUSTOM_LIAISON = #{adCustomLiaison},
            </if>
            <if test="adBusinessLicense != null and adBusinessLicense != ''">
                AD_BUSINESS_LICENSE = #{adBusinessLicense},
            </if>
            <if test="adAmountMoney != null">
                AD_AMOUNT_MONEY = #{adAmountMoney},
            </if>
            <if test="adFilmId != null">
                AD_FILM_ID = #{adFilmId},
            </if>
            <if test="adEqTypeA != null">
                AD_EQ_TYPE_A = #{adEqTypeA},
            </if>
            <if test="adEqTypeB != null">
                AD_EQ_TYPE_B = #{adEqTypeB},
            </if>
            <if test="adEqTypeC != null">
                AD_EQ_TYPE_C = #{adEqTypeC},
            </if>
            <if test="adCreatorName != null and adCreatorName != ''">
                AD_CREATOR_NAME = #{adCreatorName},
            </if>
            <if test="adCreatorNum != null and adCreatorNum != ''">
                AD_CREATOR_NUM = #{adCreatorNum},
            </if>
            <if test="source != null">
                source = #{source},
            </if>
            <if test="adCreatorTime != null">
                AD_CREATOR_TIME = #{adCreatorTime},
            </if>
            <if test="adStatus != null">
                AD_STATUS = #{adStatus}
            </if>
        </set>
        where ID = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from ad_work_order where ID = #{id}
    </delete>

    <select id="queryByOrderId" resultMap="AdWorkOrderMap">
        select
          ID, AD_JOB_NUM, AD_CONTRACT_NUM, AD_CUSTOM_NAME, AD_CUSTOM_PHONE, AD_CUSTOM_LIAISON, AD_BUSINESS_LICENSE, AD_AMOUNT_MONEY, AD_FILM_ID, AD_EQ_TYPE_A, AD_EQ_TYPE_B, AD_EQ_TYPE_C, AD_CREATOR_NAME, AD_CREATOR_NUM, source, AD_CREATOR_TIME, AD_STATUS
        from ad_work_order
        where AD_JOB_NUM = #{orderId}
    </select>
</mapper>