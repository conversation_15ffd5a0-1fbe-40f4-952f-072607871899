<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.modules.commission.mapper.BWorkDeployorderMapper">

    <resultMap type="com.jmt.modules.commission.entity.BWorkDeployorder" id="BWorkDeployorderMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="mworkorder" column="mWorkorder" jdbcType="VARCHAR"/>
        <result property="mcodename" column="mCodeName" jdbcType="VARCHAR"/>
        <result property="pextensioncodename" column="pExtensionCodeName" jdbcType="VARCHAR"/>
        <result property="createtime" column="createTime" jdbcType="TIMESTAMP"/>
        <result property="meqnuma" column="mEqNumA" jdbcType="INTEGER"/>
        <result property="meqnumb" column="mEqNumB" jdbcType="INTEGER"/>
        <result property="meqnumc" column="mEqNumC" jdbcType="INTEGER"/>
        <result property="stauts" column="stauts" jdbcType="INTEGER"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="BWorkDeployorderMap">
        select
          id, mWorkorder, mCodeName, pExtensionCodeName, createTime, mEqNumA, mEqNumB, mEqNumC, stauts
        from b_work_deployorder
        where id = #{id}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="BWorkDeployorderMap">
        select
          id, mWorkorder, mCodeName, pExtensionCodeName, createTime, mEqNumA, mEqNumB, mEqNumC, stauts
        from b_work_deployorder
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="mworkorder != null and mworkorder != ''">
                and mWorkorder = #{mworkorder}
            </if>
            <if test="mcodename != null and mcodename != ''">
                and mCodeName = #{mcodename}
            </if>
            <if test="pextensioncodename != null and pextensioncodename != ''">
                and pExtensionCodeName = #{pextensioncodename}
            </if>
            <if test="createtime != null">
                and createTime = #{createtime}
            </if>
            <if test="meqnuma != null">
                and mEqNumA = #{meqnuma}
            </if>
            <if test="meqnumb != null">
                and mEqNumB = #{meqnumb}
            </if>
            <if test="meqnumc != null">
                and mEqNumC = #{meqnumc}
            </if>
            <if test="stauts != null">
                and stauts = #{stauts}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into b_work_deployorder(
        <trim suffixOverrides=",">
            <if test="mworkorder != null and mworkorder != ''">
                mWorkorder ,
            </if>
            <if test="mcodename != null and mcodename != ''">
                mCodeName ,
            </if>
            <if test="pextensioncodename != null and pextensioncodename != ''">
                pExtensionCodeName ,
            </if>
            <if test="createtime != null">
                createTime ,
            </if>
            <if test="meqnuma != null">
                mEqNumA ,
            </if>
            <if test="meqnumb != null">
                mEqNumB ,
            </if>
            <if test="meqnumc != null">
                mEqNumC ,
            </if>
            <if test="stauts != null">
                stauts </if>
        </trim>
        )
        values (
        <trim suffixOverrides=",">
            <if test="mworkorder != null and mworkorder != ''">
                #{mworkorder},
            </if>
            <if test="mcodename != null and mcodename != ''">
                #{mcodename},
            </if>
            <if test="pextensioncodename != null and pextensioncodename != ''">
                #{pextensioncodename},
            </if>
            <if test="createtime != null">
                #{createtime},
            </if>
            <if test="meqnuma != null">
                #{meqnuma},
            </if>
            <if test="meqnumb != null">
                #{meqnumb},
            </if>
            <if test="meqnumc != null">
                #{meqnumc},
            </if>
            <if test="stauts != null">
                #{stauts}</if>
        </trim>)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update b_work_deployorder
        <set>
            <if test="mworkorder != null and mworkorder != ''">
                mWorkorder = #{mworkorder},
            </if>
            <if test="mcodename != null and mcodename != ''">
                mCodeName = #{mcodename},
            </if>
            <if test="pextensioncodename != null and pextensioncodename != ''">
                pExtensionCodeName = #{pextensioncodename},
            </if>
            <if test="createtime != null">
                createTime = #{createtime},
            </if>
            <if test="meqnuma != null">
                mEqNumA = #{meqnuma},
            </if>
            <if test="meqnumb != null">
                mEqNumB = #{meqnumb},
            </if>
            <if test="meqnumc != null">
                mEqNumC = #{meqnumc},
            </if>
            <if test="stauts != null">
                stauts = #{stauts},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from b_work_deployorder where id = #{id}
    </delete>

    <update id="updateByWorkorder">
        update b_work_deployorder
        <set>
            <if test="mcodename != null and mcodename != ''">
                mCodeName = #{mcodename},
            </if>
            <if test="pextensioncodename != null and pextensioncodename != ''">
                pExtensionCodeName = #{pextensioncodename},
            </if>
            <if test="createtime != null">
                createTime = #{createtime},
            </if>
            <if test="meqnuma != null">
                mEqNumA = #{meqnuma},
            </if>
            <if test="meqnumb != null">
                mEqNumB = #{meqnumb},
            </if>
            <if test="meqnumc != null">
                mEqNumC = #{meqnumc},
            </if>
            <if test="stauts != null">
                stauts = #{stauts},
            </if>
        </set>
        where mWorkorder = #{mworkorder}
    </update>
</mapper>