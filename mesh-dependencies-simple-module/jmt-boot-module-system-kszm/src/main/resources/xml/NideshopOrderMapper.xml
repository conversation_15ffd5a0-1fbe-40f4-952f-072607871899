<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.modules.commission.mapper.NideshopOrderMapper">

    <resultMap type="com.jmt.modules.commission.entity.NideshopOrder" id="NideshopOrderMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="orderSn" column="order_sn" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="INTEGER"/>
        <result property="orderStatus" column="order_status" jdbcType="INTEGER"/>
        <result property="shippingStatus" column="shipping_status" jdbcType="BOOLEAN"/>
        <result property="payStatus" column="pay_status" jdbcType="BOOLEAN"/>
        <result property="consignee" column="consignee" jdbcType="VARCHAR"/>
        <result property="country" column="country" jdbcType="VARCHAR"/>
        <result property="province" column="province" jdbcType="VARCHAR"/>
        <result property="city" column="city" jdbcType="VARCHAR"/>
        <result property="district" column="district" jdbcType="VARCHAR"/>
        <result property="address" column="address" jdbcType="VARCHAR"/>
        <result property="mobile" column="mobile" jdbcType="VARCHAR"/>
        <result property="postscript" column="postscript" jdbcType="VARCHAR"/>
        <result property="shippingId" column="shipping_id" jdbcType="OTHER"/>
        <result property="shippingName" column="shipping_name" jdbcType="VARCHAR"/>
        <result property="payId" column="pay_id" jdbcType="VARCHAR"/>
        <result property="payName" column="pay_name" jdbcType="VARCHAR"/>
        <result property="shippingFee" column="shipping_fee" jdbcType="OTHER"/>
        <result property="actualPrice" column="actual_price" jdbcType="OTHER"/>
        <result property="integral" column="integral" jdbcType="INTEGER"/>
        <result property="integralMoney" column="integral_money" jdbcType="OTHER"/>
        <result property="orderPrice" column="order_price" jdbcType="OTHER"/>
        <result property="goodsPrice" column="goods_price" jdbcType="OTHER"/>
        <result property="addTime" column="add_time" jdbcType="TIMESTAMP"/>
        <result property="confirmTime" column="confirm_time" jdbcType="TIMESTAMP"/>
        <result property="payTime" column="pay_time" jdbcType="TIMESTAMP"/>
        <result property="freightPrice" column="freight_price" jdbcType="INTEGER"/>
        <result property="couponId" column="coupon_id" jdbcType="INTEGER"/>
        <result property="parentId" column="parent_id" jdbcType="INTEGER"/>
        <result property="couponPrice" column="coupon_price" jdbcType="OTHER"/>
        <result property="callbackStatus" column="callback_status" jdbcType="OTHER"/>
        <result property="shippingNo" column="shipping_no" jdbcType="VARCHAR"/>
        <result property="fullCutPrice" column="full_cut_price" jdbcType="OTHER"/>
        <result property="orderType" column="order_type" jdbcType="VARCHAR"/>
        <result property="partServant" column="part_servant" jdbcType="INTEGER"/>
        <result property="mcode" column="mcode" jdbcType="VARCHAR"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="NideshopOrderMap">
        select
          id, order_sn, user_id, order_status, shipping_status, pay_status, consignee, country, province, city, district, address, mobile, postscript, shipping_id, shipping_name, pay_id, pay_name, shipping_fee, actual_price, integral, integral_money, order_price, goods_price, add_time, confirm_time, pay_time, freight_price, coupon_id, parent_id, coupon_price, callback_status, shipping_no, full_cut_price, order_type, part_servant, mcode
        from nideshop_order
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="NideshopOrderMap">
        select
          id, order_sn, user_id, order_status, shipping_status, pay_status, consignee, country, province, city, district, address, mobile, postscript, shipping_id, shipping_name, pay_id, pay_name, shipping_fee, actual_price, integral, integral_money, order_price, goods_price, add_time, confirm_time, pay_time, freight_price, coupon_id, parent_id, coupon_price, callback_status, shipping_no, full_cut_price, order_type, part_servant, mcode
        from nideshop_order
        limit #{offset}, #{limit}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="NideshopOrderMap">
        select
          id, order_sn, user_id, order_status, shipping_status, pay_status, consignee, country, province, city, district, address, mobile, postscript, shipping_id, shipping_name, pay_id, pay_name, shipping_fee, actual_price, integral, integral_money, order_price, goods_price, add_time, confirm_time, pay_time, freight_price, coupon_id, parent_id, coupon_price, callback_status, shipping_no, full_cut_price, order_type, part_servant, mcode
        from nideshop_order
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="orderSn != null and orderSn != ''">
                and order_sn = #{orderSn}
            </if>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
            <if test="orderStatus != null">
                and order_status = #{orderStatus}
            </if>
            <if test="shippingStatus != null">
                and shipping_status = #{shippingStatus}
            </if>
            <if test="payStatus != null">
                and pay_status = #{payStatus}
            </if>
            <if test="consignee != null and consignee != ''">
                and consignee = #{consignee}
            </if>
            <if test="country != null and country != ''">
                and country = #{country}
            </if>
            <if test="province != null and province != ''">
                and province = #{province}
            </if>
            <if test="city != null and city != ''">
                and city = #{city}
            </if>
            <if test="district != null and district != ''">
                and district = #{district}
            </if>
            <if test="address != null and address != ''">
                and address = #{address}
            </if>
            <if test="mobile != null and mobile != ''">
                and mobile = #{mobile}
            </if>
            <if test="postscript != null and postscript != ''">
                and postscript = #{postscript}
            </if>
            <if test="shippingId != null">
                and shipping_id = #{shippingId}
            </if>
            <if test="shippingName != null and shippingName != ''">
                and shipping_name = #{shippingName}
            </if>
            <if test="payId != null and payId != ''">
                and pay_id = #{payId}
            </if>
            <if test="payName != null and payName != ''">
                and pay_name = #{payName}
            </if>
            <if test="shippingFee != null">
                and shipping_fee = #{shippingFee}
            </if>
            <if test="actualPrice != null">
                and actual_price = #{actualPrice}
            </if>
            <if test="integral != null">
                and integral = #{integral}
            </if>
            <if test="integralMoney != null">
                and integral_money = #{integralMoney}
            </if>
            <if test="orderPrice != null">
                and order_price = #{orderPrice}
            </if>
            <if test="goodsPrice != null">
                and goods_price = #{goodsPrice}
            </if>
            <if test="addTime != null">
                and add_time = #{addTime}
            </if>
            <if test="confirmTime != null">
                and confirm_time = #{confirmTime}
            </if>
            <if test="payTime != null">
                and pay_time = #{payTime}
            </if>
            <if test="freightPrice != null">
                and freight_price = #{freightPrice}
            </if>
            <if test="couponId != null">
                and coupon_id = #{couponId}
            </if>
            <if test="parentId != null">
                and parent_id = #{parentId}
            </if>
            <if test="couponPrice != null">
                and coupon_price = #{couponPrice}
            </if>
            <if test="callbackStatus != null">
                and callback_status = #{callbackStatus}
            </if>
            <if test="shippingNo != null and shippingNo != ''">
                and shipping_no = #{shippingNo}
            </if>
            <if test="fullCutPrice != null">
                and full_cut_price = #{fullCutPrice}
            </if>
            <if test="orderType != null and orderType != ''">
                and order_type = #{orderType}
            </if>
            <if test="partServant != null">
                and part_servant = #{partServant}
            </if>
            <if test="mcode != null and mcode != ''">
                and mcode = #{mcode}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into nideshop_order(
        <trim suffixOverrides=",">
            <if test="orderSn != null and orderSn != ''">
                order_sn ,
            </if>
            <if test="userId != null">
                user_id ,
            </if>
            <if test="orderStatus != null">
                order_status ,
            </if>
            <if test="shippingStatus != null">
                shipping_status ,
            </if>
            <if test="payStatus != null">
                pay_status ,
            </if>
            <if test="consignee != null and consignee != ''">
                consignee ,
            </if>
            <if test="country != null and country != ''">
                country ,
            </if>
            <if test="province != null and province != ''">
                province ,
            </if>
            <if test="city != null and city != ''">
                city ,
            </if>
            <if test="district != null and district != ''">
                district ,
            </if>
            <if test="address != null and address != ''">
                address ,
            </if>
            <if test="mobile != null and mobile != ''">
                mobile ,
            </if>
            <if test="postscript != null and postscript != ''">
                postscript ,
            </if>
            <if test="shippingId != null">
                shipping_id ,
            </if>
            <if test="shippingName != null and shippingName != ''">
                shipping_name ,
            </if>
            <if test="payId != null and payId != ''">
                pay_id ,
            </if>
            <if test="payName != null and payName != ''">
                pay_name ,
            </if>
            <if test="shippingFee != null">
                shipping_fee ,
            </if>
            <if test="actualPrice != null">
                actual_price ,
            </if>
            <if test="integral != null">
                integral ,
            </if>
            <if test="integralMoney != null">
                integral_money ,
            </if>
            <if test="orderPrice != null">
                order_price ,
            </if>
            <if test="goodsPrice != null">
                goods_price ,
            </if>
            <if test="addTime != null">
                add_time ,
            </if>
            <if test="confirmTime != null">
                confirm_time ,
            </if>
            <if test="payTime != null">
                pay_time ,
            </if>
            <if test="freightPrice != null">
                freight_price ,
            </if>
            <if test="couponId != null">
                coupon_id ,
            </if>
            <if test="parentId != null">
                parent_id ,
            </if>
            <if test="couponPrice != null">
                coupon_price ,
            </if>
            <if test="callbackStatus != null">
                callback_status ,
            </if>
            <if test="shippingNo != null and shippingNo != ''">
                shipping_no ,
            </if>
            <if test="fullCutPrice != null">
                full_cut_price ,
            </if>
            <if test="orderType != null and orderType != ''">
                order_type ,
            </if>
            <if test="partServant != null">
                part_servant ,
            </if>
            <if test="mcode != null and mcode != ''">
                mcode </if>
        </trim>
        )
        values (
        <trim suffixOverrides=",">
            <if test="orderSn != null and orderSn != ''">
                #{orderSn},
            </if>
            <if test="userId != null">
                #{userId},
            </if>
            <if test="orderStatus != null">
                #{orderStatus},
            </if>
            <if test="shippingStatus != null">
                #{shippingStatus},
            </if>
            <if test="payStatus != null">
                #{payStatus},
            </if>
            <if test="consignee != null and consignee != ''">
                #{consignee},
            </if>
            <if test="country != null and country != ''">
                #{country},
            </if>
            <if test="province != null and province != ''">
                #{province},
            </if>
            <if test="city != null and city != ''">
                #{city},
            </if>
            <if test="district != null and district != ''">
                #{district},
            </if>
            <if test="address != null and address != ''">
                #{address},
            </if>
            <if test="mobile != null and mobile != ''">
                #{mobile},
            </if>
            <if test="postscript != null and postscript != ''">
                #{postscript},
            </if>
            <if test="shippingId != null">
                #{shippingId},
            </if>
            <if test="shippingName != null and shippingName != ''">
                #{shippingName},
            </if>
            <if test="payId != null and payId != ''">
                #{payId},
            </if>
            <if test="payName != null and payName != ''">
                #{payName},
            </if>
            <if test="shippingFee != null">
                #{shippingFee},
            </if>
            <if test="actualPrice != null">
                #{actualPrice},
            </if>
            <if test="integral != null">
                #{integral},
            </if>
            <if test="integralMoney != null">
                #{integralMoney},
            </if>
            <if test="orderPrice != null">
                #{orderPrice},
            </if>
            <if test="goodsPrice != null">
                #{goodsPrice},
            </if>
            <if test="addTime != null">
                #{addTime},
            </if>
            <if test="confirmTime != null">
                #{confirmTime},
            </if>
            <if test="payTime != null">
                #{payTime},
            </if>
            <if test="freightPrice != null">
                #{freightPrice},
            </if>
            <if test="couponId != null">
                #{couponId},
            </if>
            <if test="parentId != null">
                #{parentId},
            </if>
            <if test="couponPrice != null">
                #{couponPrice},
            </if>
            <if test="callbackStatus != null">
                #{callbackStatus},
            </if>
            <if test="shippingNo != null and shippingNo != ''">
                #{shippingNo},
            </if>
            <if test="fullCutPrice != null">
                #{fullCutPrice},
            </if>
            <if test="orderType != null and orderType != ''">
                #{orderType},
            </if>
            <if test="partServant != null">
                #{partServant},
            </if>
            <if test="mcode != null and mcode != ''">
                #{mcode}</if>
        </trim>)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update nideshop_order
        <set>
            <if test="orderSn != null and orderSn != ''">
                order_sn = #{orderSn},
            </if>
            <if test="userId != null">
                user_id = #{userId},
            </if>
            <if test="orderStatus != null">
                order_status = #{orderStatus},
            </if>
            <if test="shippingStatus != null">
                shipping_status = #{shippingStatus},
            </if>
            <if test="payStatus != null">
                pay_status = #{payStatus},
            </if>
            <if test="consignee != null and consignee != ''">
                consignee = #{consignee},
            </if>
            <if test="country != null and country != ''">
                country = #{country},
            </if>
            <if test="province != null and province != ''">
                province = #{province},
            </if>
            <if test="city != null and city != ''">
                city = #{city},
            </if>
            <if test="district != null and district != ''">
                district = #{district},
            </if>
            <if test="address != null and address != ''">
                address = #{address},
            </if>
            <if test="mobile != null and mobile != ''">
                mobile = #{mobile},
            </if>
            <if test="postscript != null and postscript != ''">
                postscript = #{postscript},
            </if>
            <if test="shippingId != null">
                shipping_id = #{shippingId},
            </if>
            <if test="shippingName != null and shippingName != ''">
                shipping_name = #{shippingName},
            </if>
            <if test="payId != null and payId != ''">
                pay_id = #{payId},
            </if>
            <if test="payName != null and payName != ''">
                pay_name = #{payName},
            </if>
            <if test="shippingFee != null">
                shipping_fee = #{shippingFee},
            </if>
            <if test="actualPrice != null">
                actual_price = #{actualPrice},
            </if>
            <if test="integral != null">
                integral = #{integral},
            </if>
            <if test="integralMoney != null">
                integral_money = #{integralMoney},
            </if>
            <if test="orderPrice != null">
                order_price = #{orderPrice},
            </if>
            <if test="goodsPrice != null">
                goods_price = #{goodsPrice},
            </if>
            <if test="addTime != null">
                add_time = #{addTime},
            </if>
            <if test="confirmTime != null">
                confirm_time = #{confirmTime},
            </if>
            <if test="payTime != null">
                pay_time = #{payTime},
            </if>
            <if test="freightPrice != null">
                freight_price = #{freightPrice},
            </if>
            <if test="couponId != null">
                coupon_id = #{couponId},
            </if>
            <if test="parentId != null">
                parent_id = #{parentId},
            </if>
            <if test="couponPrice != null">
                coupon_price = #{couponPrice},
            </if>
            <if test="callbackStatus != null">
                callback_status = #{callbackStatus},
            </if>
            <if test="shippingNo != null and shippingNo != ''">
                shipping_no = #{shippingNo},
            </if>
            <if test="fullCutPrice != null">
                full_cut_price = #{fullCutPrice},
            </if>
            <if test="orderType != null and orderType != ''">
                order_type = #{orderType},
            </if>
            <if test="partServant != null">
                part_servant = #{partServant},
            </if>
            <if test="mcode != null and mcode != ''">
                mcode = #{mcode},
            </if>
            <if test="income != null">
                income = #{income},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from nideshop_order where id = #{id}
    </delete>

    <select id="queryYesterdaySOrder" resultMap="NideshopOrderMap">
        SELECT * FROM nideshop_order WHERE mcode IS NOT NULL AND
        pay_status = 2 and income = 0 and part_servant = 1
    </select>


</mapper>