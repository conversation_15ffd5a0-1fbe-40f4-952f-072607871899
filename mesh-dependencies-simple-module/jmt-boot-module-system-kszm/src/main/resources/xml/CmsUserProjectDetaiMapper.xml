<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.modules.commission.mapper.CmsUserProjectDetaiMapper">

    <resultMap type="com.jmt.modules.commission.entity.CmsUserProjectDetai" id="CmsUserProjectDetaiMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="userCId" column="user_c_id" jdbcType="INTEGER"/>
        <result property="cid" column="c_id" jdbcType="INTEGER"/>
        <result property="cname" column="c_name" jdbcType="VARCHAR"/>
        <result property="minScale" column="min_scale"/>
        <result property="maxScale" column="max_scale"/>
        <result property="subMerchants" column="sub_merchants" jdbcType="INTEGER"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="CmsUserProjectDetaiMap">
        select
          id, user_c_id, c_id, c_name, min_scale, max_scale, sub_merchants
        from cms_user_project_detai
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="CmsUserProjectDetaiMap">
        select
          id, user_c_id, c_id, c_name, min_scale, max_scale, sub_merchants
        from cms_user_project_detai
        limit #{offset}, #{limit}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="CmsUserProjectDetaiMap">
        select
          id, user_c_id, c_id, c_name, min_scale, max_scale, sub_merchants
        from cms_user_project_detai
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="userCId != null">
                and user_c_id = #{userCId}
            </if>
            <if test="cid != null">
                and c_id = #{cid}
            </if>
            <if test="cname != null and cname != ''">
                and c_name = #{cname}
            </if>
            <if test="minScale != null">
                and min_scale = #{minScale}
            </if>
            <if test="maxScale != null">
                and max_scale = #{maxScale}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into cms_user_project_detai(user_c_id, c_id, c_name, min_scale, max_scale, sub_merchants)
        values (#{userCId}, #{cid}, #{cname}, #{minScale}, #{maxScale}, #{subMerchants})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update cms_user_project_detai
        <set>
            <if test="userCId != null">
                user_c_id = #{userCId},
            </if>
            <if test="cid != null">
                c_id = #{cid},
            </if>
            <if test="cname != null and cname != ''">
                c_name = #{cname},
            </if>
            <if test="minScale != null">
                min_scale = #{minScale},
            </if>
            <if test="maxScale != null">
                max_scale = #{maxScale},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from cms_user_project_detai where id = #{id}
    </delete>

    <select id="queryByCId" resultMap="CmsUserProjectDetaiMap">
        select
          id, user_c_id, c_id, c_name, min_scale, max_scale, sub_merchants
        from cms_user_project_detai where user_c_id = #{userCId}
    </select>
</mapper>