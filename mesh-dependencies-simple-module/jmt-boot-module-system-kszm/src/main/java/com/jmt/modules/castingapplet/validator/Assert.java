package com.jmt.modules.castingapplet.validator;

import org.apache.commons.lang.StringUtils;
import com.jmt.common.exception.RRException;

/**
 * 数据校验
 */
public abstract class Assert {

    public static void isBlank(String str, String message) {
        if (StringUtils.isBlank(str)) {
            throw new RRException(message);
        }
    }

    public static void isNull(Object object, String message) {
        if (object == null) {
            throw new RRException(message);
        }
    }

    public static void equal(Object object1,Object objec2, String message) {
        if (!object1.equals(objec2)) {
            throw new RRException(message);
        }
    }
}
