package com.jmt.modules.commission.mapper;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import com.jmt.modules.castingapplet.model.dto.ShopInfoListDto;
import com.jmt.modules.castingapplet.model.vo.ShopInfoListVo;
import com.jmt.modules.commission.entity.CmsShopInfo;
import com.jmt.modules.commission.model.dto.CmsShopInfoListAppDto;
import com.jmt.modules.commission.model.dto.CmsShopInfoListDto;
import com.jmt.modules.commission.model.dto.ShopInfoDto;
import com.jmt.modules.commission.model.vo.CmsShopInfoListVo;
import com.jmt.modules.commission.model.vo.*;

import java.util.List;
import java.util.Map;

/**
 * 商户信息(CmsShopInfo)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-04-24 10:58:21
 */
public interface CmsShopInfoMapper extends BaseMapper<CmsShopInfo> {
    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    CmsShopInfo queryById(Integer id);


    /**
     * 通过实体作为筛选条件查询
     *
     * @param cmsShopInfo 实例对象
     * @return 对象列表
     */
    List<CmsShopInfo> queryAll(CmsShopInfo cmsShopInfo);

    java.util.Map getCountyMap();
    /**
     * 新增数据
     *
     * @param cmsShopInfo 实例对象
     * @return 影响行数
     */
    int insert(CmsShopInfo cmsShopInfo);

    /**
     * 修改数据
     *
     * @param cmsShopInfo 实例对象
     * @return 影响行数
     */
    int update(CmsShopInfo cmsShopInfo);

    /**
     * 修改数据
     *
     * @param cmsShopInfo 实例对象
     * @return 影响行数
     */
    int updateByMcode(CmsShopInfo cmsShopInfo);
    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Integer id);

    /**
     * 描述:  商户列表
     * @method  queryAllLike
     * @date: 2020/4/24 0024
     * @author: hanshangrong
     * @param page
     * @param cms
     * @return java.util.List<com.jmt.modules.commission.model.vo.CmsShopInfoListVo>
     */
    List<CmsShopInfoListVo> queryAllLike(Page<CmsShopInfoListVo> page, CmsShopInfoListDto cms);

    /**
     * 描述:  通过商家编号修改合伙人信息
     * @method  updateSubCodeByMcode
     * @date: 2020/4/24 0024
     * @author: hanshangrong
     * @param mCode
     * @param pId
     * @return int
     */
    int updateSubCodeByMcode(@Param("mCode") String mCode,@Param("pId") Integer pId);

    /**
     * 描述:  通过合伙人编号修改合伙人编号
     * @method  updateSubCodeBySubCode
     * @date: 2020/4/24 0024
     * @author: hanshangrong
     * @param increaseCode
     * @param removeCode
     * @return int
     */
    int updateSubCodeBySubCode(@Param("increaseCode") String increaseCode,@Param("removeCode") String removeCode);

    /**
     * 描述:  获取商户信息
     * @method  queryByMcode
     * @date: 2020/4/25 0025
     * @author: hanshangrong
     * @param mCode
     * @return com.jmt.modules.commission.model.vo.CmsShopInfoVo
     */
    CmsShopInfoVo queryByMcode(String mCode);

    /**
     * 描述:  商户列表
     * @method  queryAllLikeApp
     * @date: 2020/4/27 0027
     * @author: hanshangrong
     * @param page
     * @param cms
     * @return java.util.List<com.jmt.modules.commission.model.vo.CmsShopInfoListAppVo>
     */
    List<CmsShopInfoListAppVo> queryAllLikeApp(Page<CmsShopInfoListAppVo> page, CmsShopInfoListAppDto cms);

    /**
     * 描述:  获取商户详情
     * @method  queryByMCode
     * @date: 2020/4/27 0027
     * @author: hanshangrong
     * @param mCode
     * @return com.jmt.modules.commission.entity.CmsShopInfo
     */
    CmsShopInfo queryByMCode(String mCode);

    /**
     * 描述:  查询名称
     * @method  queryByRecommended
     * @date: 2020/4/27 0027
     * @author: hanshangrong
     * @param recommendedRestaurant
     * @return java.lang.String
     */
    String queryByRecommended(String recommendedRestaurant);

    /**
     * 描述:  商户数量
     * @method  queryShopNum
     * @date: 2020/4/28 0028
     * @author: hanshangrong
     * @param pId
     * @return java.util.Map<java.lang.String,java.lang.Integer>
     */
    ShopInfoNumVo queryShopNum(Integer pId);


    /**
     * 提供小程序端的数据
     * @param mCode
     * @return
     */
    CmsShopInfo getBuserInfo(@Param("mCode") String mCode);


    /**
     * 描述:  查询合伙人商家数量
     * @method  queryShopNums
     * @date: 2020/5/15 0015
     * @author: hanshangrong
     * @param pId
     * @return java.lang.Integer
     */
    Integer queryShopNums(Integer pId);

    /**
     * 描述:  投广商户列表
     * @method  listOfMerchants
     * @date: 2020/5/21 0021
     * @author: hanshangrong
     * @param page
     * @param shop
     * @return java.util.List<com.jmt.modules.castingapplet.model.vo.ShopInfoListVo>
     */
    List<ShopInfoListVo> listOfMerchants(Page<ShopInfoListVo> page, ShopInfoListDto shop);

    /***
     * 描述:  查询商户信息
     * @method: queryShopInfo
     * @author: HSR
     * @date: 2020/10/29
     * @param param
     * @return: com.jmt.modules.commission.entity.CmsShopInfo
     * @exception:
    **/
    CmsShopInfo queryShopInfo(JSONObject param);

    String getMTraffics(@Param("mCodes") String mCodes);



    List<com.jmt.modules.commission.model.vo.ShopInfoListVo> queryPoint(Page<com.jmt.modules.commission.model.vo.ShopInfoListVo> page, ShopInfoDto shop);

    com.jmt.modules.commission.model.vo.ShopInfoVo queryPointInfo(String mCode);

    /**
     *  根据集合中code查询点位信息
     * @param codes
     * @return
     */
    List<ShopInfoListVo> listShopByArrayCode(@Param("code") JSONArray codes);

    /**
     *  审核列表
     * @param page
     * @param params
     * @return
     */
    List<ShopAuditListVO> listShopAudit(Page<ShopAuditListVO> page,@Param("params") JSONObject params);

    /***
     * 描述:  通过设备编号查询商户信息
     * @method: getShopInfoByEqCode
     * @author: HSR
     * @date: 2020/11/3
     * @param eqCode
     * @return: com.jmt.modules.commission.entity.CmsShopInfo
     * @exception:
    **/
    CmsShopInfo getShopInfoByEqCode(String eqCode);

    /***
     * 描述:  修改商户充电积分
     * @method: updateIntegralByLevel
     * @author: HSR
     * @date: 2020/11/19
     * @param lowestValue
     * @param starRating
     * @return: void
     * @exception:
    **/
    void updateIntegralByLevel(@Param("lowestValue") Integer lowestValue,@Param("starRating") Integer starRating);


    void updateShopPartner(@Param("id") Integer id,@Param("pId") List<String> pId);

    List<String> listShopCodeByPid(Integer pId);

    List<CmsShopInfo>  queryCuisine();

    List<CmsShopInfoListAppVo> queryByRecommend(Integer pId);

    List<CmsShopInfoListAppVo> queryByPromoter(String promoterNo,String area);
    List<CmsShopInfoListAppVo> queryByPartener1(Page<CmsShopInfoListAppVo> page, CmsShopInfoListAppDto cms);
    List<CmsShopInfoListAppVo> queryByPartener2(Page<CmsShopInfoListAppVo> page, CmsShopInfoListAppDto cms);
    Integer getToAuditShopCount(String pId); //可以淘汰
    Integer getShopCountOfPartner(Integer pId,Integer status);
}