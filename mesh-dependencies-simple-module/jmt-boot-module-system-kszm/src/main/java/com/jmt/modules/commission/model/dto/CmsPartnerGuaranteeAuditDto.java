package com.jmt.modules.commission.model.dto;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 *  @author: hanshangrong
 *  @Date: 2020/4/23 0023 11:30
 *  @Description:类注释
 *  保证金审核
 */
@Data
public class CmsPartnerGuaranteeAuditDto {
    @NotNull(message = "保证金Id为空")
    private Integer id;

    @NotNull(message = "状态为空")
    @Max(value = 2,message = "状态非法参数")
    @Min(value = 1,message = "状态非法参数")
    private Integer status;
    @NotBlank(message = "流水号为空")
    private String flowingWater;
    private String reason;
}
