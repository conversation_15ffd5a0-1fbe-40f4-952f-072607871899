package com.jmt.modules.commission.service.impl;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.jmt.modules.commission.mapper.PlantChargeOrderMapper;
import com.jmt.modules.commission.service.PlantChargeOrderService;
import org.springframework.stereotype.Service;

@DS("platform")
@Service
public class PlantChargeOrderServiceImpl implements PlantChargeOrderService {
    private PlantChargeOrderMapper plantChargeOrderMapper;

    public java.util.Map<String,Integer> statEqChargeOrder(){ return plantChargeOrderMapper.statEqChargeOrder();}
}
