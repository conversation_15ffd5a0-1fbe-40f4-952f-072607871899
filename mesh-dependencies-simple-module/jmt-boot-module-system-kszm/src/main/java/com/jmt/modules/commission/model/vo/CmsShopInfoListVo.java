package com.jmt.modules.commission.model.vo;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 *  @author: hanshangrong
 *  @Date: 2020/4/24 0024 11:24
 *  @Description:类注释
 *  商户列表
 */
@Data
@JsonAutoDetect(getterVisibility=JsonAutoDetect.Visibility.NONE)
public class CmsShopInfoListVo {
    @JsonProperty("id")
    private Integer id;
    /**
     * 商户编号
     */
    @JsonProperty("mCode")
    private String mCode;
    /**
     * 商户名称
     */
    @JsonProperty("mName")
    private String mName;

    /**
     * 店铺面积
     */
    @JsonProperty("mMeasure")
    private Double mMeasure;


    /**
     * 人均消费
     */
    @JsonProperty("mConsume")
    private String mConsume;

    /**
     * 圆桌数量
     */
    @JsonProperty("mAmountDeskY")
    private Integer mAmountDeskY;
    /**
     * 方桌数量
     */
    @JsonProperty("mAmountDeskF")
    private Integer mAmountDeskF;

    /**
     * 类型
     */
    @JsonProperty("mType")
    private String mType;
    /**
     * 营业执照
     */
    @JsonProperty("mBusinessLicense")
    private String mBusinessLicense;
    /**
     * 所属合伙人
     */
    @JsonProperty("pSubCode")
    private String pSubCode;
    /**
     * 商户总收益
     */
    @JsonProperty("mProfit")
    private BigDecimal mProfit;
    /**
     * 推荐收益
     */
    @JsonProperty("referralIncome")
    private BigDecimal referralIncome;

    /**
     * 地区
     */
    @JsonProperty("address")
    private String address;
    /**
     * 设备总数
     */
    @JsonProperty("eqNum")
    private Integer eqNum;
    /**
     * 合伙人名称
     */
    @JsonProperty("pName")
    private String pName;

    /**
     * 合伙人名称
     */
    @JsonProperty("syn")
    private Integer syn;
}
