package com.jmt.modules.commission.model.vo;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import com.jmt.modules.commission.entity.CmsArea;
import com.jmt.modules.commission.entity.CmsFile;
import com.jmt.modules.commission.entity.CmsPartnerInfo;
import com.jmt.modules.commission.entity.CmsPartnerProject;

import java.util.List;

/**
 *  @author: hanshangrong
 *  @Date: 2020/4/16 0016 11:42
 *  @Description:类注释
 *  合伙人详情
 */
@Data
@JsonAutoDetect(getterVisibility=JsonAutoDetect.Visibility.NONE)
public class CmsPartnerInfoVo extends CmsPartnerInfo {
    @JsonProperty("userCName")
    private String userCName;
    @JsonProperty("cmsArea")
    private CmsArea cmsArea;
    @JsonProperty("cmsFileList")
    private List<CmsFile> cmsFileList;
    @JsonProperty("projectList")
    private List<CmsPartnerProject> projectList;
}
