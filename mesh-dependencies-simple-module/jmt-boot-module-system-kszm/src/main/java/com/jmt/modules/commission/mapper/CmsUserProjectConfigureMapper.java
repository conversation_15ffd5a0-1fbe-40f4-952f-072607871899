package com.jmt.modules.commission.mapper;

import org.apache.ibatis.annotations.Param;
import com.jmt.modules.commission.entity.CmsUserProjectConfigure;

import java.util.List;

/**
 * 设备数量配置表(CmsUserProjectConfigure)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-04-21 16:51:46
 */
public interface CmsUserProjectConfigureMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    CmsUserProjectConfigure queryById(Integer id);


    /**
     * 通过实体作为筛选条件查询
     * @return 对象列表
     */
    List<CmsUserProjectConfigure> queryAll();

    /**
     * 新增数据
     *
     * @param cmsUserProjectConfigure 实例对象
     * @return 影响行数
     */
    int insert(CmsUserProjectConfigure cmsUserProjectConfigure);

    /**
     * 修改数据
     *
     * @param cmsUserProjectConfigure 实例对象
     * @return 影响行数
     */
    int update(CmsUserProjectConfigure cmsUserProjectConfigure);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Integer id);

}