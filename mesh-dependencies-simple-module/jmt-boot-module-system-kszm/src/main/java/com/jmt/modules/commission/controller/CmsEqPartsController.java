package com.jmt.modules.commission.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.jmt.common.api.vo.Result;
import com.jmt.modules.commission.entity.CmsEqParts;
import com.jmt.modules.commission.model.dto.AddCmsEqPartsDto;
import com.jmt.modules.commission.service.CmsEqPartsService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 *  @author: hanshangrong
 *  @Date: 2020/4/20 0020 10:26
 *  @Description:类注释
 *  配件
 */
@RestController
@RequestMapping("/cmsEqParts")
@Api("配件")
public class CmsEqPartsController {
    @Resource
    private CmsEqPartsService cmsEqPartsServiceImpl;
    @ApiOperation("新增配件")
    @RequestMapping(value = "/saveCmsEqParts",method = RequestMethod.POST)
    public Result<Object> saveCmsEqParts(@RequestBody @Valid AddCmsEqPartsDto addCmsEqPartsDto){
        return cmsEqPartsServiceImpl.saveCmsEqParts(addCmsEqPartsDto);
    }

    @ApiOperation("配件列表")
    @RequestMapping(value = "/listCmsEqParts",method = RequestMethod.POST)
    public Result<Page<CmsEqParts>> listCmsEqParts(@RequestParam(defaultValue = "1",required = false) Integer pageNo,
                                                   @RequestParam(defaultValue = "10",required = false) Integer pageSize){

        return cmsEqPartsServiceImpl.listCmsEqParts(pageNo,pageSize);
    }

    @ApiOperation("删除配件信息")
    @RequestMapping(value = "/deleteCmsEqParts/{id}",method = RequestMethod.GET)
    public Result<Object> deleteCmsEqParts(@PathVariable Integer id){
        return cmsEqPartsServiceImpl.deleteCmsEqParts(id);
    }

    @ApiOperation("修改配件信息")
    @RequestMapping(value = "/updateCmsEqParts",method = RequestMethod.POST)
    public Result<Object> updateCmsEqParts(@RequestBody AddCmsEqPartsDto addCmsEqPartsDto){

        return cmsEqPartsServiceImpl.updateCmsEqParts(addCmsEqPartsDto);
    }

    @ApiOperation("配件详情")
    @RequestMapping(value = "/getCmsEqPartsInfo/{id}",method = RequestMethod.POST)
    public Result<CmsEqParts> getCmsEqPartsInfo(@PathVariable Integer id){
        return cmsEqPartsServiceImpl.getCmsEqPartsInfo(id);
    }
}
