package com.jmt.modules.commission.mapper;

import com.jmt.modules.commission.entity.CmsOrderLog;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * (CmsOrderLog)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-09-21 18:26:08
 */
public interface CmsOrderLogMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    CmsOrderLog queryById(Integer id);

    /**
     * 查询指定行数据
     *
     * @param offset 查询起始位置
     * @param limit 查询条数
     * @return 对象列表
     */
    List<CmsOrderLog> queryAllByLimit(@Param("offset") int offset, @Param("limit") int limit);


    /**
     * 通过实体作为筛选条件查询
     *
     * @param cmsOrderLog 实例对象
     * @return 对象列表
     */
    List<CmsOrderLog> queryAll(CmsOrderLog cmsOrderLog);

    /**
     * 新增数据
     *
     * @param cmsOrderLog 实例对象
     * @return 影响行数
     */
    int insert(CmsOrderLog cmsOrderLog);

    /**
     * 修改数据
     *
     * @param cmsOrderLog 实例对象
     * @return 影响行数
     */
    int update(CmsOrderLog cmsOrderLog);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Integer id);

}