package com.jmt.modules.commission.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import com.jmt.modules.commission.entity.CmsPartnerBank;
import com.jmt.modules.commission.model.dto.CmsPartnerBankListDto;

import java.util.List;

/**
 * 合伙人区域表(CmsArea)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-04-13 15:53:14
 */
public interface CmsPartnerBankMapper {


    //新增
    int insert(CmsPartnerBank cmsPartnerBank);

    //查询列表
    List<CmsPartnerBank> queryList(Page<CmsPartnerBank> page,CmsPartnerBankListDto cms);

    //删除
    int delBank(@Param("id") Integer id);

    //查询单个信息
    CmsPartnerBank queryById(@Param("id") Integer id);

    //查询是否重复添加
    CmsPartnerBank queryRepeat(@Param("pCode") Integer ipCoded,@Param("pBankCode") String pBankCode);

    /**
     * 修改数据
     *
     * @param cmsPartnerBank 实例对象
     * @return 影响行数
     */
    int update(CmsPartnerBank cmsPartnerBank);
}