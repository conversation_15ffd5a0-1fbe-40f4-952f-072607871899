package com.jmt.modules.commission.entity;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
/**
 * 保障金表(CmsPartnerGuarantee)实体类
 *
 * <AUTHOR>
 * @since 2020-04-21 18:00:04
 */
@Data
public class CmsPartnerGuarantee implements Serializable {
    private static final long serialVersionUID = 151421382854321608L;
        
    @JsonProperty("id")
    private Integer id;
    /**
    * 用户编码
    */    
    @JsonProperty("pCode")
    private String pCode;
    /**
    * 金额
    */    
    @JsonProperty("pMoney")
    private BigDecimal pMoney;
    /**
    * 设备数量
    */    
    @JsonProperty("pEqNum")
    private Integer pEqNum;
    /**
    * 创建时间
    */    
    @JsonProperty("pCreatetime")
    private Date pCreatetime;
    /**
    * 配置表ID
    */    
    @JsonProperty("cofigId")
    private Integer cofigId;

    /**
     * 保证金类型 0 初始保证金 1新增保证金
     */
    @JsonProperty("guaranteeType")
    private Integer guaranteeType;
    /**
     * 交易流水
     */
    @JsonProperty("flowingWater")
    private String flowingWater;
    /**
     * 审核状态 0未审核 1已审核 2驳回
     */
    @JsonProperty("auditstatus")
    private Integer auditstatus;
    /**
     * 申请人登陆账户
     */
    @JsonProperty("applyUser")
    private String applyUser;
    /**
     * 驳回原因
     */
    @JsonProperty("reason")
    private String reason;


}