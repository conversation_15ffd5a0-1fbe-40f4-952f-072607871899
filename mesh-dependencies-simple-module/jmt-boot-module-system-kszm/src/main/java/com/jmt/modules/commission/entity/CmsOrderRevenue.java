package com.jmt.modules.commission.entity;

import java.math.BigDecimal;
import java.util.Date;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 订单收益表(CmsOrderRevenue)实体类
 *
 * <AUTHOR>
 * @since 2020-05-13 15:56:18
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class CmsOrderRevenue implements Serializable {

    private static final long serialVersionUID = 836382347160779624L;
    /**
    * id
    */    
    @JsonProperty("id")
    private Integer id;
    /**
    * 订单编号
    */    
    @JsonProperty("orderid")
    private String orderid;
    /**
    * 收益项目
    */    
    @JsonProperty("revenueItems")
    private String revenueItems;
    /**
    * 分佣金额
    */    
    @JsonProperty("commissionAmount")
    private BigDecimal commissionAmount;
    /**
    * 合伙人收益
    */    
    @JsonProperty("partnerIncome")
    private BigDecimal partnerIncome;
    /**
    * 餐厅收益
    */    
    @JsonProperty("restaurantRevenue")
    private BigDecimal restaurantRevenue;
    /**
    * 维护费用
    */    
    @JsonProperty("maintenanceFee")
    private BigDecimal maintenanceFee;
    /**
    * 推荐人分佣
    */    
    @JsonProperty("recommission")
    private BigDecimal recommission;
    /**
    * 合伙人名称
    */    
    @JsonProperty("partnerName")
    private String partnerName;
    /**
    * 商户编号
    */    
    @JsonProperty("merchantCode")
    private String merchantCode;
    /**
    * 商户名称
    */    
    @JsonProperty("merchantName")
    private String merchantName;
    /**
    * 设备编号
    */    
    @JsonProperty("eqid")
    private String eqid;
    /**
    * 合伙人编号
    */    
    @JsonProperty("partnerCode")
    private Integer partnerCode;
    /**
    * 推荐人编号
    */    
    @JsonProperty("refereeCode")
    private String refereeCode;
    /**
    * 推荐人名称
    */    
    @JsonProperty("refereeName")
    private String refereeName;
    /**
     * 收益项目id
     */
    @JsonProperty("revenueItemsId")
    private Integer revenueItemsId;
    /**
     *  是否同步商户积分
     */
    @JsonProperty("isSynShop")
    private Boolean isSynShop;
    /**
     * 订单创建时间
     */
    @JsonProperty("orderDate")
    private Date orderDate;
    /**
     * 创建时间
     */
    @JsonProperty("createDate")
    private Date createDate;


}