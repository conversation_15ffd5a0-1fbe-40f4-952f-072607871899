package com.jmt.modules.castingapplet.controller;

import io.swagger.annotations.ApiOperation;
import com.jmt.common.api.vo.Result;
import com.jmt.modules.castingapplet.model.vo.ShopInfoListVo;
import com.jmt.modules.castingapplet.service.ShoppingCartService;
import com.jmt.modules.castingapplet.util.ApiBaseAction;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/applets/shoppingCart")
public class ShoppingCartController extends ApiBaseAction {
    @Resource
    private ShoppingCartService shoppingCartServiceImpl;

    @ApiOperation("新增点位")
    @RequestMapping(value = "/addShoppingCart",method = RequestMethod.POST)
    public Result<Object> addShoppingCart(@RequestBody List<ShopInfoListVo> shopInfoListVo){
        Long userId=this.getUserId();
        boolean isSuccess=shoppingCartServiceImpl.addShoppingCart(userId.toString(),shopInfoListVo);
        if(isSuccess){
            return Result.ok("增加成功");
        }
        return Result.error("增加失败");
    }

    @ApiOperation("删除点位")
    @RequestMapping(value = "/cutShoppingCart",method = RequestMethod.POST)
    public Result<Object> cutShoppingCart(@RequestBody List<String> mCode){
        Long userId=this.getUserId();
        boolean isSuccess=shoppingCartServiceImpl.cutShoppingCart(userId.toString(),mCode);
        if(isSuccess){
            return Result.ok("删除成功");
        }
        return Result.error("删除失败");
    }

    @ApiOperation("获取购物车点位数")
    @RequestMapping(value = "/getTheNumberOfShoppingCarts",method = RequestMethod.GET)
    public Result<Map<String, Integer>> getTheNumberOfShoppingCarts(){
        Result<Map<String, Integer>> result=new Result<>();
        Map<String, Integer> map=new HashMap<>();
        Long userId=this.getUserId();
        Integer num=shoppingCartServiceImpl.getTheNumberOfShoppingCarts(userId.toString());
        map.put("numberOfPoints",num);
        result.setResult(map);
        result.setCode(200);
        return result;
    }

    @ApiOperation("获取购物车详情")
    @RequestMapping(value = "/listShoppingCart",method = RequestMethod.GET)
    public Result<List<ShopInfoListVo>> listShoppingCart(){
        return shoppingCartServiceImpl.listShoppingCart(this.getUserId());
    }

}
