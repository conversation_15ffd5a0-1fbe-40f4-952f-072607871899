package com.jmt.modules.commissionapi.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jmt.common.api.vo.Result;
import com.jmt.modules.commission.entity.CmsPartnerBank;
import com.jmt.modules.commission.model.dto.CmsPartnerBankDTO;
import com.jmt.modules.commission.model.dto.CmsPartnerBankListDto;

public interface CmsPanterBankAppService {
    //新增
    Result<Object> save(CmsPartnerBankDTO cmsPartnerBank);

    //查询
    Result<Page<CmsPartnerBank>> queryList(CmsPartnerBankListDto cmsPartnerBankListDto);

    //删除
    Result<Object> delCmsBank(Integer id);

    //查询详细信息
    Result<CmsPartnerBank> queryBankInfo(Integer id);

    Result<Object> update(CmsPartnerBankDTO cmsPartnerBank);
}
