package com.jmt.modules.commission.model.dto;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 *  @author: hanshangrong
 *  @Date: 2020/5/6 0006 16:25
 *  @Description:类注释
 *  新增商户信息
 */
@Data
public class AddShopInfoDto {
    /**
     * 商户编号
     */
    @JsonProperty("mCode")
    @NotBlank(message = "商户编号为空")
    private String mCode;
    /**
     * 商户名称
     */
    @JsonProperty("mName")
    @NotBlank(message = "商户名称为空")
    private String mName;
    /**
     * 联系人
     */
    @JsonProperty("mContacts")
    @NotBlank(message = "联系人为空")
    private String mContacts;
    /**
     * 联系电话
     */
    @JsonProperty("mPhone")
    @NotBlank(message = "联系电话为空")
    private String mPhone;
    /**
     * 店铺面积
     */
    @JsonProperty("mMeasure")
    @NotNull(message = "店铺面积为空")
    private double mMeasure;
    /**
     * 所属省份
     */
    @JsonProperty("mProvinces")
    @NotBlank(message = "所属省份为空")
    private String mProvinces;
    /**
     * 所属市区
     */
    @JsonProperty("mCity")
    @NotBlank(message = "所属市区为空")
    private String mCity;
    /**
     * 所属区县
     */
    @JsonProperty("mCountiy")
    @NotBlank(message = "所属区县为空")
    private String mCountiy;

    /**
     * 所属省份
     */
    @JsonProperty("mProvincesNum")
    @NotNull(message = "所属省份编号为空")
    private int mProvincesNum;
    /**
     * 所属市区
     */
    @JsonProperty("mCityNum")
    @NotNull(message = "所属市区编号为空")
    private int mCityNum;
    /**
     * 所属区县
     */
    @JsonProperty("mCountiyNum")
    @NotNull(message = "所属区县编号为空")
    private int mCountiyNum;
    /**
     * 商户地址
     */
    @JsonProperty("mAddress")
    @NotBlank(message = "商户地址为空")
    private String mAddress;
    /**
     * 人均消费
     */
    @JsonProperty("mConsume")
    @NotBlank(message = "人均消费为空")
    private String mConsume;
    /**
     * 营业时间
     */
    @JsonProperty("mOpenTime")
    @NotBlank(message = "营业时间为空")
    private String mOpenTime;
    /**
     * 经度
     */
    @JsonProperty("mLongitude")
    @NotBlank(message = "经度为空")
    private String mLongitude;
    /**
     * 纬度
     */
    @JsonProperty("mLatitude")
    @NotBlank(message = "纬度为空")
    private String mLatitude;
    /**
     * 类型
     */
    @JsonProperty("mType")
    @NotBlank(message = "商户类型为空")
    private String mType;
    /**
     * 圆桌数量
     */
    @JsonProperty("mAmountDeskY")
    @NotNull(message = "圆桌数量为空")
    private int mAmountDeskY;
    /**
     * 方桌数量
     */
    @JsonProperty("mAmountDeskF")
    @NotNull(message = "方桌数量为空")
    private int mAmountDeskF;
    /**
     * 营业执照
     */
    @JsonProperty("mBusinessLicense")
    @NotBlank(message = "营业执照为空")
    private String mBusinessLicense;
    /**
     * 推广人员
     */
    @JsonProperty("promoterNo")
    @NotNull(message = "推广人员编号为空")
    private int promoterNo;

    /**
     * 商户星级
     */
    @JsonProperty("mLevel")
    @NotBlank(message = "商户星级为空")
    private String mLevel;
    /**
     * 人流量
     */
    @JsonProperty("mTraffic")
    @NotNull(message = "人流量为空")
    private int mTraffic;
    /**
     * 菜系
     */
    @JsonProperty("mCuisine")
    @NotBlank(message = "菜系为空")
    private String mCuisine;
    /**
     * 推荐餐厅编号
     */
    @JsonProperty("recommendedRestaurant")
    private String recommendedRestaurant;
    /**
     * 商户编号
     */
    @JsonProperty("bUserCode")
    private String bUserCode;
    /**
     * 音量
     */
    @JsonProperty("OverallVolume")
    private String OverallVolume;
    /**
     * 时间段1
     */
    @JsonProperty("open_close_one")
    private String open_close_one;
    /**
     * 时间段2
     */
    @JsonProperty("open_close_two")
    private String open_close_two;
    /**
     * 时间段3
     */
    @JsonProperty("open_close_three")
    private String open_close_three;

    /**
     * 静音音量
     */
    @JsonProperty("muteVolume")
    private String muteVolume;

    /**
     * 静音时长
     */
    @JsonProperty("muteTime")
    private String muteTime;

    /**
     * 合伙人编号
     */
    @JsonProperty("pId")
    private String pId;

    /**
     * 合伙人名称
     */
    @JsonProperty("pName")
    private String pName;

    /**
     * 合伙人手机号
     */
    @JsonProperty("pPhoneNum")
    private String pPhoneNum;

    /**
     * 每分钟充电消耗积分数
     */
    @JsonProperty("mPaymentIntegral")
    private int mPaymentIntegral;

    /**
     * 注册时间
     */
    @JsonProperty("createTime")
    private Date createTime;

    /**
     * 商户文件
     */
    @JsonProperty("userFile")
    private List<@Valid AddCmsFile> userFile;



}
