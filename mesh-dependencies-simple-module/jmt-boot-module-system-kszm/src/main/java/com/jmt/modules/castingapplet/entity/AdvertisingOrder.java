package com.jmt.modules.castingapplet.entity;

import java.math.BigDecimal;
import java.util.Date;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.models.auth.In;
import lombok.Data;
/**
 * (AdvertisingOrder)实体类
 *
 * <AUTHOR>
 * @since 2020-05-22 09:46:13
 */
@Data
public class AdvertisingOrder implements Serializable {

    private static final long serialVersionUID = 832521513286023317L;
    /**
    * 主键
    */    
    @JsonProperty("id")
    private Integer id;
    /**
    * 订单编号
    */    
    @JsonProperty("orderId")
    private String orderId;
    /**
    * 用户编号
    */    
    @JsonProperty("userId")
    private String userId;
    /**
    * 投广视频url
    */    
    @JsonProperty("orderMp4Url")
    private String orderMp4Url;
    /**
    * 视频MD5
    */    
    @JsonProperty("orderMp4Md5")
    private String orderMp4Md5;
    /**
     * 视频时长
     */
    @JsonProperty("orderMp4Duration")
    private Integer orderMp4Duration;
    /**
    * 广告投放开始时间
    */    
    @JsonProperty("launchStartTime")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date launchStartTime;
    /**
    * 广告投放结束时间
    */    
    @JsonProperty("launchSendTime")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date launchSendTime;
    /**
    * 投放餐厅数
    */    
    @JsonProperty("launchRestaurant")
    private Integer launchRestaurant;
    /**
    * 投放设备数
    */    
    @JsonProperty("launchEquipment")
    private Integer launchEquipment;
    /**
    * 总金额
    */    
    @JsonProperty("totalAmount")
    private BigDecimal totalAmount;
    /**
    * 折扣id
    */    
    @JsonProperty("discountId")
    private Integer discountId;
    /**
    * 订单状态 1 已取消、2 待支付、3 审核中、4 已驳回、5 投放中、6 已完成 7已过期 8 已退款
    */    
    @JsonProperty("orderStatus")
    private Integer orderStatus;
    /**
    * 过期时间
    */    
    @JsonProperty("expirationTime")
    private Date expirationTime;
    /**
     *  收益 0 未计算 1 已计算
     */
    @JsonProperty("income")
    private Integer income;
    /**
    * 创建时间
    */    
    @JsonProperty("createTime")
    private Date createTime;
    /**
    * 修改时间
    */    
    @JsonProperty("updateTime")
    private Date updateTime;
}