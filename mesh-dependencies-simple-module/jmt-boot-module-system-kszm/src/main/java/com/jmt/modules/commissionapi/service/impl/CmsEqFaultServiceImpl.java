package com.jmt.modules.commissionapi.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import com.jmt.common.api.vo.Result;
import com.jmt.modules.commission.entity.CmsEqFault;
import com.jmt.modules.commission.entity.CmsPushMessageUnread;
import com.jmt.modules.commission.entity.CmsShopInfo;
import com.jmt.modules.commission.mapper.CmsEqFaultMapper;
import com.jmt.modules.commission.mapper.CmsPushMessageUnreadMapper;
import com.jmt.modules.commission.mapper.CmsShopInfoMapper;
import com.jmt.modules.commission.model.dto.AddCmsEqFaultDto;
import com.jmt.modules.commission.model.dto.CmsEqFaultAppListDto;
import com.jmt.modules.commission.model.vo.CmsEqFaultInfoAppVo;
import com.jmt.modules.commissionapi.service.CmsEqFaultService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 *  @author: hanshangrong
 *  @Date: 2020/4/23 0023 15:01
 *  @Description:类注释
 *  设备维护列表
 */
@Service
@Slf4j
public class CmsEqFaultServiceImpl implements CmsEqFaultService {
    @Resource
    private CmsEqFaultMapper cmsEqFaultMapper;

    @Resource
    private CmsPushMessageUnreadMapper cmsPushMessageUnreadMapper;

    @Resource
    private CmsShopInfoMapper cmsShopInfoMapper;

    @Value("${asyn.request.bside.url}")
    private String bUrl;



    @Override
    public Result<Page<CmsEqFault>> listCmsEqFault(CmsEqFaultAppListDto cmsEqFaultAppListDto) {
        Result<Page<CmsEqFault>> result=new Result<>();
        Page<CmsEqFault> page=new Page<>(cmsEqFaultAppListDto.getPageNo(),cmsEqFaultAppListDto.getPageSize());
        List<CmsEqFault> list=cmsEqFaultMapper.queryAllApp(page,cmsEqFaultAppListDto);
        //修改所有设备维护消息已读
        CmsPushMessageUnread cmsPushMessageUnread=new CmsPushMessageUnread();
        cmsPushMessageUnread.setRemindUserid(Integer.valueOf(cmsEqFaultAppListDto.getPid()));
        cmsPushMessageUnread.setMessageStatus(2);
        cmsPushMessageUnread.setMessageType(2);
        cmsPushMessageUnread.setUpdateDate(new Date());
        cmsPushMessageUnreadMapper.update(cmsPushMessageUnread);
        page.setRecords(list);
        result.setResult(page);
        result.setCode(200);
        return result;
    }


    @Override
    public Result<CmsEqFaultInfoAppVo> getEqFaultInfo(Map<String,String> map) {
        Result<CmsEqFaultInfoAppVo> result=new Result<>();
        if(StringUtils.isBlank(map.get("orderId"))){
            return result.error500("维护订单编号为空");
        }
        CmsEqFaultInfoAppVo cmsEqFaultInfoAppVo=cmsEqFaultMapper.queryByOrderId(map.get("orderId"));
        result.setResult(cmsEqFaultInfoAppVo);
        result.setCode(200);
        return result;
    }


    @Override
    public Result<Object> stateChange(Map<String, String> map) {
        if(StringUtils.isBlank(map.get("orderId")) && StringUtils.isBlank(map.get("status"))){
            return Result.error("订单编号或状态为空");
        }
        if(!StringUtils.isNumeric(map.get("status")) || Integer.parseInt(map.get("status")) >1 || Integer.parseInt(map.get("status")) <0){
            return Result.error("状态非法入参");
        }
        CmsEqFaultInfoAppVo cmsEqFault=cmsEqFaultMapper.queryByOrderId(map.get("orderId"));
        if(cmsEqFault == null){
            return Result.error("维护订单不存在");
        }
        if(cmsEqFault.getStatus() == 1){
            return Result.error("该维护订单已解决");
        }
        int eqFault=cmsEqFaultMapper.updateStatusByOrderId(1,map.get("orderId"));
        if(eqFault == 1){
            return Result.ok("状态更改成功");
        }
        return Result.error("状态更改失败");
    }


    @Override
    public Result<Object> newSynEqFault(AddCmsEqFaultDto addCmsEqFaultDto){
        CmsEqFault cmsEqFault=new CmsEqFault();
        BeanUtils.copyProperties(addCmsEqFaultDto,cmsEqFault);
        cmsEqFault.setStatus(0);
        int eqFaultResult=cmsEqFaultMapper.insert(cmsEqFault);
        if(eqFaultResult >0){
            //查询商户信息
            CmsShopInfo cmsShopInfo=cmsShopInfoMapper.queryByMCode(addCmsEqFaultDto.getmCode());
            //记录该消息未读
            CmsPushMessageUnread cmsPushMessageUnread=new CmsPushMessageUnread();
            cmsPushMessageUnread.setMessageStatus(1);
            cmsPushMessageUnread.setMessageType(2);
            cmsPushMessageUnread.setMessageId(cmsEqFault.getId().toString());
            cmsPushMessageUnread.setCreateDate(new Date());
            cmsPushMessageUnread.setRemindUserid(Integer.valueOf(cmsShopInfo.getPSubCode()));
            cmsPushMessageUnreadMapper.insert(cmsPushMessageUnread);
            return Result.ok("信息同步成功");
        }
        return Result.error("信息同步失败");
    }
}
