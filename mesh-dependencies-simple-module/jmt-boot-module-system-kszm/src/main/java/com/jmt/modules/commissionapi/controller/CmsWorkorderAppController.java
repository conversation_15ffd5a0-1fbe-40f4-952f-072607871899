package com.jmt.modules.commissionapi.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jmt.modules.commission.entity.CmsWorkorder;
import com.jmt.modules.commission.mapper.CmsWorkorderMapper;
import io.swagger.annotations.ApiOperation;
import com.jmt.common.api.vo.Result;
import com.jmt.modules.commission.model.dto.CmsWorkorderListAppDto;
import com.jmt.modules.commission.model.dto.UpdateWorkOrderAppDto;
import com.jmt.modules.commission.model.vo.CmsWorkOrderInfoAppVo;
import com.jmt.modules.commission.model.vo.CmsWorkorderListAppVo;
import com.jmt.modules.commissionapi.service.CmsWorkorderAppService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 *  @author: hanshangrong
 *  @Date: 2020/4/29 0029 16:32
 *  @Description:类注释
 *
 */
@RestController
@Slf4j
@RequestMapping("/app/workorder")
public class CmsWorkorderAppController {

    @Resource
    private CmsWorkorderAppService cmsWorkorderAppServiceImpl;
    @Resource
    private CmsWorkorderMapper workorderMapper;

    @ApiOperation("部署订单列表")
    @RequestMapping(value = "/listWorkorder",method = RequestMethod.POST)
    public Result<Page<CmsWorkorderListAppVo>> listWorkorder(@RequestBody CmsWorkorderListAppDto cmsWorkorderListAppDto, HttpServletRequest request){
        log.info("========进入部署订单列表");
        return cmsWorkorderAppServiceImpl.listWorkorder(cmsWorkorderListAppDto,request);
    }

    @ApiOperation("获取部署订单详情")
    @RequestMapping(value = "/getWorkOrderInfo",method = RequestMethod.POST)
    public Result<CmsWorkOrderInfoAppVo> getWorkOrderInfo(@RequestBody Map<String,String> map){
        log.info("========进入部署订单详情getWorkOrderInfo");
        Result<CmsWorkOrderInfoAppVo> result= cmsWorkorderAppServiceImpl.getWorkOrderInfo(map);
        log.info("========返回：getWorkOrderInfo");
        log.info(result.toString());
        return result;
    }


    @ApiOperation("审核部署订单")
    @RequestMapping(value = "/audit",method = RequestMethod.POST)
    public Result<Object> audit(@RequestBody Map<String,String> map){
        log.info("========进入审核部署订单");
        return cmsWorkorderAppServiceImpl.audit(map);
    }


    @ApiOperation("更新部署工单")
    @RequestMapping(value = "/updateWorkOrder",method = RequestMethod.POST)
    public Result<Object> updateWorkOrder(@RequestBody UpdateWorkOrderAppDto updateWorkOrderAppDto){
        log.info("========进入更新部署工单");
        return cmsWorkorderAppServiceImpl.updateWorkOrder(updateWorkOrderAppDto);
    }


}
