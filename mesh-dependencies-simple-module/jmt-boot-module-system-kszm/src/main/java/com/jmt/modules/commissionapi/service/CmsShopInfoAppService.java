package com.jmt.modules.commissionapi.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jmt.common.api.vo.Result;
import com.jmt.modules.commission.model.dto.*;
import com.jmt.modules.commission.model.vo.CmsShopEqVO;
import com.jmt.modules.commission.model.vo.CmsShopInfoAppVo;
import com.jmt.modules.commission.model.vo.CmsShopInfoListAppVo;
import com.jmt.modules.commission.model.vo.ShopAuditListVO;

/**
 *  @author: hanshangrong
 *  @Date: 2020/4/27 0027 10:07
 *  @Description:类注释
 *  商户信息app
 */
public interface CmsShopInfoAppService {
    /**
     * 描述:  商户列表
     * @method  listShopInfo
     * @date: 2020/4/27 0027
     * @author: hanshangrong
     * @param cmsShopInfoListAppDto
     * @return com.jmt.common.api.vo.Result<com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.jmt.modules.commission.model.vo.CmsShopInfoListAppVo>>
     */
    Result<Page<CmsShopInfoListAppVo>> listShopInfo(CmsShopInfoListAppDto cmsShopInfoListAppDto);

    /**
     * 描述:  获取商户详情
     * @method  getShopInfo
     * @date: 2020/4/27 0027
     * @author: hanshangrong
     * @param mCode
     * @return com.jmt.common.api.vo.Result<com.jmt.modules.commission.model.vo.CmsShopInfoAppVo>
     */
    Result<CmsShopInfoAppVo> getShopInfo(String mCode);

    /**
     * 描述:  修改商户信息
     * @method  updateShopInfo
     * @date: 2020/4/27 0027
     * @author: hanshangrong
     * @param updateShopInfoAppDto
     * @return com.jmt.common.api.vo.Result<java.lang.Object>
     */
    Result<Object> updateShopInfo(UpdateShopInfoAppDto updateShopInfoAppDto);

    /**
     * 描述:  B端注册商户同步接口    合伙人注册表
     * @method  newShopSyn
     * @date: 2020/5/6 0006
     * @author: hanshangrong
     * @param addShopInfoDto
     * @return com.jmt.common.api.vo.Result<java.lang.Object>
     */
    Result<Object> newShopSyn(AddShopInfoDto addShopInfoDto);

    /**
     * 描述:  同步广告机商户设备信息
     * @method  syncDevice
     * @date: 2020/5/7 0007
     * @author: hanshangrong
     * @param addCmsEqDto
     * @return com.jmt.common.api.vo.Result<java.lang.Object>
     */
    boolean syncDevice(AddCmsEqDto addCmsEqDto);

    /**
     * 描述:  B端部署工单同步接口
     * @method  newWorkOrderSyn
     * @date: 2020/5/7 0007
     * @author: hanshangrong
     * @param addWorkOrderDto
     * @return com.jmt.common.api.vo.Result<java.lang.Object>
     */
    Result<Object> newWorkOrderSyn(AddWorkOrderDto addWorkOrderDto);

    /**
     * @Description:  获取设备设置详情
     * @@method: getShopEqInfo
     * @Author: hsr
     * @Date: 2020/9/18 13:37
     * @param json:
     * @return: com.jmt.common.api.vo.Result<com.jmt.modules.commission.model.vo.CmsShopEqVO>
     * @throw Exception:
     **/
    Result<CmsShopEqVO> getShopEqInfo(JSONObject json);

    /**
     * @Description:    商户审核列表
     * @@method: listShopAudit
     * @Author: hsr
     * @Date: 2020/9/18 13:37
     * @param params:
     * @return: com.jmt.common.api.vo.Result<com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.jmt.modules.commission.model.vo.ShopAuditListVO>>
     * @throw Exception:
     **/
    Result<Page<ShopAuditListVO>> listShopAudit(JSONObject params);

    /**
     * @Description:  审核商户
     * @@method: verifyShop
     * @Author: hsr
     * @Date: 2020/9/18 13:38
     * @param params:
     * @return: com.jmt.common.api.vo.Result<java.lang.Object>
     * @throw Exception:
     **/
    Result<Object> verifyShop(JSONObject params);
}
