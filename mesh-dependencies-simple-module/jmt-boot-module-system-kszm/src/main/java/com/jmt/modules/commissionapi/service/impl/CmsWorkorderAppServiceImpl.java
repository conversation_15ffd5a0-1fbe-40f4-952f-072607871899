package com.jmt.modules.commissionapi.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jmt.modules.commission.model.vo.CmsEqNum;
import com.jmt.modules.commission.model.vo.CmsShopEqListAppVo;
import com.jmt.modules.system.util.UserUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import com.jmt.common.api.vo.Result;
import com.jmt.common.constant.CommissionConstant;
import com.jmt.common.util.RedisUtil;
import com.jmt.modules.commission.entity.*;
import com.jmt.modules.commission.mapper.*;
import com.jmt.modules.commission.model.dto.CmsWorkorderListAppDto;
import com.jmt.modules.commission.model.dto.UpdateWorkOrderAppDto;
import com.jmt.modules.commission.model.vo.CmsWorkOrderInfoAppVo;
import com.jmt.modules.commission.model.vo.CmsWorkorderListAppVo;
import com.jmt.modules.commission.service.UserBusinessActService;
import com.jmt.modules.commissionapi.service.CmsWorkorderAppService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;


@Service
@Slf4j
public class CmsWorkorderAppServiceImpl implements CmsWorkorderAppService {

    @Resource
    private CmsWorkorderMapper cmsWorkorderMapper;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private CmsShopInfoMapper cmsShopInfoMapper;

    @Resource
    private CmsPartnerInfoMapper cmsPartnerInfoMapper;

    @Resource
    private CmsFileMapper cmsFileMapper;

    @Resource
    private CmsShopEqMapper cmsShopEqMapper;

    @Resource
    private CmsApprovalMapper cmsApprovalMapper;

    @Resource
    private UserBusinessActService userBusinessActServiceImpl;

    @Resource
    private CmsPushMessageUnreadMapper cmsPushMessageUnreadMapper;

    @Resource
    private CmsPartnerProjectMapper cmsPartnerProjectMapper;

    @Resource
    private CmsPartnerSubMapper cmsPartnerSubMapper;

    @Resource
    private CmsOrderRevenueMapper cmsOrderRevenueMapper;

    @Resource
    private CmsShopSubMapper cmsShopSubMapper;
    @Resource
    private CmsEqMapper cmsEqMapper;

    @Override
    public Result<Page<CmsWorkorderListAppVo>> listWorkorder(CmsWorkorderListAppDto cmsWorkorderListAppDto, HttpServletRequest request) {
        Object userObject=UserUtil.getRedisUser();
        CmsPartnerInfo cmsPartnerInfo=null;
        CmsPartnerSub cmsPartnerSub=null;
        if(userObject!=null){
            if(userObject instanceof CmsPartnerInfo){
                log.info("{}--->",userObject);
                cmsPartnerInfo= (CmsPartnerInfo) userObject;
            }else if(userObject instanceof CmsPartnerSub){
                cmsPartnerSub= (CmsPartnerSub) userObject;
                log.info("{}--->",userObject);
            }
        }
        log.info("{}--->",userObject);
        Result<Page<CmsWorkorderListAppVo>> result=new Result<>();
        Page<CmsWorkorderListAppVo> page=new Page<>(cmsWorkorderListAppDto.getPageNo(),cmsWorkorderListAppDto.getPageSize());
        if(cmsPartnerInfo != null&&cmsPartnerInfo.getPartnerType()==2){
            cmsWorkorderListAppDto.setPId(cmsPartnerInfo.getPId());
            List<CmsWorkorderListAppVo> listAppVoList=cmsWorkorderMapper.queryAllByPartnerIdApp(page,cmsWorkorderListAppDto);
            page.setRecords(listAppVoList);
        }else if(cmsPartnerSub != null){
            cmsWorkorderListAppDto.setPId(cmsPartnerSub.getPSubCode());
            List<CmsWorkorderListAppVo> listAppVoLists=cmsWorkorderMapper.queryAllByPartnerSubIdApp(page,cmsWorkorderListAppDto);
            page.setRecords(listAppVoLists);
        }
        //修改所有部署消息已读
        CmsPushMessageUnread cmsPushMessageUnread=new CmsPushMessageUnread();
        cmsPushMessageUnread.setRemindUserid(cmsPartnerInfo!=null?cmsPartnerInfo.getPId():cmsPartnerSub.getPSubCode());
        cmsPushMessageUnread.setMessageStatus(2);
        cmsPushMessageUnread.setMessageType(1);
        cmsPushMessageUnread.setUpdateDate(new Date());
        cmsPushMessageUnreadMapper.update(cmsPushMessageUnread);
        result.setCode(200);
        result.setResult(page);
        return result;
    }


    @Override
    public Result<CmsWorkOrderInfoAppVo> getWorkOrderInfo(Map<String, String> map) {
        Result<CmsWorkOrderInfoAppVo> result=new Result<>();
        if(StringUtils.isBlank(map.get("orderId"))){
            return result.error500("工单编号为空");
        }
        //查询工单信息
        CmsWorkOrderInfoAppVo cmsWorkOrderInfoAppVo=cmsWorkorderMapper.queryByOrderId(map.get("orderId"));
        //查询部署单对应的部署照片
         List<CmsFile> fileList=cmsFileMapper.queryFileByWorkId(map.get("orderId"));
        for (CmsFile file:fileList) {
            cmsWorkOrderInfoAppVo.getFileUrl().add(file.getFileUrl());
        }

        //mod by lsw on 0516
        List<CmsShopEqListAppVo> eqList = this.loadWorkOrderEqInfo(cmsWorkOrderInfoAppVo.getMWorkorder(),cmsWorkOrderInfoAppVo.getMCode());
        cmsWorkOrderInfoAppVo.setCmsShopEqList(eqList);
        result.setCode(200);
        result.setResult(cmsWorkOrderInfoAppVo);
        return result;
    }

    public  List<CmsShopEqListAppVo> loadWorkOrderEqInfo(String orderId,String shopCode){
        List<CmsShopEqListAppVo> eqList = cmsShopEqMapper.queryAllByOrderId(orderId);
        List<CmsEqNum>  eqlist2= this.cmsEqMapper.queryEqTypeNumByMcode(shopCode);
        for (CmsEqNum eqNum:eqlist2){
            CmsShopEqListAppVo vo=CmsShopEqListAppVo.findByEqType(eqList,eqNum.getEqType());
            if(vo!=null) vo.setMDeployNum(eqNum.getEqNum());
        }
        return eqList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<Object> audit(Map<String, String> map) {
        if(StringUtils.isBlank(map.get("orderId"))){
            return Result.error("部署单号为空");
        }
        if(StringUtils.isBlank(map.get("auditStatus"))
                || !StringUtils.isNumeric(map.get("auditStatus"))){
            return Result.error("审核状态非法");
        }
        //查询部署信息
        CmsWorkorder workOrder=cmsWorkorderMapper.queryByWorkId(map.get("orderId"));
        if(workOrder == null){
            return Result.error("部署工单不存在");
        }
        if(workOrder.getMOrderStatus() != 0){
            return Result.error("该工单已审核");
        }
        //查询商户信息
        CmsShopInfo cmsShopInfo=cmsShopInfoMapper.queryByMCode(workOrder.getMCode());
        if(cmsShopInfo == null){
            return Result.error("不存在商户信息");
        }
        if(CommissionConstant.SHOP_STATUS_PAST != Integer.parseInt(cmsShopInfo.getMStatus())){
            return Result.error("商户未审核通过");
        }
        //新增审核信息
        CmsApproval cmsApproval=new CmsApproval();
        cmsApproval.setPCode(workOrder.getMCode());
        cmsApproval.setMApprovalTime(new Date());
        cmsApproval.setMWorkorder(workOrder.getMWorkorder());
        cmsApproval.setPUserName(workOrder.getPExtensionCode());
        //修改审核工单
        int verifyStatus=0;
        if(CommissionConstant.VERIFY_PAST.equals(map.get("auditStatus"))){
            //审核通过
            cmsApproval.setMOrderStatus(1);
            workOrder.setMOrderStatus(1);
            workOrder.setPExtensionCode(map.get("deployerCode"));
            verifyStatus = 1;
        }else if (CommissionConstant.VERIFY_TURN_DOWN.equals(map.get("auditStatus"))){
            //驳回
            cmsApproval.setMOrderStatus(3);
            workOrder.setMOrderStatus(3);
            verifyStatus = 2;
        }
        cmsWorkorderMapper.update(workOrder);
        cmsApprovalMapper.insert(cmsApproval);
        //插入未读信息
        CmsPushMessageUnread cmsPushMessageUnread=new CmsPushMessageUnread();
        cmsPushMessageUnread.setCreateDate(new Date());
        cmsPushMessageUnread.setMessageId(workOrder.getId().toString());
        cmsPushMessageUnread.setMessageType(1);
        cmsPushMessageUnread.setMessageStatus(1);
        cmsPushMessageUnread.setRemindUserid(StringUtils.isNumeric(workOrder.getPExtensionCode())?Integer.valueOf(workOrder.getPExtensionCode()):null);
        cmsPushMessageUnreadMapper.insert(cmsPushMessageUnread);
        //修改B端部署订单状态
        UserBusinessAct businessAct = new UserBusinessAct();
        businessAct.setBorder(workOrder.getMWorkorder());
        businessAct.setStatus(verifyStatus);
        userBusinessActServiceImpl.update(businessAct);
        return Result.ok("审核成功");
    }


    // TODO 分润的位置
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<Object> updateWorkOrder(UpdateWorkOrderAppDto updateWorkOrderAppDto) {
        CmsWorkorder cmsWorkorder = cmsWorkorderMapper.queryByWorkId(updateWorkOrderAppDto.getMWorkorder());

        if (cmsWorkorder.getMOrderStatus() == 2) {
            return Result.error("该工单已部署");
        }

        //更新部署工单状态
        cmsWorkorderMapper.updateStatusByOrderId(updateWorkOrderAppDto.getMWorkorder(), 2);
        //更新商户部署照
        cmsFileMapper.updateInArrays(new Integer[]{updateWorkOrderAppDto.getFileId()}, updateWorkOrderAppDto.getMCode(), updateWorkOrderAppDto.getMWorkorder());

        // 部署完成进行分润
        return divCommission(updateWorkOrderAppDto.getMWorkorder());
    }


    public Result<Object> divCommission(String orderId){
        CmsWorkorder cmsWorkorder = cmsWorkorderMapper.queryByWorkId(orderId);
        // 将申请设备数改为实际部署设备数量 : mod by lsw
        List<CmsShopEqListAppVo> cmsShopEqListAppVos = this.loadWorkOrderEqInfo(cmsWorkorder.getMWorkorder(),cmsWorkorder.getMCode());
        BigDecimal total = new BigDecimal("0");
        for (CmsShopEqListAppVo cmsShopEqListAppVo : cmsShopEqListAppVos) {
            total = total.add(new BigDecimal(cmsShopEqListAppVo.getMDeployNum().toString()));
        }

        // 查找部署业务员的运营商
        CmsPartnerInfo cmsPartnerInfo = cmsPartnerInfoMapper.queryCmsPartnerBySubCode(Integer.parseInt(cmsWorkorder.getPExtensionCode()));
        // 查找运营商分润比例
        CmsPartnerProject deploy = cmsPartnerProjectMapper.queryByPartnerCodeAndProjectId(cmsPartnerInfo.getPId(), 27);

        if (deploy == null) {
            return Result.error("未设置运营商分润比例");
        }
        // 计算运营商分润金额
        BigDecimal deployMoney = new BigDecimal(deploy.getCommissionMin().toString()).multiply(total);
        // 更新运营商金额
        cmsPartnerInfo.setPProfit(deployMoney);
        // 查找业务员
        CmsPartnerSub cmsPartnerSub = cmsPartnerSubMapper.querySubCode(Integer.parseInt(cmsWorkorder.getPExtensionCode()));
        BigDecimal subMoney = new BigDecimal("0");
        if (cmsPartnerSub != null) {
            // 计算业务员分润金额
            subMoney = new BigDecimal(cmsPartnerSub.getDeploymentIncome()).multiply(total);
        }
        CmsShopInfo cmsShopInfo=cmsShopInfoMapper.queryByMCode(cmsWorkorder.getMCode());
        // 新增收益表
        CmsOrderRevenue cmsOrderRevenue = new CmsOrderRevenue();
        cmsOrderRevenue.setOrderid("B" + cmsWorkorder.getMWorkorder() + new Date().getTime());
        cmsOrderRevenue.setRevenueItems("部署收益");
        cmsOrderRevenue.setCommissionAmount(deployMoney);
        cmsOrderRevenue.setMaintenanceFee(deployMoney);
        cmsOrderRevenue.setRecommission(subMoney);
        cmsOrderRevenue.setPartnerName(cmsPartnerInfo.getPName());
        cmsOrderRevenue.setPartnerCode(cmsPartnerInfo.getPId());
        cmsOrderRevenue.setRefereeCode(cmsPartnerSub.getPSubCode().toString());
        cmsOrderRevenue.setRefereeName(cmsPartnerSub.getPSubName());
        cmsOrderRevenue.setRevenueItemsId(27);
        cmsOrderRevenue.setMerchantCode(cmsShopInfo.getMCode());
        cmsOrderRevenue.setMerchantName(cmsShopInfo.getMName());
        cmsOrderRevenue.setOrderDate(new Date());
        cmsOrderRevenue.setCreateDate(new Date());

        cmsOrderRevenueMapper.insert(cmsOrderRevenue);

        // 查找推荐业务员
             // 计算推荐业务员分润金额
            subMoney = new BigDecimal(cmsPartnerSub.getLayingIncome()).multiply(total);
            // 查找推荐业务员的运营商
            cmsPartnerInfo = cmsPartnerInfoMapper.queryCmsPartnerBySubCode(cmsPartnerSub.getPSubCode());
            // 查找运营商分润比例
            CmsPartnerProject spread = cmsPartnerProjectMapper.queryByPartnerCodeAndProjectId(cmsPartnerInfo.getPId(), 28);
            // 计算运营商分润金额
            BigDecimal spreadMoney = new BigDecimal(spread.getCommissionMin().toString()).multiply(total);
            // 更新运营商金额
            cmsPartnerInfo.setPProfit(spreadMoney);

            // 新增收益表
            cmsOrderRevenue = new CmsOrderRevenue();
            cmsOrderRevenue.setOrderid("T" + cmsWorkorder.getMWorkorder() + new Date().getTime());
            cmsOrderRevenue.setRevenueItems("推广收益");
            cmsOrderRevenue.setCommissionAmount(spreadMoney);
            cmsOrderRevenue.setMaintenanceFee(spreadMoney);
            cmsOrderRevenue.setRecommission(subMoney);
            cmsOrderRevenue.setPartnerName(cmsPartnerInfo.getPName());
            cmsOrderRevenue.setPartnerCode(cmsPartnerInfo.getPId());
            cmsOrderRevenue.setRefereeCode(cmsPartnerSub.getPSubCode().toString());
            cmsOrderRevenue.setRefereeName(cmsPartnerSub.getPSubName());
            cmsOrderRevenue.setRevenueItemsId(28);
            cmsOrderRevenue.setMerchantCode(cmsShopInfo.getMCode());
            cmsOrderRevenue.setMerchantName(cmsShopInfo.getMName());
            cmsOrderRevenue.setOrderDate(new Date());
            cmsOrderRevenue.setCreateDate(new Date());

            cmsOrderRevenueMapper.insert(cmsOrderRevenue);

        return Result.ok("更新成功");
    }


}
