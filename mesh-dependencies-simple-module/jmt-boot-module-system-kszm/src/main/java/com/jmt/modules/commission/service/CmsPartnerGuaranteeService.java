package com.jmt.modules.commission.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.models.auth.In;
import com.jmt.common.api.vo.Result;
import com.jmt.modules.commission.entity.CmsPartnerGuarantee;
import com.jmt.modules.commission.model.dto.AddCmsPartnerGuaranteeDto;
import com.jmt.modules.commission.model.dto.CmsPartnerGuaranteeAuditDto;
import com.jmt.modules.commission.model.dto.CmsPartnerGuaranteeListDto;
import com.jmt.modules.commission.model.vo.CmsPartnerGuaranteeInfoVo;
import com.jmt.modules.commission.model.vo.CmsPartnerGuaranteeListVo;

import java.util.Map;

/**
 *  @author: hanshangrong
 *  @Date: 2020/4/23 0023 10:42
 *  @Description:类注释
 *  保证金业务接口
 */
public interface CmsPartnerGuaranteeService {

    /**
     * 描述:  新增保证金
     * @method  savePartnerGuarantee
     * @date: 2020/4/23 0023
     * @author: hanshangrong
     * @param addCmsPartnerGuaranteeDto
     * @return com.jmt.common.api.vo.Result<java.lang.Object>
     */
    Result<Object> savePartnerGuarantee(AddCmsPartnerGuaranteeDto addCmsPartnerGuaranteeDto);


    /**
     * 描述:  保证金列表
     * @method  listPartnerGuarantee
     * @date: 2020/4/23 0023
     * @author: hanshangrong
     * @param cmsPartnerGuaranteeListDto
     * @return com.jmt.common.api.vo.Result<com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.jmt.modules.commission.model.vo.CmsPartnerGuaranteeListVo>>
     */
    Result<Page<CmsPartnerGuaranteeListVo>> listPartnerGuarantee(CmsPartnerGuaranteeListDto cmsPartnerGuaranteeListDto);


    /**
     * 描述:  查询保证金详情
     * @method  getPartnerGuaranteeInfo
     * @date: 2020/4/23 0023
     * @author: hanshangrong
     * @param id
     * @return com.jmt.common.api.vo.Result<com.jmt.modules.commission.model.vo.CmsPartnerGuaranteeInfoVo>
     */
    Result<CmsPartnerGuaranteeInfoVo> getPartnerGuaranteeInfo(Integer id);


    /**
     * 描述:  保证金审核
     * @method  audit
     * @date: 2020/4/23 0023
     * @author: hanshangrong
     * @param cmsPartnerGuaranteeAuditDto
     * @return com.jmt.common.api.vo.Result<java.lang.Object>
     */
    Result<Object> audit(CmsPartnerGuaranteeAuditDto cmsPartnerGuaranteeAuditDto);
}
