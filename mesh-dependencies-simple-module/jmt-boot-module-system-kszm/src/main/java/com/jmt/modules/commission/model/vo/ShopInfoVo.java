package com.jmt.modules.commission.model.vo;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import com.jmt.modules.commission.entity.CmsFile;

import java.math.BigDecimal;
import java.util.List;

@Data
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE)
public class ShopInfoVo {
    @JsonProperty("id")
    private Integer id;
    /**
     * 商户编号
     */
    @JsonProperty("mCode")
    private String mCode;
    /**
     * 商户名称
     */
    @JsonProperty("mName")
    private String mName;
    /**
     * 所属省份
     */
    @JsonProperty("mProvinces")
    private String mProvinces;
    /**
     * 所属市区
     */
    @JsonProperty("mCity")
    private String mCity;
    /**
     * 所属区县
     */
    @JsonProperty("mCountiy")
    private String mCountiy;

    /**
     * 商户地址
     */
    @JsonProperty("address")
    private String address;
    /**
     * 人均消费
     */
    @JsonProperty("mConsume")
    private String mConsume;

    /**
     * 经度
     */
    @JsonProperty("mLongitude")
    private String mLongitude;
    /**
     * 纬度
     */
    @JsonProperty("mLatitude")
    private String mLatitude;

    /**
     * 圆桌数量
     */
    @JsonProperty("mAmountDeskY")
    private Integer mAmountDeskY;
    /**
     * 方桌数量
     */
    @JsonProperty("mAmountDeskF")
    private Integer mAmountDeskF;

    /**
     * 商户星级
     */
    @JsonProperty("mLevel")
    private String mLevel;
    /**
     * 人流量
     */
    @JsonProperty("mTraffic")
    private Integer mTraffic;
    /**
     * 人流量
     */
    @JsonProperty("pName")
    private String pName;
    /**
     * 人流量
     */
    @JsonProperty("eqNum")
    private Integer eqNum;
    /**
     * 原价
     */
    @JsonProperty("originalPrice")
    private BigDecimal originalPrice;
    /**
     * 现价
     */
    @JsonProperty("presentPrice")
    private BigDecimal presentPrice;
    /**
     * 描述
     */
    @JsonProperty("describe")
    private String describe;

    /**
     * 联系人
     */
    @JsonProperty("mContacts")
    private String mContacts;
    /**
     * 联系电话
     */
    @JsonProperty("mPhone")
    private String mPhone;
    /**
     * 店铺面积
     */
    @JsonProperty("mMeasure")
    private Double mMeasure;
    /**
     * 类型
     */
    @JsonProperty("mType")
    private String mType;
    /**
     * 菜系
     */
    @JsonProperty("mCuisine")
    private String mCuisine;

    @JsonProperty("partnerPhone")
    private String partnerPhone;
    @JsonProperty("mAttribute")
    private String mAttribute;

    @JsonProperty("file")
    private List<CmsFile> file;

}
