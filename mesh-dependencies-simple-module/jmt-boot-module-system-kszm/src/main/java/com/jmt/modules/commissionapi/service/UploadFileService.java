package com.jmt.modules.commissionapi.service;

import com.jmt.common.api.vo.Result;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 *  @author: hanshangrong
 *  @Date: 2020/4/27 0027 18:11
 *  @Description:类注释
 *  文件上传
 */
public interface UploadFileService {
    Result<Map<String, String>> uploadFile(MultipartFile file,Integer type);
    Result<Map<String, String>> uploadFiles(MultipartFile file,Integer type,Integer imgType);
}
