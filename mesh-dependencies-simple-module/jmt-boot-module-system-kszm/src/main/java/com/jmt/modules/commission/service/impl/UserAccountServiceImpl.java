package com.jmt.modules.commission.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.jmt.modules.commission.mapper.UserAccountMapper;
import com.jmt.modules.commission.service.UserAccountService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@DS("jmtbsideAccount")
public class UserAccountServiceImpl implements UserAccountService {

    @Resource
    private UserAccountMapper userAccountMapper;

    public Integer getAllNumer(){
        return userAccountMapper.getAllNumer();
    }
}
