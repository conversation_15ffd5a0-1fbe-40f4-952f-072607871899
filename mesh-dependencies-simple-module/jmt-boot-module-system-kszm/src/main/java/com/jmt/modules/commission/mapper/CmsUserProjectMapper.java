package com.jmt.modules.commission.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import com.jmt.modules.commission.entity.CmsUserProject;
import com.jmt.modules.commission.model.vo.CmsUserProjectListVo;

import java.util.List;


/**
 * 用户分佣表(CmsUserProject)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-04-13 10:14:29
 */
public interface CmsUserProjectMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    CmsUserProject queryById(Integer id);

    /**
     * 分佣列表
     *
     * @param page
     * @return 对象列表
     */
    List<CmsUserProjectListVo> queryAllByLimit(Page<CmsUserProjectListVo> page);


    /**
     * 通过实体作为筛选条件查询
     *
     * @param cmsUserProject 实例对象
     * @return 对象列表
     */
    List<CmsUserProject> queryAll(CmsUserProject cmsUserProject);

    /**
     * 新增数据
     *
     * @param cmsUserProject 实例对象
     * @return 影响行数
     */
    int insert(CmsUserProject cmsUserProject);

    /**
     * 修改数据
     *
     * @param cmsUserProject 实例对象
     * @return 影响行数
     */
    int update(CmsUserProject cmsUserProject);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Integer id);

    int deleteByCofigId(Integer cofigId);

}