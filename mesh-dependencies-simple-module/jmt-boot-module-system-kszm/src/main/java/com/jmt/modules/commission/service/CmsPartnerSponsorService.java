package com.jmt.modules.commission.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jmt.common.api.vo.Result;
import com.jmt.modules.commission.entity.CmsPartnerSponsor;
import com.jmt.modules.commission.model.dto.CmsPartnerSponsorListDto;
import com.jmt.modules.commission.model.vo.CmsPartnerSponsorListVo;
import com.jmt.modules.commission.model.vo.RecommendPeopleVo;

import java.util.List;

/**
 *  @author: hanshangrong
 *  @Date: 2020/4/18 0018 15:44
 *  @Description:类注释
 *  合伙人推荐信息业务接口
 */
public interface CmsPartnerSponsorService {

    /**
     * 描述:  合伙人推荐信息列表
     * @method  listCmsPartnerSponsor
     * @date: 2020/4/18 0018
     * @author: hanshangrong
     * @param cmsPartnerSponsorListDto
     * @return com.jmt.common.api.vo.Result<com.baomidou.mybatisplus.extension.plugins.pagination.Page<java.util.List<com.jmt.modules.commission.model.vo.CmsPartnerSponsorListVo>>>
     */
    Result<Page<CmsPartnerSponsorListVo>> listCmsPartnerSponsor(CmsPartnerSponsorListDto cmsPartnerSponsorListDto);

    void deleteCmsPartnerSponsor(Integer id);

    /**
     * 描述:  发放推荐人奖励
     * @method  sendRewards
     * @date: 2020/4/18 0018
     * @author: hanshangrong
     * @param id
     * @param pCode
     * @return com.jmt.common.api.vo.Result<java.lang.Object>
     */
    Result<Object> sendRewardsTransactional(Integer id,Integer pCode);

    /**
     * 描述:  检验密码
     * @method  verifyPassword
     * @date: 2020/4/20 0020
     * @author: hanshangrong
     * @param password
     * @return boolean
     */
    boolean verifyPassword(String password);

    /**
     * 描述:  推荐人列表
     * @@method: listRecommendPeople
     * @Author: hsr
     * @Date: 2020/9/21 9:34
     * @param param:
     * @return: com.jmt.common.api.vo.Result<com.jmt.modules.commission.model.vo.RecommendPeopleVo>
     * @throw Exception:
     **/
    Result<Page<RecommendPeopleVo>> listRecommendPeople(JSONObject param);
}
