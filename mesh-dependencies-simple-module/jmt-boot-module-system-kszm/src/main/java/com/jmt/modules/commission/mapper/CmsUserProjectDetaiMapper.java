package com.jmt.modules.commission.mapper;

import org.apache.ibatis.annotations.Param;
import com.jmt.modules.commission.entity.CmsUserProjectDetai;

import java.util.List;

/**
 * 用户分佣项目明细表(CmsUserProjectDetai)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-04-13 10:14:49
 */
public interface CmsUserProjectDetaiMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    CmsUserProjectDetai queryById(Integer id);

    /**
     * 查询指定行数据
     *
     * @param offset 查询起始位置
     * @param limit 查询条数
     * @return 对象列表
     */
    List<CmsUserProjectDetai> queryAllByLimit(@Param("offset") int offset, @Param("limit") int limit);


    /**
     * 通过实体作为筛选条件查询
     *
     * @param cmsUserProjectDetai 实例对象
     * @return 对象列表
     */
    List<CmsUserProjectDetai> queryAll(CmsUserProjectDetai cmsUserProjectDetai);

    /**
     * 新增数据
     *
     * @param cmsUserProjectDetai 实例对象
     * @return 影响行数
     */
    int insert(CmsUserProjectDetai cmsUserProjectDetai);

    /**
     * 修改数据
     *
     * @param cmsUserProjectDetai 实例对象
     * @return 影响行数
     */
    int update(CmsUserProjectDetai cmsUserProjectDetai);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Integer id);

    /**
     * 描述:  查询分佣明细
     * @method  queryByCId
     * @date: 2020/4/30 0030
     * @author: hanshangrong
     * @param userCId
     * @return com.jmt.modules.commission.entity.CmsUserProjectDetai
     */
    List<CmsUserProjectDetai> queryByCId(Integer userCId);
}