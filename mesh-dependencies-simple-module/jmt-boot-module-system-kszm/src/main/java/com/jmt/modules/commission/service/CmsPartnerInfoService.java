package com.jmt.modules.commission.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jmt.common.api.vo.Result;
import com.jmt.modules.commission.entity.CmsPartnerApply;
import com.jmt.modules.commission.entity.CmsPartnerInfo;
import com.jmt.modules.commission.entity.CmsPartnerSub;
import com.jmt.modules.commission.model.dto.AddCmsPartnerInfoDto;
import com.jmt.modules.commission.model.dto.CmsPartnerInfoListDto;
import com.jmt.modules.commission.model.vo.*;
import java.util.List;

/**
 *  @author: hanshangrong
 *  @Date: 2020/4/13 0013 16:00
 *  @Description:类注释
 *  合伙人业务接口
 */
public interface CmsPartnerInfoService {
    /**
     * 描述:  新增合伙人
     * @method  savePartnerInfoTransactional
     * @date: 2020/4/13 0013
     * @author: hanshangrong    区
     * @param addCmsPartnerInfoDto
     * @return com.jmt.common.api.vo.Result<java.lang.Object>
     */
    Result<Object> savePartnerInfoTransactional(AddCmsPartnerInfoDto addCmsPartnerInfoDto);

    /**
     * 描述:  合伙人列表
     * @method  listCmsPartnerInfo
     * @date: 2020/4/13 0013
     * @author: hanshangrong
     * @param cmsPartnerInfoListDto
     * @return com.jmt.common.api.vo.Result<com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.jmt.modules.commission.model.vo.CmsPartnerInfoListVo>>
     */
    Result<Page<CmsPartnerInfoListVo>> listCmsPartnerInfo(CmsPartnerInfoListDto cmsPartnerInfoListDto);

    /**
     * 描述:  更改合伙人账户状态
     * @method  editPartnerStatus
     * @date: 2020/4/16 0016
     * @author: hanshangrong
     * @param partnerId
     * @param status  3、冻结 4、启用
     * @return com.jmt.common.api.vo.Result<java.lang.Object>
     */
    Result<Object> editPartnerStatus(Integer partnerId, Integer status);

    /**
     * 描述:  获取合伙人详情
     * @method  getCmsPartnerInfo
     * @date: 2020/4/16 0016
     * @author: hanshangrong
     * @param id
     * @return com.jmt.common.api.vo.Result<com.jmt.modules.commission.model.vo.CmsPartnerInfoVo>
     */
    Result<CmsPartnerInfoVo> getCmsPartnerInfo(Integer id);

    /**
     * 描述:  修改合伙人信息
     * @method  updatePartnerInfoTransactional
     * @date: 2020/4/16 0016
     * @author: hanshangrong
     * @param addCmsPartnerInfoDto
     * @return com.jmt.common.api.vo.Result<java.lang.Object>
     */
    Result<Object> updatePartnerInfoTransactional(AddCmsPartnerInfoDto addCmsPartnerInfoDto);

    /**
     * 描述:  审核合伙人信息
     * @method  auditCmsPartnerInfo
     * @date: 2020/4/16 0016
     * @author: hanshangrong
     * @param params
     * @return com.jmt.common.api.vo.Result<java.lang.Object>
     */
    Result<Object> auditCmsPartnerInfoTransactional(JSONObject params);

    /**
     * 描述:  合伙人收益详情
     * @method  getPartnerProfitInfo
     * @date: 2020/4/17 0017
     * @author: hanshangrong
     * @param pid
     * @return com.jmt.common.api.vo.Result<com.jmt.modules.commission.model.vo.CmsPartnerProfitInfoVo>
     */
    Result<CmsPartnerProfitInfoVo> getPartnerProfitInfo(Integer pid);

    /**
     * 描述:  合伙人审核信息
     * @method  getCmsPartnerApply
     * @date: 2020/4/17 0017
     * @author: hanshangrong
     * @param pid
     * @return com.jmt.common.api.vo.Result<java.util.List<com.jmt.modules.commission.entity.CmsPartnerApply>>
     */
    Result<List<CmsPartnerApply>> getCmsPartnerApply(Integer pid);

    /**
     * 描述:  搜索合伙人
     * @method  listCmsPartnerInfos
     * @date: 2020/4/18 0018 
     * @author: hanshangrong
     * @param pName
     * @return com.jmt.common.api.vo.Result<java.util.List<com.jmt.modules.commission.model.vo.CmsPartnerInfoListsVo>>
     */
    Result<List<CmsPartnerInfoListsVo>> listCmsPartnerInfos(String pName);

    /**
     * 描述:  通过手机号
     * @method  getCmsPartnerInfoByUserName
     * @date: 2020/4/22 0022
     * @author: hanshangrong
     * @param userName
     * @return com.jmt.modules.commission.entity.CmsPartnerInfo
     */
    CmsPartnerInfo getCmsPartnerInfoByUserName(String userName);

    CmsPartnerInfo getCmsPartnerInfoById(Integer id);

    /**
     *  修改合伙人信息
     * @param cmsPartnerInfo
     */
    void updatePartnerInfo(CmsPartnerInfo cmsPartnerInfo);
    /**
     *  注册用户列表
     * @param json
     * @return
     */
    Result<Page<RegisterPantnerVo>> listRegister(JSONObject json);

    /**
     * @param id: 用户id
     * <AUTHOR>
     * @description TODO
     * @date 2023/6/30 15:15
     */
    void deleteRegister(Integer id);

    /**
     *  修改注册用户信息
     * @param cmsPartnerInfoAp
     * @return
     */
    Result<Object> updateRegister(AddCmsPartnerInfoDto cmsPartnerInfoAp);

    /**
     *  子账户列表
     * @param param
     * @return
     */
    Result<Page<CmsPartnerSub>> listPartnerSub(JSONObject param);

    /**
     * 运营商列表
     */
    List<CmsPartsInfoOperatorVo> listOperator();
}
