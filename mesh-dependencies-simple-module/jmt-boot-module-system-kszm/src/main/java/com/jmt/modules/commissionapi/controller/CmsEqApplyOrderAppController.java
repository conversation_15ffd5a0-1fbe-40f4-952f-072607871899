package com.jmt.modules.commissionapi.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiOperation;
import com.jmt.common.api.vo.Result;
import com.jmt.modules.commission.entity.CmsEqApplyOrder;
import com.jmt.modules.commission.model.dto.AddCmsEqApplyOrderAppDto;
import com.jmt.modules.commission.model.vo.CmaEqNumApplyAppVo;
import com.jmt.modules.commissionapi.service.CmsEqApplyOrderAppService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

/**
 *  @author: hanshangrong
 *  @Date: 2020/4/25 0025 16:57
 *  @Description:类注释
 *  设备申请 app
 */
@RestController
@RequestMapping("/app/eqApplyOrderApp")
public class CmsEqApplyOrderAppController {

    @Resource
    private CmsEqApplyOrderAppService cmsEqApplyOrderAppServiceImpl;

    @ApiOperation("获取申请设备设备信息")
    @RequestMapping(value = "/getEqNum/{pId}",method = RequestMethod.GET)
    public Result<CmaEqNumApplyAppVo> getEqNum(@PathVariable Integer pId){
        return cmsEqApplyOrderAppServiceImpl.getEqInfo(pId);
    }

    @ApiOperation("新增设备申请")
    @RequestMapping(value = "/saveCmsEqNumApply",method = RequestMethod.POST)
    public Result<Object> saveCmsEqNumApply(@RequestBody AddCmsEqApplyOrderAppDto addCmsEqApplyOrderAppDto){
       return cmsEqApplyOrderAppServiceImpl.saveCmsEqNumApply(addCmsEqApplyOrderAppDto);
    }

    @ApiOperation("设备申请列表")
    @RequestMapping(value = "/listEqApplyOrder",method = RequestMethod.POST)
    public Result<Page<CmsEqApplyOrder>> listEqApplyOrder(@RequestBody Map<String,String> map){
        return cmsEqApplyOrderAppServiceImpl.listEqApplyOrder(map);
    }

    @ApiOperation("确认收货")
    @RequestMapping(value = "/confirmReceipt",method = RequestMethod.POST)
    public Result<Object> confirmReceipt(@RequestBody JSONObject param){
        return cmsEqApplyOrderAppServiceImpl.confirmReceipt(param);
    }

    @ApiOperation("取消订单")
    @RequestMapping(value="/cancelOrder",method = RequestMethod.POST)
    public Result<Object> cancelOrder(@RequestBody JSONObject param){
        return cmsEqApplyOrderAppServiceImpl.cancelOrder(param);
    }



}