package com.jmt.modules.castingapplet.entity;

import java.util.Date;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 片源库(FilmLibrary)实体类
 *
 * <AUTHOR>
 * @since 2020-05-26 15:03:26
 */
@Data
@Accessors(chain = true)
public class FilmLibrary implements Serializable {

    private static final long serialVersionUID = 716221401006446989L;
    /**
    * 主键
    */    
    @JsonProperty("filmId")
    private Integer filmId;
    /**
    * 片源的MD5标示
    */    
    @JsonProperty("filmMd5")
    private String filmMd5;
    /**
    * 广告合同编号
    */    
    @JsonProperty("adContractNum")
    private String adContractNum;
    /**
    * 片源名称
    */    
    @JsonProperty("filmName")
    private String filmName;
    /**
    * 片源存储地址 磁盘存储位置
    */    
    @JsonProperty("filmDisk")
    private String filmDisk;
    /**
    * 片源的URL路径
    */    
    @JsonProperty("filmUrl")
    private String filmUrl;
    /**
    * 视频时长
    */    
    @JsonProperty("filmDuration")
    private Integer filmDuration;
    /**
    * 0:未审核 1：通过审核 2：未通过审核 3：播放中 4：历史片源
    */    
    @JsonProperty("filmStatus")
    private Integer filmStatus;
    /**
    * 投放开始时间
    */    
    @JsonProperty("filmStartTime")
    private Date filmStartTime;
    /**
    * 投放结束时间
    */    
    @JsonProperty("filmEndTime")
    private Date filmEndTime;
    /**
    * 1:非广告视频 2广告视频  4：全网充电视频 5：全网商家视频 3：紧急推送
    */    
    @JsonProperty("filmType")
    private Integer filmType;
    /**
    * 视频位置 2号位和3号位，只有全完商家视频的时候才有这个属性
    */    
    @JsonProperty("playPosition")
    private Integer playPosition;
    /**
    * 1:总部投放
    */    
    @JsonProperty("filmPuttype")
    private Integer filmPuttype;
    /**
    * 视频中购买物品的地址
    */    
    @JsonProperty("imgUrl")
    private String imgUrl;
    /**
    * 更新时间
    */    
    @JsonProperty("updateTime")
    private Date updateTime;
    /**
    * 创建时间
    */    
    @JsonProperty("createTime")
    private Date createTime;

}