package com.jmt.modules.commission.service.impl;

import org.apache.commons.lang3.StringUtils;
import com.jmt.common.api.vo.Result;
import com.jmt.common.constant.CommissionConstant;
import com.jmt.modules.commission.entity.CmsPushmsg;
import com.jmt.modules.commission.mapper.CmsOrderRevenueMapper;
import com.jmt.modules.commission.mapper.CmsPushmsgMapper;
import com.jmt.modules.commission.model.vo.PartServantHomePageVo;
import com.jmt.modules.commission.model.vo.RevenueBreakdownVo;
import com.jmt.modules.commission.service.PartServantHomePageService;
import com.jmt.modules.commission.util.BigDecimalUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Service
public class PartServantHomePageServiceImpl implements PartServantHomePageService {

    @Resource
    private CmsOrderRevenueMapper cmsOrderRevenueMapper;

    @Resource
    private CmsPushmsgMapper cmsPushmsgMapper;

    @Override
    public Result<PartServantHomePageVo> homepage(Map<String, String> map) {
        Result<PartServantHomePageVo> result=new Result<>();
        if(StringUtils.isBlank(map.get("optionType"))){
            return result.error500("optionType入参为空");
        }
        List<RevenueBreakdownVo>  breakdownVoList= null;
        PartServantHomePageVo partServantHomePageVo=new PartServantHomePageVo();
        switch (map.get("optionType")){
            case CommissionConstant.COMMISSION_HOMEPAGE_CLASSIFCATION_OPTION_TYPE_CHOPSTICKS:
                breakdownVoList=cmsOrderRevenueMapper.queryTotalRevenue(1);
                break;
            case CommissionConstant.COMMISSION_HOMEPAGE_CLASSIFCATION_OPTION_TYPE_ADVERTISINGREVENUE:
                breakdownVoList=cmsOrderRevenueMapper.queryTotalRevenue(2);
                break;
            case CommissionConstant.COMMISSION_HOMEPAGE_CLASSIFCATION_OPTION_TYPE_CENDREVENUE:
                breakdownVoList=cmsOrderRevenueMapper.queryTotalRevenue(3);
                break;
            case CommissionConstant.COMMISSION_HOMEPAGE_CLASSIFCATION_OPTION_TYPE_BENDREVENUE:
                breakdownVoList=cmsOrderRevenueMapper.queryTotalRevenue(4);
                break;
            case CommissionConstant.COMMISSION_HOMEPAGE_CLASSIFCATION_OPTION_TYPE_AllSTATISTICS:
                //查询总收益
                Map<String, BigDecimal> income=null;
                //全部收益
                BigDecimal allGains=new BigDecimal("0");
                //总收益
                BigDecimal totalRevenue=new BigDecimal("0");
                for(int i =1;i<=4;i++){
                    income=cmsOrderRevenueMapper.queryCommissionProjectIncome(i);
                    allGains= BigDecimalUtils.addBig(allGains,income.get("totalRevenue"));
                    totalRevenue=BigDecimalUtils.addBig(totalRevenue,income.get("headquartersRevenue"));
                    switch (i){
                        case 1:
                            partServantHomePageVo.setChopsticksRevenue(income.get("totalRevenue"));
                            break;
                        case 2:
                            partServantHomePageVo.setAdvertisingRevenue(income.get("totalRevenue"));
                            break;
                        case 3:
                            partServantHomePageVo.setCEndRevenue(income.get("totalRevenue"));
                            break;
                        case 4:
                            partServantHomePageVo.setBEndRevenue(income.get("totalRevenue"));
                            break;
                        default:
                            break;
                    }
                }
                partServantHomePageVo.setAllGains(allGains);
                partServantHomePageVo.setTotalRevenue(totalRevenue);
                break;
            default:
                return result.error500("optionType非法入参");
        }
        partServantHomePageVo.setRevenueBreakdown(breakdownVoList);
        //查询推送消息
        List<CmsPushmsg> pushmsgList=cmsPushmsgMapper.listCmsPushmsgs();
        partServantHomePageVo.setPushMessage(pushmsgList);
        result.setCode(200);
        result.setResult(partServantHomePageVo);
        return result;
    }
}
