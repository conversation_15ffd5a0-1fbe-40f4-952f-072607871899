package com.jmt.modules.castingapplet.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class AdvertisingOrderListVo {

    /**
     * 主键
     */
    @JsonProperty("id")
    private Integer id;
    /**
     * 订单编号
     */
    @JsonProperty("orderId")
    private String orderId;
    /**
     * 用户编号
     */
    @JsonProperty("userId")
    private String userId;
    /**
     * 广告投放开始时间
     */
    @JsonProperty("launchStartTime")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date launchStartTime;
    /**
     * 广告投放结束时间
     */
    @JsonProperty("launchSendTime")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date launchSendTime;
    /**
     * 投放餐厅数
     */
    @JsonProperty("launchRestaurant")
    private Integer launchRestaurant;
    /**
     * 投放设备数
     */
    @JsonProperty("launchEquipment")
    private Integer launchEquipment;
    /**
     * 总金额
     */
    @JsonProperty("totalAmount")
    private BigDecimal totalAmount;



    /**
     * 折扣id
     */
    @JsonProperty("discountId")
    private Integer discountId;
    /**
     * 订单状态 1 已取消、2 待支付、3 审核中、4 已驳回、5 投放中、6 已完成 7已过期 8 已退款
     */
    @JsonProperty("orderStatus")
    private Integer orderStatus;
    /**
     * 过期时间
     */
    @JsonProperty("expirationTime")
    private Date expirationTime;
    /**
     * 创建时间
     */
    @JsonProperty("createTime")
    private Date createTime;
    /**
     * 修改时间
     */
    @JsonProperty("updateTime")
    private Date updateTime;
}
