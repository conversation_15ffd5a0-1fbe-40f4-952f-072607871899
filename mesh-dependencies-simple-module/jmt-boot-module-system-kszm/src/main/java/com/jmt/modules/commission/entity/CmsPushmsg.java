package com.jmt.modules.commission.entity;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 信息推送表(CmsPushmsg)实体类
 *
 * <AUTHOR>
 * @since 2020-05-18 10:28:17
 */
@Data
@Accessors(chain = true)
public class CmsPushmsg implements Serializable {
    private static final long serialVersionUID = 126316840021267401L;
        
    @JsonProperty("id")
    private Integer id;
    /**
    * 内容
    */    
    @JsonProperty("content")
    private String content;
    /**
    * 标题
    */    
    @JsonProperty("title")
    private String title;
    /**
    * 推送对象
    */    
    @JsonProperty("pushtarg")
    private Integer pushtarg;
    /**
    * 推送类型 0、单人 1、全体
    */    
    @JsonProperty("pushType")
    private Integer pushType;
    /**
     *  消息类型 0系统消息 1 设备订单消息
     */
    @JsonProperty("messageType")
    private Integer messageType;
    /**
     * 创建时间
     */
    @JsonProperty("createTime")
    private Date createTime;
    /**
     * 接收人
     */
    @JsonProperty("receiver")
    private String receiver;

}