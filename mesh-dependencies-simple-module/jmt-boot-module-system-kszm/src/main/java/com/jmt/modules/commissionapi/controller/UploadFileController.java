package com.jmt.modules.commissionapi.controller;

import io.swagger.annotations.ApiOperation;
import com.jmt.common.api.vo.Result;
import com.jmt.modules.commissionapi.service.UploadFileService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Map;

/**
 *  @author: hanshangrong
 *  @Date: 2020/4/27 0027 18:04
 *  @Description:类注释
 *  文件上传
 */
@RestController
@RequestMapping("/app/upload")
public class UploadFileController {

    @Resource
    private UploadFileService uploadFileServiceImpl;

    @ApiOperation("文件上传")
    @RequestMapping(value = "/uploadFile",method = RequestMethod.POST)
    public Result<Map<String, String>> uploadFile(MultipartFile file,Integer fileType){
        return uploadFileServiceImpl.uploadFile(file,fileType);
    }

    @ApiOperation("文件上传")
    @RequestMapping(value = "/uploadFiles",method = RequestMethod.POST)
    public Result<Map<String, String>> uploadFile(MultipartFile file,Integer fileType,Integer imgType){
        return uploadFileServiceImpl.uploadFiles(file,fileType,imgType);
    }
}
