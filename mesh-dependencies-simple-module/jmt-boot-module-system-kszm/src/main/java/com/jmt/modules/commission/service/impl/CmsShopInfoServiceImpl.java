package com.jmt.modules.commission.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jmt.common.api.vo.Result;
import com.jmt.modules.castingapplet.entity.CmsShopInfoDetai;
import com.jmt.modules.castingapplet.mapper.CmsShopInfoDetaiMapper;
import com.jmt.modules.client.device.DeviceClient;
import com.jmt.modules.commission.entity.*;
import com.jmt.modules.commission.mapper.*;
import com.jmt.modules.commission.model.dto.*;
import com.jmt.modules.commission.model.vo.*;
import com.jmt.modules.commission.service.CmsShopInfoService;
import com.jmt.modules.commission.service.CmsStarService;
import com.jmt.modules.commissionapi.async.Async;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;


@Service
@Slf4j
public class CmsShopInfoServiceImpl extends ServiceImpl<CmsShopInfoMapper, CmsShopInfo> implements CmsShopInfoService {
    @Resource
    private CmsShopInfoMapper cmsShopInfoMapper;

    @Resource
    private CmsEqMapper cmsEqMapper;

    @Resource
    private CmsEqSetMapper cmsEqSetMapper;

    @Resource
    private CmsPartnerInfoMapper cmsPartnerInfoMapper;

    @Resource
    private CmsFileMapper cmsFileMapper;

    @Resource
    private CmsOrderRevenueMapper cmsOrderRevenueMapper;

    @Resource
    private CmsShopProjectInfoMapper cmsShopProjectInfoMapper;

    @Resource
    private CmsShopInfoDetaiMapper cmsShopInfoDetaiMapper;

    @Resource
    private CmsPartnerProjectMapper cmsPartnerProjectMapper;

    @Resource
    private CmsStarService cmsStarServiceImpl;

    @Resource
    private Async async;

    @Resource
    private DeviceClient deviceClient;

    @Resource
    private CmsChargingGradeMapper cmsChargingGradeMapper;


    @Override
    public Result<Page<CmsShopInfoListVo>> listShopInfo(CmsShopInfoListDto cmsShopInfoListDto) {
        Result<Page<CmsShopInfoListVo>> result = new Result<>();
        Page<CmsShopInfoListVo> page = new Page<>(cmsShopInfoListDto.getPageNo(), cmsShopInfoListDto.getPageSize());
        List<CmsShopInfoListVo> listVoList = cmsShopInfoMapper.queryAllLike(page, cmsShopInfoListDto);
        page.setRecords(listVoList);
        result.setResult(page);
        return result;
    }

    @Override
    public void deleteShopInfo(Integer id) {
        cmsShopInfoMapper.deleteById(id);
    }

    @Override
    public Result<List<CmsShopInfo>> listShopInfo2() {
        Result<List<CmsShopInfo>> result = new Result<>();

        List<CmsShopInfo> list = cmsShopInfoMapper.queryAll(new CmsShopInfo());

        result.setResult(list);
        result.setCode(200);
        result.setSuccess(true);

        return result;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<Object> distributionShopInfo(String mCode, Integer pId) {
        if (pId == null || StringUtils.isBlank(mCode)) {
            return Result.error("合伙人编号或商家编号为空");
        }
        CmsPartnerInfo cmsPartnerInfo = cmsPartnerInfoMapper.queryByPId(pId);
        Optional.ofNullable(cmsPartnerInfo).ifPresent(partnerInfo -> {
            List<CmsPartnerProject> projectList = cmsPartnerProjectMapper.queryAll(new CmsPartnerProject().setPartnerCode(pId));
            if (!projectList.isEmpty()) {
                cmsShopProjectInfoMapper.deleteByMcode(mCode);
                projectList.forEach(project -> {
                    if (project.getIsCommissionShop()) {
                        cmsShopProjectInfoMapper.insert(new CmsShopProjectInfo()
                                .setMCode(mCode)
                                .setProjectid(project.getProjectId())
                                .setProportion(project.getCommissionMin()));
                    }
                });
            }
        });
        return cmsShopInfoMapper.updateSubCodeByMcode(mCode, pId) == 1 ? Result.ok("分配成功") : Result.error("分配失败");
    }


    @Override
    public Result<CmsShopInfoVo> getShopInfo(String mCode) {
        Result<CmsShopInfoVo> result = new Result<>();
        //获取主体信息
        CmsShopInfoVo cmsShopInfoVo = cmsShopInfoMapper.queryByMcode(mCode);
        if (cmsShopInfoVo == null) {
            return result.error500("该商户不存在");
        }
        //封装营业时间
        if (StringUtils.isNotBlank(cmsShopInfoVo.getMOpenTime())) {
            List<String[]> set = new ArrayList<>();
            String[] openStr = cmsShopInfoVo.getMOpenTime().split("::");
            for (int i = 0, open = openStr.length; i < open; i++) {
                set.add(openStr[i].split("-"));
            }
            cmsShopInfoVo.setOpenTime(set);
        }
        //获取商家设备信息
        List<CmsEqNum> cmsEqNumList = cmsEqMapper.queryEqNumByMcode(mCode);
        //获取商家昨日收益
        BigDecimal bigDecimal = cmsOrderRevenueMapper.shopYdayIncome(mCode);
        cmsShopInfoVo.setYesterdayEarnings(bigDecimal);
        //查询商户分佣收益
        List<CmsShopProjectListAppVo> listAppVos = cmsShopProjectInfoMapper.listProjectByMcode(mCode, cmsShopInfoVo.getPSubCode());
        List<PartServantIncome> incomes = null;
        if (listAppVos != null && listAppVos.size() > 0) {
            incomes = new ArrayList<>();
            PartServantIncome servantIncome = null;
            for (CmsShopProjectListAppVo listApp : listAppVos) {
                servantIncome = new PartServantIncome();
                servantIncome.setCname(listApp.getUserCName());
                servantIncome.setCommissionRatio(listApp.getProportion());
                //查询商户收益
                BigDecimal shopIncome = cmsOrderRevenueMapper.queryProjectIncome(mCode, listApp.getUserCName());
                servantIncome.setIncome(shopIncome);
                incomes.add(servantIncome);
            }
        }
        cmsShopInfoVo.setIncomes(incomes);
        cmsShopInfoVo.setEqNum(cmsEqNumList);
        //查询图片信息
        CmsFile cmsFile = new CmsFile();
        cmsFile.setUserId(mCode);
        cmsFile.setFileType(2);
        List<CmsFile> imgFile = cmsFileMapper.queryAll(cmsFile);
        cmsShopInfoVo.setImgList(imgFile);
        //查询视频
        CmsFile mp4File = cmsFileMapper.queryByCodeAndType(mCode, 0);
        cmsShopInfoVo.setMp4File(mp4File);
        result.setResult(cmsShopInfoVo);
        result.setCode(200);
        return result;
    }


    @Override
    public Result<Object> aSynShopInfo(JSONObject param) {
        if (StringUtils.isBlank(param.getString("mCode"))) {
            return Result.error("商户编号为空");
        }
        //查询商户信息
        AddShopInfoDto addShopInfoDto = new AddShopInfoDto();
        CmsShopInfo cmsShopInfo = cmsShopInfoMapper.queryByMCode(param.getString("mCode"));
        if (cmsShopInfo == null) {
            return Result.error("该商户不存在");
        }
        BeanUtils.copyProperties(cmsShopInfo, addShopInfoDto);
        CmsEqSet cmsEqSets = cmsEqSetMapper.queryByMCode(param.getString("mCode"));
        addShopInfoDto.setBUserCode(cmsShopInfo.getMCode());
        if (cmsEqSets != null) {
            addShopInfoDto.setOverallVolume(cmsEqSets.getMVolume() == null ? "" : cmsEqSets.getMVolume().toString());
            addShopInfoDto.setMuteVolume(cmsEqSets.getMMinVoume() == null ? "" : cmsEqSets.getMMinVoume().toString());
            addShopInfoDto.setMuteTime(cmsEqSets.getMDurationtime() == null ? "" : cmsEqSets.getMDurationtime().toString());
            addShopInfoDto.setOpen_close_one(cmsEqSets.getMOpentimeF() + "-" + cmsEqSets.getMClosetimeF());
            addShopInfoDto.setOpen_close_two(cmsEqSets.getMOpentimeS() + "-" + cmsEqSets.getMClosetimeS());
            addShopInfoDto.setOpen_close_three(cmsEqSets.getMOpentimeT() + "-" + cmsEqSets.getMClosetimeT());
        }
        addShopInfoDto.setMOpenTime(cmsShopInfo.getMOpenTime());
        //查询商户文件信息
        CmsFile cmsFile = new CmsFile();
        cmsFile.setUserId(cmsShopInfo.getMCode());
        List<CmsFile> cmsFiles = cmsFileMapper.queryAll(cmsFile);
        if (cmsFiles != null && cmsFiles.size() > 0) {
            List<AddCmsFile> addCmsFiles = new ArrayList<>();
            AddCmsFile addCmsFile = null;
            for (CmsFile file : cmsFiles) {
                addCmsFile = new AddCmsFile();
                BeanUtils.copyProperties(file, addCmsFile);
                addCmsFile.setFileDuration(cmsFile.getFileDuration());
                addCmsFiles.add(addCmsFile);
            }
            addShopInfoDto.setUserFile(addCmsFiles);
        }
        CmsPartnerInfo cmsPartnerInfo = cmsPartnerInfoMapper.queryCmsPartnerBySubCodeOrPCode(cmsShopInfo.getPromoterNo(), cmsShopInfo.getPSubCode());
        addShopInfoDto.setMPaymentIntegral(cmsShopInfo.getMPaymentIntegral());
        //同步信息到广告机
        if (cmsPartnerInfo != null) {
            addShopInfoDto.setPId(cmsPartnerInfo.getPId().toString());
            addShopInfoDto.setPName(cmsPartnerInfo.getPName());
            addShopInfoDto.setPPhoneNum(cmsPartnerInfo.getPPhoneNum());
            addShopInfoDto.setCreateTime(cmsPartnerInfo.getCreatetime());
        }
        CmsShopInfo cmsShopInfos = new CmsShopInfo();
        cmsShopInfos.setMCode(addShopInfoDto.getMCode());
        log.info("同步商户信息----》》{}", JSON.toJSONString(addShopInfoDto, SerializerFeature.PrettyFormat, SerializerFeature.WriteMapNullValue));
        boolean isSyn = Boolean.parseBoolean(deviceClient.updateUserInfo(JSONObject.parseObject(JSON.toJSONString(addShopInfoDto, SerializerFeature.PrettyFormat, SerializerFeature.WriteMapNullValue))));
        if (isSyn) {
            //信息同步成功
            cmsShopInfos.setSyn(1);
            cmsShopInfoMapper.updateByMcode(cmsShopInfos);
            return Result.ok("同步成功");
        }
        //同步失败
        cmsShopInfos.setSyn(-1);
        cmsShopInfoMapper.updateByMcode(cmsShopInfos);
        return Result.error("同步失败");
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<Object> updateShop(UpdateCmsShopDto updateCmsShopDto) {
        //拼接营业时间
        StringBuilder stringBuilder = new StringBuilder();
        List<String[]> list = updateCmsShopDto.getOpenTime();
        for (String[] str : list) {
            stringBuilder.append(str[0]).append("-").append(str[1]).append("::");
        }
        //更新商户信息
        CmsShopInfo cmsShopInfo = new CmsShopInfo();
        cmsShopInfo.setMCode(updateCmsShopDto.getMCode());
        cmsShopInfo.setMOpenTime(stringBuilder.toString());
        cmsShopInfo.setMPaymentIntegral(updateCmsShopDto.getMPaymentIntegral());
        cmsShopInfo.setMLevel(updateCmsShopDto.getMLevel());
        cmsShopInfoMapper.updateByMcode(cmsShopInfo);
        //更新商户设备配置
        CmsEqSet cmsShopEq = new CmsEqSet();
        BeanUtils.copyProperties(updateCmsShopDto, cmsShopEq);
        cmsEqSetMapper.updateByMcode(cmsShopEq);
        //更新视频
        CmsFile mp4File = updateCmsShopDto.getMp4File();
        mp4File.setUserId(updateCmsShopDto.getMCode());
        mp4File.setFileUrl(null);
        CmsFile cmsFile = cmsFileMapper.queryByCodeAndType(updateCmsShopDto.getMCode(), 0);
        if (cmsFile != null && !cmsFile.getId().equals(mp4File.getId())) {
            cmsFileMapper.deleteById(cmsFile.getId());
        }
        cmsFileMapper.updateById(mp4File);
        //同步信息至广告机
        AddShopInfoDto addShopInfoDto = new AddShopInfoDto();
        cmsShopInfo = cmsShopInfoMapper.queryByMCode(updateCmsShopDto.getMCode());
        BeanUtils.copyProperties(cmsShopInfo, addShopInfoDto);
        cmsFile = cmsFileMapper.queryByCodeAndType(updateCmsShopDto.getMCode(), 0);
        if (cmsFile != null) {
            AddCmsFile addCmsFile = new AddCmsFile();
            BeanUtils.copyProperties(cmsFile, addCmsFile);
            List<AddCmsFile> files = new ArrayList<>();
            addCmsFile.setFileName(cmsFile.getFileRealName());
            addCmsFile.setFileDuration(cmsFile.getFileDuration());
            files.add(addCmsFile);
            addShopInfoDto.setUserFile(files);
        }
        CmsEqSet eqSet = cmsEqSetMapper.queryByMCode(updateCmsShopDto.getMCode());
        if (eqSet != null) {
            addShopInfoDto.setBUserCode(eqSet.getMCode());
            addShopInfoDto.setOverallVolume(eqSet.getMVolume().toString());
            addShopInfoDto.setMuteVolume(eqSet.getMMinVoume() == null ? "" : eqSet.getMMinVoume().toString());
            addShopInfoDto.setMuteTime(eqSet.getMDurationtime() == null ? "" : eqSet.getMDurationtime().toString());
            addShopInfoDto.setOpen_close_one(eqSet.getMOpentimeF() + "-" + eqSet.getMClosetimeF());
            addShopInfoDto.setOpen_close_two(eqSet.getMOpentimeS() + "-" + eqSet.getMClosetimeS());
            addShopInfoDto.setOpen_close_three(eqSet.getMOpentimeT() + "-" + eqSet.getMClosetimeT());
        }
        CmsPartnerInfo cmsPartnerInfo = cmsPartnerInfoMapper.queryCmsPartnerBySubCodeOrPCode(cmsShopInfo.getPromoterNo(), cmsShopInfo.getPSubCode());
        addShopInfoDto.setMPaymentIntegral(updateCmsShopDto.getMPaymentIntegral());
        async.asynShop(cmsPartnerInfo, addShopInfoDto);
        return Result.ok("修改成功");
    }

    @Override
    public CmsShopInfo queryShopInfo(JSONObject param) {
        return cmsShopInfoMapper.queryShopInfo(param);
    }

    @Override
    public Result<Page<ShopInfoListVo>> listShop(ShopInfoDto shopInfoDto) {
        Result<Page<ShopInfoListVo>> result = new Result<>();
        Page<ShopInfoListVo> page = new Page<>(shopInfoDto.getPageNo(), shopInfoDto.getPageSize());
        List<ShopInfoListVo> list = cmsShopInfoMapper.queryPoint(page, shopInfoDto);
        page.setRecords(list);
        result.setResult(page);
        result.setCode(200);
        return result;
    }

    @Override
    public Result<ShopInfoVo> getShopInfos(JSONObject param) {
        Result<ShopInfoVo> result = new Result<>();
        ShopInfoVo shopInfoVo = cmsShopInfoMapper.queryPointInfo(param.getString("mCode"));
        CmsFile cmsFile = new CmsFile();
        cmsFile.setUserId(shopInfoVo.getMCode());
        cmsFile.setFileType(2);
        List<CmsFile> files = cmsFileMapper.queryAll(cmsFile);
        shopInfoVo.setFile(files == null ? new ArrayList<>() : files);
        result.setCode(200);
        result.setResult(shopInfoVo);
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<Object> modifyPoint(ShopInfoVo shopInfoVo) {
        //查询商户星级
        CmsStar cmsStar=cmsStarServiceImpl.getOne(new QueryWrapper<CmsStar>().eq("star_rating",shopInfoVo.getMLevel()));
        if(cmsStar == null){
            return Result.error("星级配置不存在");
        }
        CmsShopInfo cmsShopInfo = new CmsShopInfo();
        cmsShopInfo.setMCode(shopInfoVo.getMCode());
        cmsShopInfo.setMTraffic(shopInfoVo.getMTraffic());
        cmsShopInfo.setMLevel(shopInfoVo.getMLevel());
        cmsShopInfo.setMConsume(shopInfoVo.getMConsume());
        cmsShopInfo.setMPaymentIntegral(cmsStar.getLowestValue());
        cmsShopInfoMapper.updateByMcode(cmsShopInfo);
        CmsShopInfoDetai detai = cmsShopInfoDetaiMapper.queryByMcode(shopInfoVo.getMCode());
        if (detai == null) {
            detai = new CmsShopInfoDetai();
            detai.setOriginalPrice(shopInfoVo.getOriginalPrice());
            detai.setPresentPrice(shopInfoVo.getPresentPrice());
            detai.setMCode(shopInfoVo.getMCode());
            detai.setDescribe(shopInfoVo.getDescribe());
            Date date = new Date();
            detai.setCreateTime(date);
            detai.setUpdateTime(date);
            detai.setStatus(1);
            cmsShopInfoDetaiMapper.insert(detai);
        } else {
            detai.setOriginalPrice(shopInfoVo.getOriginalPrice());
            detai.setPresentPrice(shopInfoVo.getPresentPrice());
            detai.setMCode(shopInfoVo.getMCode());
            detai.setDescribe(shopInfoVo.getDescribe());
            Date date = new Date();
            detai.setCreateTime(date);
            detai.setUpdateTime(date);
            detai.setStatus(1);
            cmsShopInfoDetaiMapper.updateByMode(detai);
        }
        return Result.ok("修改成功");
    }

    @Override
    public CmsShopInfo queryShopByMcode(String merchantCode) {
        return cmsShopInfoMapper.queryByMCode(merchantCode);
    }

    @Override
    public CmsShopInfo queryShopByEqCode(String mcode) {
        CmsEq cmsEq = cmsEqMapper.getMcode(mcode);
        if (cmsEq == null) {
            return null;
        }
        return cmsShopInfoMapper.getBuserInfo(cmsEq.getMCode());
    }

    @Override
    public CmsShopInfo getShopInfoByEqCode(String eqCode) {
        CmsShopInfo cmsShopInfo = cmsShopInfoMapper.getShopInfoByEqCode(eqCode);
        if (cmsShopInfo == null) {
            return null;
        }
        CmsChargingGrade cmsChargingGrade=cmsChargingGradeMapper.selectById(1);
        cmsShopInfo.setChargingGrade(cmsChargingGrade.getChargingGrade());
        return cmsShopInfo;
    }

    @Override
    public void updateIntegralByLevel(Integer lowestValue, Integer starRating) {
        cmsShopInfoMapper.updateIntegralByLevel(lowestValue, starRating);
    }
}
