package com.jmt.modules.commissionapi.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jmt.common.api.vo.Result;
import com.jmt.modules.commission.entity.CmsEqFault;
import com.jmt.modules.commission.model.dto.AddCmsEqFaultDto;
import com.jmt.modules.commission.model.dto.CmsEqFaultAppListDto;
import com.jmt.modules.commission.model.vo.CmsEqFaultInfoAppVo;

import java.util.Map;

/**
 *  @author: hanshangrong
 *  @Date: 2020/4/23 0023 14:52
 *  @Description:类注释
 *  设备故障
 */
public interface CmsEqFaultService {

    /**
     * 描述:  设备维护列表
     * @method  listCmsEqFault
     * @date: 2020/4/23 0023
     * @author: hanshangrong
     * @param cmsEqFaultAppListDto
     * @return com.jmt.common.api.vo.Result<com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.jmt.modules.commission.entity.CmsEqFault>>
     */
    Result<Page<CmsEqFault>> listCmsEqFault(CmsEqFaultAppListDto cmsEqFaultAppListDto);


    /**
     * 描述:  获取设备维护详情
     * @method  getEqFaultInfo
     * @date: 2020/4/23 0023
     * @author: hanshangrong
     * @param map
     * @return com.jmt.common.api.vo.Result<com.jmt.modules.commission.model.vo.CmsEqFaultInfoAppVo>
     */
    Result<CmsEqFaultInfoAppVo> getEqFaultInfo(Map<String, String> map);

    /**
     * 描述:  设备维护状态变更
     * @method  stateChange
     * @date: 2020/4/23 0023
     * @author: hanshangrong
     * @param map
     * @return com.jmt.common.api.vo.Result<java.lang.Object>
     */
    Result<Object> stateChange(Map<String, String> map);

    /**
     * 描述:  B端同步设备维护信息
     * @method  newSynEqFault
     * @date: 2020/5/9 0009
     * @author: hanshangrong
     * @param addCmsEqFaultDto
     * @return com.jmt.common.api.vo.Result<java.lang.Object>
     */
    Result<Object> newSynEqFault(AddCmsEqFaultDto addCmsEqFaultDto);
}
