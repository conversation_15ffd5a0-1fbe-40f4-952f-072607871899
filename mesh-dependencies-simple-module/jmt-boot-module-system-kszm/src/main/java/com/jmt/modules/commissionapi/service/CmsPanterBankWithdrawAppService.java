package com.jmt.modules.commissionapi.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jmt.common.api.vo.Result;
import com.jmt.modules.commission.entity.CmsPartnerWithdraw;
import com.jmt.modules.commission.entity.CmsPartnerWithdrawConfigure;
import com.jmt.modules.commission.model.dto.CmsPartnerWithdrawListDto;

public interface CmsPanterBankWithdrawAppService {

    Result<Object> save(CmsPartnerWithdraw cmsPartnerWithdraw);

    Result<CmsPartnerWithdraw> queryById(Integer id);

    Result<Page<CmsPartnerWithdraw>> queryList(CmsPartnerWithdrawListDto cmsPartnerWithdrawListDto);

    Result<CmsPartnerWithdrawConfigure> getConfigure();
}
