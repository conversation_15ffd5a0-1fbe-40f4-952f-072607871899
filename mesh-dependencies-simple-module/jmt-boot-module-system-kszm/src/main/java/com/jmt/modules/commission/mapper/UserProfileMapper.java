package com.jmt.modules.commission.mapper;

import com.jmt.modules.commission.entity.UserProfile;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * (UserProfile)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-09-17 11:53:59
 */
public interface UserProfileMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    UserProfile queryById(Long id);

    /**
     * 查询指定行数据
     *
     * @param offset 查询起始位置
     * @param limit 查询条数
     * @return 对象列表
     */
    List<UserProfile> queryAllByLimit(@Param("offset") int offset, @Param("limit") int limit);


    /**
     * 通过实体作为筛选条件查询
     *
     * @param userProfile 实例对象
     * @return 对象列表
     */
    List<UserProfile> queryAll(UserProfile userProfile);

    /**
     * 新增数据
     *
     * @param userProfile 实例对象
     * @return 影响行数
     */
    int insert(UserProfile userProfile);

    /**
     * 修改数据
     *
     * @param userProfile 实例对象
     * @return 影响行数
     */
    int update(UserProfile userProfile);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Long id);

}