package com.jmt.modules.commission.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.Date;

@Data
public class EventPackageListVo {

    @JsonProperty("id")
    private Integer id;
    /**
     * 活动名
     */
    @JsonProperty("eventName")
    private String eventName;

    private String address;
    /**
     * 活动开始时间
     */
    @JsonProperty("startTime")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date startTime;
    /**
     * 活动结束时间
     */
    @JsonProperty("sendTime")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date sendTime;
    /**
     * 0 按月 1 按周 2 按天
     */
    @JsonProperty("activityType")
    private Integer activityType;
    /**
     * 折扣
     */
    @JsonProperty("discount")
    private Double discount;
    /**
     * 状态 1、活动中 2、已过期
     */
    @JsonProperty("status")
    private Integer status;

    @JsonProperty("createTime")
    private Date createTime;

}
