package com.jmt.modules.commission.entity;

import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
/**
 * 推荐奖励配置表(CmsReward)实体类
 *
 * <AUTHOR>
 * @since 2020-04-18 11:11:00
 */
@Data
public class CmsReward implements Serializable {
    private static final long serialVersionUID = -61663439708805971L;
        
    @JsonProperty("id")
    private Integer id;
    /**
    * 用户分佣级别编号
    */    
    @JsonProperty("projectid")
    private Integer projectid;
    /**
    * 名称
    */    
    @JsonProperty("name")
    private String name;
    /**
    * 奖励数量
    */    
    @JsonProperty("eqnumber")
    private Integer eqnumber;
    /**
    * 状态
    */    
    @JsonProperty("status")
    private Integer status;




}