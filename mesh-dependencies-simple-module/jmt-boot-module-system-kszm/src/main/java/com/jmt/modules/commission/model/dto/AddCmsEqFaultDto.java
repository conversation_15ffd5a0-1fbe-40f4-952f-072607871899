package com.jmt.modules.commission.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.Date;

/**
 *  @author: hanshangrong
 *  @Date: 2020/5/8 0008 18:42
 *  @Description:类注释
 *  新增设备维护
 */
public class AddCmsEqFaultDto {
    /**
     * 订单号
     */
    @JsonProperty("orderid")
    private String orderid;
    /**
     * 设备编号
     */
    @JsonProperty("eqCode")
    private String eqCode;
    /**
     * 所属商户编号
     */
    @JsonProperty("mCode")
    private String mCode;
    /**
     * 故障信息
     */
    @JsonProperty("faultinfo")
    private String faultinfo;
    /**
     * 维护类型
     */
    @JsonProperty("maintenancetype")
    private Integer maintenancetype;
    /**
     * 损坏部件
     */
    @JsonProperty("damage")
    private String damage;
    /**
     * 图片
     */
    @JsonIgnore
    private String img;
    /**
     * 图片数组
     */
    private String[] imgArray;
    /**
     * 创建时间
     */
    @JsonProperty("creationtime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date creationtime;

    public String getOrderid() {
        return orderid;
    }

    public void setOrderid(String orderid) {
        this.orderid = orderid;
    }

    public String getEqCode() {
        return eqCode;
    }

    public void setEqCode(String eqCode) {
        this.eqCode = eqCode;
    }

    public String getmCode() {
        return mCode;
    }

    public void setmCode(String mCode) {
        this.mCode = mCode;
    }

    public String getFaultinfo() {
        return faultinfo;
    }

    public void setFaultinfo(String faultinfo) {
        this.faultinfo = faultinfo;
    }

    public Integer getMaintenancetype() {
        return maintenancetype;
    }

    public void setMaintenancetype(Integer maintenancetype) {
        this.maintenancetype = maintenancetype;
    }

    public String getDamage() {
        return damage;
    }

    public void setDamage(String damage) {
        this.damage = damage;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public String[] getImgArray() {
        return imgArray;
    }

    public void setImgArray(String[] imgArray) {
        StringBuilder stringBuilder=new StringBuilder();
        if(imgArray.length>0){
            for (String url:imgArray) {
                stringBuilder.append(url+"::");
            }
            this.img=stringBuilder.toString();
        }
        this.imgArray = imgArray;
    }

    public Date getCreationtime() {
        return creationtime;
    }

    public void setCreationtime(Date creationtime) {
        this.creationtime = creationtime;
    }
}
