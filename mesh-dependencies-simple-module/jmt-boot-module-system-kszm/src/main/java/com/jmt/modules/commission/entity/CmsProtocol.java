package com.jmt.modules.commission.entity;

import java.util.Date;
import java.io.Serializable;

/**
 * (CmsProtocol)实体类
 *
 * <AUTHOR>
 * @since 2020-09-21 14:23:35
 */
public class CmsProtocol implements Serializable {
    private static final long serialVersionUID = -94245178982408244L;
    
    private Integer id;
    /**
    * 协议名称
    */
    private String protocolName;
    /**
    * 协议内容
    */
    private String protocolContent;
    /**
    * 图标url
    */
    private String protocolIconUrl;
    /**
    * 发布时间
    */
    private Date protpcolCreateTime;
    
    private Date protpcolUpdateTime;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getProtocolName() {
        return protocolName;
    }

    public void setProtocolName(String protocolName) {
        this.protocolName = protocolName;
    }

    public String getProtocolContent() {
        return protocolContent;
    }

    public void setProtocolContent(String protocolContent) {
        this.protocolContent = protocolContent;
    }

    public String getProtocolIconUrl() {
        return protocolIconUrl;
    }

    public void setProtocolIconUrl(String protocolIconUrl) {
        this.protocolIconUrl = protocolIconUrl;
    }

    public Date getProtpcolCreateTime() {
        return protpcolCreateTime;
    }

    public void setProtpcolCreateTime(Date protpcolCreateTime) {
        this.protpcolCreateTime = protpcolCreateTime;
    }

    public Date getProtpcolUpdateTime() {
        return protpcolUpdateTime;
    }

    public void setProtpcolUpdateTime(Date protpcolUpdateTime) {
        this.protpcolUpdateTime = protpcolUpdateTime;
    }

}