package com.jmt.modules.commission.mapper;
import org.apache.ibatis.annotations.Param;
import com.jmt.modules.commission.entity.NideshopChargeOrder;

import java.util.List;

/**
 * 充电订单表(NideshopChargeOrder)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-05-13 16:00:03
 */
public interface NideshopChargeOrderMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    NideshopChargeOrder queryById(Integer id);


    /**
     * 通过实体作为筛选条件查询
     *
     * @param nideshopChargeOrder 实例对象
     * @return 对象列表
     */
    List<NideshopChargeOrder> queryAll(NideshopChargeOrder nideshopChargeOrder);

    /**
     * 新增数据
     *
     * @param nideshopChargeOrder 实例对象
     * @return 影响行数
     */
    int insert(NideshopChargeOrder nideshopChargeOrder);

    /**
     * 修改数据
     *
     * @param nideshopChargeOrder 实例对象
     * @return 影响行数
     */
    int update(NideshopChargeOrder nideshopChargeOrder);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Integer id);

    /**
     * 描述:  查询未计算收益的充电订单
     * @method  queryThatDay
     * @date: 2020/5/13 0013
     * @author: hanshangrong
     * @param
     * @return java.util.List<com.jmt.modules.commission.entity.NideshopChargeOrder>
     */
    List<NideshopChargeOrder> queryThatDay();
}