package com.ihomeui.mesh.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public class Md5CryptoUtil {

    private static final Logger log = LoggerFactory.getLogger(Md5CryptoUtil.class);

    public static String MD5(String s) {
        return MD5(s,"utf-8");
    }

    public static String MD5(String data, String charset) {
        try {
            MessageDigest digest = MessageDigest.getInstance("md5");
            byte[] result = digest.digest(data.getBytes(charset));
            StringBuilder buffer = new StringBuilder();
            for (byte b : result) {
                int number = b & 0xFF;
                String str = Integer.toHexString(number);
                if (str.length() == 1) {
                    buffer.append("0");
                }
                buffer.append(str);
            }
            return buffer.toString();
        } catch (Exception e) {
            log.debug("加密算法异常："+e.getMessage(), e);
        }
        return "";
    }

    public static String fileMD5(byte[] bytes) throws NoSuchAlgorithmException {
        MessageDigest messageDigest = MessageDigest.getInstance("md5");
        messageDigest.update(bytes);
        byte[] digest = messageDigest.digest();
        StringBuilder buffer = new StringBuilder();
        for (byte b : digest) {
            int a = b & 0xff;
            String hex = Integer.toHexString(a);
            if (hex.length() == 1) {
                hex = 0 + hex;
            }
            buffer.append(hex);
        }
        return buffer.toString();
    }
}
