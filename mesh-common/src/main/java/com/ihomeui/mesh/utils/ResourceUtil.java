package com.ihomeui.mesh.utils;

import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;


public class ResourceUtil {
    private static final Logger log = LoggerFactory.getLogger(ResourceUtil.class);

    public static String resourceToString(String resourceName) {
        return resourceToString(resourceName, StandardCharsets.UTF_8);
    }

    public static String resourceToString(String resourceName, Charset charset){
        try {
            InputStream is = ResourceUtil.class.getClassLoader().getResourceAsStream(resourceName);
            return IOUtils.toString(is, charset);
        }catch(Exception ex){
            log.error("加载资源文件失败：resourceName={}, error={}", resourceName, ex.getMessage());
        }
        return null;
    }
}
