package com.ihomeui.mesh.utils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class ArrayUtils extends org.apache.commons.lang3.ArrayUtils {
    public static boolean isItemsEqual(Object[] arr1, Object[] arr2){
        if(arr1 == arr2) return true;

        return arr1!=null && getEqualsItems(arr1,arr2).size() == arr1.length;
    }


    public static <T> List<T> getEqualsItems(T[] arr1, T[] arr2){
        if(arr1 == arr2) return arr1 == null ? new ArrayList<>() : Arrays.asList(arr1);

        List<T> list1 = arr1==null?new ArrayList<>():new ArrayList<>(Arrays.asList(arr1));
        List<T> list2 = arr2==null?new ArrayList<>():new ArrayList<>(Arrays.asList(arr2));
        List<T> result = new ArrayList<>();
        T item;
        for (int i1 = list1.size() - 1; i1 >= 0; i1--) {
            item = list1.get(i1);
            for (int i2 = list2.size() - 1; i2 >= 0; i2--) {
                if(item==list2.get(i2) || (item!=null && item.equals(list2.get(i2)))){
                    list1.remove(i1);
                    list2.remove(i2);
                    if(item!=null) result.add(item);
                    break;
                }
            }
        }
        return result;
    }
}
