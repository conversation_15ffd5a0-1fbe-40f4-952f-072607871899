<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.4.xsd">
    <!--
        Added the constraints for entity User.
    -->
    <changeSet id="20161229004502-2" author="jhipster">

        <addForeignKeyConstraint baseColumnNames="user_id"
                                 baseTableName="uaa_user_authority"
                                 constraintName="fk_user_authority_user_id"
                                 referencedColumnNames="id"
                                 referencedTableName="uaa_user"
                                 onDelete="CASCADE"/>
        <addForeignKeyConstraint baseColumnNames="authority_id"
                                 baseTableName="uaa_user_authority"
                                 constraintName="fk_user_authority_authority_id"
                                 referencedColumnNames="id"
                                 referencedTableName="uaa_authority"
                                 onDelete="CASCADE"/>

        <addForeignKeyConstraint baseColumnNames="user_id"
                                 baseTableName="uaa_user_office"
                                 constraintName="fk_user_office_user_id"
                                 referencedColumnNames="id"
                                 referencedTableName="uaa_user"
                                 onDelete="CASCADE"/>
        <addForeignKeyConstraint baseColumnNames="office_id"
                                 baseTableName="uaa_user_office"
                                 constraintName="fk_user_office_office_id"
                                 referencedColumnNames="id"
                                 referencedTableName="uaa_office"
                                 onDelete="CASCADE"/>

        <addForeignKeyConstraint baseColumnNames="user_id"
                                 baseTableName="uaa_user_group"
                                 constraintName="fk_user_group_user_id"
                                 referencedColumnNames="id"
                                 referencedTableName="uaa_user"
                                 onDelete="CASCADE"/>
        <addForeignKeyConstraint baseColumnNames="group_id"
                                 baseTableName="uaa_user_group"
                                 constraintName="fk_user_group_group_id"
                                 referencedColumnNames="id"
                                 referencedTableName="uaa_group"
                                 onDelete="CASCADE"/>

    </changeSet>
</databaseChangeLog>
