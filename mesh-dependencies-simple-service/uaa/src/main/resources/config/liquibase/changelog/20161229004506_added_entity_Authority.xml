<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.4.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <property name="now" value="now()" dbms="mysql,h2"/>
    <property name="now" value="current_timestamp" dbms="postgresql"/>
    <property name="now" value="sysdate" dbms="oracle"/>
    <property name="now" value="GETDATE()" dbms="mssql"/>

    <property name="autoIncrement" value="true" dbms="mysql,h2,postgresql,oracle,mssql"/>

    <property name="floatType" value="float4" dbms="postgresql, h2"/>
    <property name="floatType" value="float" dbms="mysql, oracle, mssql"/>

    <!--
        Added the entity Authority.
    -->
    <changeSet id="20161229004506" author="jhipster">

        <createTable tableName="uaa_authority">
            <column name="id" type="bigint" autoIncrement="${autoIncrement}">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="name" type="varchar(64)">
                <constraints unique="true" nullable="false" />
            </column>
            <column name="label" type="varchar(64)"/>
            <column name="remarks" type="varchar(256)"/>

            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here, do not remove-->
        </createTable>

        <createIndex indexName="idx_uaa_authority_name"
                     tableName="uaa_authority"
                     unique="true">
            <column name="name" type="varchar(64)"/>
        </createIndex>

    </changeSet>

    <changeSet id="201801151455072" author="caixy">
        <addColumn tableName="uaa_authority">
            <column name="relation_type" type="varchar(50)"/>
            <column name="relation_type_id" type="bigint"/>
        </addColumn>
    </changeSet>

    <changeSet id="201805291114671" author="caixy">
        <dropColumn tableName="uaa_authority" columnName="relation_type"/>
        <dropColumn tableName="uaa_authority" columnName="relation_type_id"/>
    </changeSet>

    <changeSet id="201806041131221" author="caixy">
        <addColumn tableName="uaa_authority">
            <column name="inherit_id" type="bigint" />
        </addColumn>
    </changeSet>

    <changeSet id="201806131136333-1" author="caixy">
        <preConditions onError="MARK_RAN" onFail="MARK_RAN">
            <not><tableExists tableName="uaa_authority_inherit"/></not>
        </preConditions>
        <createTable tableName="uaa_authority_inherit">
            <column name="inherited_id" type="bigint">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="authority_id" type="bigint">
                <constraints primaryKey="true" nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="2018073115520001-8" author="xxm">
        <addColumn tableName="uaa_authority">
            <column name="kind" type="varchar(50)" />
        </addColumn>
    </changeSet>

    <changeSet id="2018080718030001-9" author="xxm">
        <modifyDataType tableName="uaa_authority" columnName="label" newDataType="varchar(256)"/>
    </changeSet>

</databaseChangeLog>
