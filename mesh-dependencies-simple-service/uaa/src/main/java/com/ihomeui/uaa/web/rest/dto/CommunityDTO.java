package com.ihomeui.uaa.web.rest.dto;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2018/5/28
 */
public class CommunityDTO {

    private Long id;

    private Long communityGroupId;

    private String communityGroupName;

    private String code;

    private String name;

    private String fullName;

    private String phone;

    private String contacts;

    private String area;

    private String address;

    private String zipCode;

    private String remarks;

    private String longitude;

    private String latitude;

    private BranchRegionDTO branchRegion;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCommunityGroupId() {
        return communityGroupId;
    }

    public void setCommunityGroupId(Long communityGroupId) {
        this.communityGroupId = communityGroupId;
    }

    public String getCommunityGroupName() {
        return communityGroupName;
    }

    public void setCommunityGroupName(String communityGroupName) {
        this.communityGroupName = communityGroupName;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRemarks() {
        return remarks;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getContacts() {
        return contacts;
    }

    public void setContacts(String contacts) {
        this.contacts = contacts;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public BranchRegionDTO getBranchRegion() {
        return branchRegion;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getZipCode() {
        return zipCode;
    }

    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public void setBranchRegion(BranchRegionDTO branchRegion) {
        this.branchRegion = branchRegion;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CommunityDTO that = (CommunityDTO) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {

        return Objects.hash(id);
    }
}
