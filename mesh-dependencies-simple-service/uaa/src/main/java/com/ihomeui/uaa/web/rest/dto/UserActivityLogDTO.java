package com.ihomeui.uaa.web.rest.dto;

import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.Objects;


/**
 * A DTO for the UserActivityLog entity.
 */
public class UserActivityLogDTO implements Serializable {

    private Long id;

    @ApiModelProperty(value = "用户Id")
    private Long userId;

    @ApiModelProperty(value = "社区物业集团ID")
    private Long communityGroupId;

    @ApiModelProperty(value = "社区ID")
    private Long communityId;

    @ApiModelProperty(value = "应用名称：MOBILE_APP_CUSTOMER、MOBILE_APP_EMPLOYEE")
    private String appName;

    @ApiModelProperty(value = "活动名称：APP_LOGIN(APP登录)、APP_WAKE_UP(APP启动)、APP_SWITCH_COMMUNITY(APP切换房产或社区)", required = true)
    private String activityName;

    @ApiModelProperty(value = "日志描述")
    private String description;

    @ApiModelProperty(value = "时间", required = true)
    private LocalDateTime atTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }
    public Long getCommunityGroupId() {
        return communityGroupId;
    }

    public void setCommunityGroupId(Long communityGroupId) {
        this.communityGroupId = communityGroupId;
    }
    public Long getCommunityId() {
        return communityId;
    }

    public void setCommunityId(Long communityId) {
        this.communityId = communityId;
    }
    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }
    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
    public LocalDateTime getAtTime() {
        return atTime;
    }

    public void setAtTime(LocalDateTime atTime) {
        this.atTime = atTime;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        UserActivityLogDTO userActivityLogDTO = (UserActivityLogDTO) o;

        if ( ! Objects.equals(id, userActivityLogDTO.id)) {
            return false;
        }

        return true;
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(id);
    }

    @Override
    public String toString() {
        return "UserActivityLogVM{" +
            "id=" + id +
            ", userId='" + userId + "'" +
            ", communityGroupId='" + communityGroupId + "'" +
            ", communityId='" + communityId + "'" +
            ", appName='" + appName + "'" +
            ", activityName='" + activityName + "'" +
            ", description='" + description + "'" +
            ", atTime='" + atTime + "'" +
            '}';
    }
}
