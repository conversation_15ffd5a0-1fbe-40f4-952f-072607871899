package com.ihomeui.uaa.domain.util;

import com.ihomeui.mesh.exception.CheckFailedException;
import com.ihomeui.uaa.web.rest.vm.StatisticsQuery;

/**
 * @Description:
 * @Author: LJ
 * @Date: 2019/7/23 10:32
 * @version:
 */
public class StatisticsQueryUtil {
    /**
     * 判断时间必填
     *
     * @param statisticsQuery
     * @return
     */
    public static void checkTimeNotNull(StatisticsQuery statisticsQuery) {
        if (ToolUtil.anyNull(statisticsQuery.getDateType(), statisticsQuery.getStartTime(), statisticsQuery.getEndTime())) {
            throw new CheckFailedException("请选择查询时间");
        }
    }
}
