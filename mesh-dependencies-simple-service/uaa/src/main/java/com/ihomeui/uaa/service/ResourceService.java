package com.ihomeui.uaa.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ihomeui.mesh.criteria.CriteriaParser;
import com.ihomeui.mesh.utils.ResourceUtil;
import com.ihomeui.uaa.repository.AuthorityRepository;
import com.ihomeui.uaa.repository.ResourceRepository;
import com.ihomeui.uaa.service.generatedef.GeneratePattern;
import com.ihomeui.uaa.service.generatedef.ResourceDef;
import com.ihomeui.uaa.web.rest.dto.ResourceImport;
import com.ihomeui.uaa.domain.Authority;
import com.ihomeui.uaa.domain.Resource;
import com.ihomeui.uaa.domain.constants.ResourceType;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Service Implementation for managing Resource.
 */
@Service
@Transactional
public class ResourceService {

    private final Logger log = LoggerFactory.getLogger(ResourceService.class);

    private final ResourceRepository resourceRepository;
    private final AuthorityRepository authorityRepository;
    private final AuthorityService authorityService;
    private ResourceDef resourceDef;

    public ResourceService(ResourceRepository resourceRepository,
                           AuthorityRepository authorityRepository,
                           AuthorityService authorityService){
        this.resourceRepository = resourceRepository;
        this.authorityRepository = authorityRepository;
        this.authorityService = authorityService;


        resourceDef = JSONObject.toJavaObject(
            JSON.parseObject(ResourceUtil.resourceToString("resource-node-def.json")),
            ResourceDef.class
        );
        resourceDef.dataToMap();
    }

    public ResourceDef getResourceDef() {
        return resourceDef;
    }

    /**
     * 新增修改资源
     * @param resource the entity to save
     * @return the persisted entity
     */
    @Transactional
    public Resource save(Resource resource) {
        log.debug("Request to save Resource : {}", resource);
        String oldPathName = null;
        if ( resource.getId() != null ) {
            oldPathName = resourceRepository.findById(resource.getId()).orElse(null).getPathName();
        }
        resource.setPathName(resource.getPathName().toLowerCase());
        if (resource.getParent()!=null && resource.getParent().getName()==null) {
            resource.setParent(findOne(resource.getParent().getId()));
        }
        generateName(resource);
        Resource result = resourceRepository.save(resource);
        if(result.getParent() != null) {
            result.getParent().addChildren(result);
        }
        //添加子级如果有
        List<ResourceDef.DefItem> childrenDef = resourceDef.DATA.get(
            resource.getResourceType()
        );
        for (ResourceDef.DefItem resourceDefItem : childrenDef) {
            saveByDefItem(resourceDefItem,result);
        }
        if ( oldPathName != null && ( ! oldPathName.equals( result.getPathName() ) )){
            refreshNode(result);
        }
        return result;
    }

    /**
     * 更新name
     * 和pathName
     * @param resource
     */
    private void generateName(Resource resource) {
        //子级可添加类型验证
        List<ResourceDef.DefItem> parentDef = resourceDef.DATA.get(
            resource.getParent()==null?
                ResourceType.ROOT:resource.getParent().getResourceType()
        );
        ResourceDef.DefItem thisdef = null;
        for (ResourceDef.DefItem resourceDefItem : parentDef) {
            if (resourceDefItem.getRef().equals(resource.getResourceType())){
                thisdef = resourceDefItem;
            }
        }
        if (thisdef==null){
            //错误子级
            throw new RuntimeException("不支持的子级"+resource.getResourceType());
        }else {
            if ( thisdef.getIsSingleton() && resource.getId() == null ){
                Resource find = resourceRepository.findOneByResourceTypeAndParentId(
                    thisdef.getRef(),
                    resource.getParent()==null? null :
                        resource.getParent().getId()
                );
                if ( find != null ) {
                    throw new RuntimeException(String.format(
                        "resource.type=%s 只能创建一个",
                        resource.getResourceType()
                    ));
                }
            }
        }
        //添加完整路径名
        if (resource.getParent()!=null){
            resource.setFullPath(
                resource.getParent().getFullPath()+"/"+resource.getPathName()
            );
        }else {
            resource.setFullPath(resource.getPathName());
        }
        //生成name
        String nameGeneratePattern = thisdef.getNameGeneratePattern();
        if (nameGeneratePattern==null) {
            resource.setName(
                resource.getParent().getName()+"."+resource.getPathName()
            );
        } else {
            resource.setName(paseNameGeneratePattern(nameGeneratePattern,resource));
        }
    }

    /**
     * 根据模板定义自动创建默认子级
     *
     * @param resourceDefItem
     * @param parent
     */
    private void saveByDefItem(ResourceDef.DefItem resourceDefItem, Resource parent) {
        if (resourceDefItem.getIsSingleton()){
            Resource resource = resourceRepository.findOneByResourceTypeAndParentId(resourceDefItem.getRef(),parent.getId());
            if (resource!=null) {
                return;
            }
        }
        if (resourceDefItem.getAutoCreate()){
            Resource resource = new Resource();
            resource.setParent(parent);
            resource.setPathName(resourceDefItem.getSingletonPathName());
            resource.setResourceType(resourceDefItem.getRef());
            resource.setRemarks(resourceDefItem.getSingletonCaption());
            resource.setLabel(resourceDefItem.getSingletonCaption());
            resource.setSeq(10L);
            resource.visible(false);
            save(resource);
        }
    }

    /**
     * 根据表达式明白规则生成命名
     *
     * @param nameGeneratePattern
     * @param resource
     * @return
     */
    private String paseNameGeneratePattern(String nameGeneratePattern, Resource resource) {
        Map<String,String> data = new HashMap<>();
        Resource application = findApplication(resource);
        data.put("APPLICATION.NAME",application==null ? "": application.getName());
        data.put("SELF.PATH_NAME",resource.getPathName());
        GeneratePattern namePattern = new GeneratePattern(nameGeneratePattern,data);
        return namePattern.generate();
    }

    /**
     * 找到资源的application节点
     * @param resource
     * @return
     */
    private Resource findApplication(Resource resource){
        if (resource==null) {
            return null;
        }
        if (ResourceType.APPLICATION.equals(resource.getResourceType())){
            return resource;
        }
        return findApplication(resource.getParent());
    }

    /**
     * 保存带权限的资源
     * @param resource
     * @param authorities
     * @return
     */
    @Transactional
    public Resource save(Resource resource, Set<String> authorities) {
        log.debug("Request to save Resource : {}", resource);
        if( authorities != null ) {
            Set<Authority> authoritySet = resource.getAuthorities();
            authoritySet.clear();
            authorities.forEach(
                authority -> authoritySet.add(authorityRepository.findOneByName(authority))
            );
        } else {
            resourceRepository.findById(resource.getId()).ifPresent(old ->
                resource.setAuthorities(old.getAuthorities())
            );
        }
        return save(resource);
    }

    /**
     *  Get all the resources.
     */
    @Transactional(readOnly = true)
    public Page<Resource> findAll(Pageable pageable) {
        log.debug("Request to get all Resources");
        return resourceRepository.findAll(pageable);
    }

    @Transactional(readOnly = true)
    public List<Resource> findAll(Specification<Resource> specification) {
        log.debug("Request to get all Resources");
        return resourceRepository.findAll(specification);
    }

    @Transactional(readOnly = true)
    public Page<Resource> findAll(CriteriaParser filter, Specification<Resource> specification, Pageable pageable) {
        log.debug("Request to get all Offices by filter: {}", filter);
        return resourceRepository.findAll((root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            if (filter!=null){
                Predicate predicate = filter.toPredicate(root, query, cb);
                if(predicate != null) {
                    predicates.add(predicate);
                }
            }
            if (specification!=null){
                Predicate predicate = specification.toPredicate(root, query, cb);
                if(predicate != null) {
                    predicates.add(predicate);
                }
            }
            return cb.and(predicates.toArray(new Predicate[0]));
        },pageable);
    }

    @Transactional(readOnly = true)
    public Page<Resource> findAll(Specification<Resource> specification, Pageable pageable) {
        return resourceRepository.findAll(specification , pageable);
    }

    /**
     * 查询
     * @param filter
     * @param pageable
     * @param parentId
     * @param resourceType
     * @return
     */
    @Transactional(readOnly = true)
    public Page<Resource> findAll(CriteriaParser filter, Pageable pageable, Long parentId, String[] resourceType, String parentFullPath) {
        log.debug("Request to get all Offices by filter: {}", filter);
        return resourceRepository.findAll(
            (Root<Resource> root, CriteriaQuery<?> query, CriteriaBuilder cb) -> {

                List<Predicate> predicates = new ArrayList<>();
                Predicate p1 = filter.toPredicate(root, query, cb);
                if(p1 != null) {
                    predicates.add(p1);
                }

                if (parentId != null){
                    if(parentId > 0) {
                        predicates.add(cb.equal(root.get("parent"), parentId));
                    } else // -1 读取根节点
                    {
                        predicates.add(cb.isNull(root.get("parent")));
                    }
                }

                if (parentFullPath!=null){
                    predicates.add(
                        cb.like(root.get("fullPath"),parentFullPath+"%")
                    );
                }

                if (resourceType !=null){
                    predicates.add(root.get("resourceType").in(resourceType));
                }

                return cb.and(predicates.toArray(new Predicate[predicates.size()]));
            },
            pageable
        );
    }

    /**
     * 查询资源树
     * @param rootResourceNames
     * @param resourceTypes
     * @return
     */
    @Transactional(readOnly = true)
    public List<ResourceTreeNode> getResourceTreeList(String rootResourceNames, String resourceTypes) {
        List<ResourceTreeNode> list = new ArrayList<>();
        ResourceTreeNode treeNode;

        List<String> types = null;
        if(StringUtils.isNotBlank(resourceTypes)) {
            types = Arrays.asList(StringUtils.split(resourceTypes.toUpperCase(), ","));
        }

        if(StringUtils.isBlank(rootResourceNames)){
            for(Resource resource : resourceRepository.findAllByParentIdIsNull()) {
                if (types != null && !types.contains(resource.getResourceType())) {
                    continue;
                }

                treeNode = getResourceTree(resource, resourceTypes);
                if (treeNode != null) {
                    list.add(treeNode);
                }
            }
        }else {
            for (String name : StringUtils.split(rootResourceNames, ",")) {
                treeNode = getResourceTree(name, resourceTypes);
                if (treeNode != null) {
                    list.add(treeNode);
                }
            }
        }

        return list;
    }

    /**
     * 获取资源树
     * @param rootResourceName 资源根节点名，为null时，为根节点
     * @param resourceTypes 指定资源类型，为null时，为查找所有类型
     * @return
     */
    @Transactional(readOnly = true)
    public ResourceTreeNode getResourceTree(String rootResourceName, String resourceTypes) {
        if(StringUtils.isBlank(rootResourceName)){
            return getResourceTreeRoot(resourceTypes);
        } else {
            Resource root = resourceRepository.findOneByNameWithEagerRelationships(rootResourceName);
            return (root == null)?null:getResourceTree(root, resourceTypes);
        }
    }

    @Transactional(readOnly = true)
    public ResourceTreeNode getResourceTree(Long rootResourceId, String resourceTypes) {
        if(rootResourceId == null||rootResourceId.equals(-1L)||rootResourceId.equals(0L)){
            return getResourceTreeRoot(resourceTypes);
        }else {
            Resource root = resourceRepository.findOneByIdWithEagerRelationships(rootResourceId);
            return (root == null)?null:getResourceTree(root, resourceTypes);
        }
    }

    private ResourceTreeNode getResourceTreeRoot(String resourceTypes){
        ResourceTreeNode root = (new ResourceTreeNode()).id(0L).name("root").resourceType("ROOT").visible(false);
        root.setChildren(new HashSet<>(getResourceTreeList(null, resourceTypes)));
        return root;
    }

    private ResourceTreeNode getResourceTree(Resource root, String resourceTypes) {
        Set<Long> ids = new HashSet<>();

        ResourceTreeNode treeRoot = resourceToResourceTreeNode(root);

        ids.add(root.getId());

        List<String> types = null;
        if(StringUtils.isNotBlank(resourceTypes)) {
            types = Arrays.asList(StringUtils.split(resourceTypes.toUpperCase(), ","));
        }

        addResourceTreeNodeChildren(treeRoot, root, types, ids);
        return treeRoot;
    }

    private void addResourceTreeNodeChildren(ResourceTreeNode parentTreeNode, Resource parentResource, List<String> types, Set<Long> addedIds){
        ResourceTreeNode childNode;
        for (Resource child : parentResource.getChildren()) {

            if(types != null && !types.contains(child.getResourceType())) {
                continue;
            }

            if(addedIds.contains(child.getId())) {
                continue;
            }
            addedIds.add(child.getId());

            childNode = resourceToResourceTreeNode(child);
            parentTreeNode.addChildren(childNode);

            addResourceTreeNodeChildren(childNode, child, types, addedIds);
        }
    }

    /**
     * 转换资源为资源树节点（不包含子节点）
     * @param resource
     * @return
     */
    @Transactional(readOnly = true)
    public ResourceTreeNode resourceToResourceTreeNode(Resource resource){
        ResourceTreeNode treeNode = new ResourceTreeNode();
        BeanUtils.copyProperties(resource, treeNode, "children","authorities");

        treeNode.setAuthorities(resource.getAuthorities().stream().map(Authority::getName)
            .collect(Collectors.toSet()));
        String resourceName = resource.getName();
        if (ResourceType.NAV.equals(resource.getResourceType())){
            Resource app = getApplication(resource);
            if ( app != null && resourceName.length() > app.getName().length() + 1 ) {
                treeNode.setRelatedModule(resourceName.substring(app.getName().length()+1));
            }
        } else {
            treeNode.setRelatedModule(resourceName);
        }
        if(resource.getParent() != null) {
            treeNode.setParentId(resource.getParent().getId());
        }
        return treeNode;
    }

    private Resource getApplication(Resource resource) {
        if ( resource == null ) {
            return null;
        }
        if (resource.getResourceType().equals(ResourceType.APPLICATION)){
            return resource;
        }else {
            return getApplication(resource.getParent());
        }
    }

    private Resource findNavBarByNav(Resource resource) {
        if (resource.getResourceType().equals(ResourceType.NAVBAR)){
            return resource;
        }
        return findNavBarByNav(resource.getParent());
    }

    /**
     *  Get one resource by id.
     *
     *  @param id the id of the entity
     *  @return the entity
     */
    @Transactional(readOnly = true)
    public Resource findOne(Long id) {
        log.debug("Request to get Resource : {}", id);
        return resourceRepository.findOneWithEagerRelationships(id);
    }

    @Transactional(readOnly = true)
    public Resource findOneByName(String name) {
        log.debug("Request to get Resource : name = {}", name);
        return resourceRepository.findOneByNameWithEagerRelationships(name);
    }

    public List<Resource> findAllApplication() {
        return resourceRepository.findAllByResourceType(ResourceType.APPLICATION);
    }

    public ResourceTreeNode getAppModuleTree() {
        ResourceTreeNode root = new ResourceTreeNode();
        List<Resource> appSet = resourceRepository.findAllByParentIdIsNull();
        for (Resource resource : appSet) {
            getResourceTree(resource,"APPLICATION,APPLICATION_SET");
        }
        return root;
    }

    /**
     *  Delete the  resource by id.
     *  @param id the id of the entity
     */
    @Transactional
    public void delete(Long id) {
        log.debug("Request to delete Resource : {}", id);
        resourceRepository.deleteById(id);
    }

    public boolean isAncestorOf(Resource ancestor, Resource descendant){
        Long ancestorId = ancestor.getId();
        Set<Long> stepIds = new HashSet<>();
        Resource parent = descendant;
        while (parent != null) {
            if (stepIds.contains(parent.getId())) {
                break; // 避免因数据错误造成的死循环
            }
            stepIds.add(parent.getId());

            if(parent.getId().equals(ancestorId)) {
                return true;
            }
            parent = parent.getParent();
        }
        return false;
    }

    public void refreshNode(Long id) {
        refreshNode(resourceRepository.findById(id).orElse(null));
    }

    public void refreshNode(Resource resource) {
        generateName(resource);
        resource = resourceRepository.save(resource);
        for (Resource child : resource.getChildren()) {
            refreshNode(child);
        }
    }

    public void move(Long one, Long toOne) {
        Resource resource = findOne(one);
        Resource toResource = findOne(toOne);

        resource.getParent().removeChildren(resource);
        resource.setParent(toResource);
    }

    public void importResource(String parentName, ResourceImport resourceImport) {
        Resource resource = toResource(resourceImport);
        saveWithChildren(resourceRepository.findOneByName(parentName),resource.getChildren());
    }

    private Resource toResource(ResourceImport resourceImport) {
        Resource resource = new Resource();
        BeanUtils.copyProperties(resourceImport,resource,"children,id");
        resource.setId(null);
        resource.setChildren(new HashSet<>());
        for (ResourceImport child : resourceImport.getChildren()) {
            resource.getChildren().add(toResource(child));
        }
        return resource;
    }

    public void saveWithChildren(Resource parent, Set<Resource> children) {
        parent.addAuthority(authorityRepository.findOneByName("ROLE_ADMIN"));
        parent = resourceRepository.save(parent);
        for (Resource child : children) {
            child.setParent(parent);
            saveWithChildren(child, child.getChildren());
        }
    }

    public ResourceImport exportResource(String name) {
        Resource resource = resourceRepository.findOneByName(name);
        return toImport(resource);
    }

    private ResourceImport toImport(Resource resource) {
        ResourceImport resourceImport = new ResourceImport();
        BeanUtils.copyProperties(resource,resourceImport,"children");
        resourceImport.setChildren(new HashSet<>());
        for (Resource child : resource.getChildren()) {
            resourceImport.getChildren().add(toImport(child));
        }
        return resourceImport;
    }

    public void copyResource(Long one, Long toOne) {
        Resource resource = findOne(one);
        Resource toResource = findOne(toOne);
        deep(resource);

        Resource clone = resource.clone();
        clone.setId(null);
        clone.setParent(toResource);
        clone.setPathName(clone.getPathName()+"_copy");
        clone.setAuthorities(new HashSet<>());
        clone.setChildren(new HashSet<>());
        for (Authority authority : resource.getAuthorities()) {
            clone.addAuthority(authority);
        }

        saveClone(resource.getChildren(),save(clone));
    }

    private void saveClone(Set<Resource> children, Resource parent) {
        for (Resource child : children) {
            Resource clone = child.clone();
            clone.setId(null);
            clone.setParent(parent);
            clone.setPathName(clone.getPathName()+"_copy");
            clone.setAuthorities(new HashSet<>());
            clone.setChildren(new HashSet<>());
            for (Authority authority : child.getAuthorities()) {
                clone.addAuthority(authority);
            }
            saveClone(child.getChildren(),save(clone));
        }
    }

    private void deep(Resource resource) {
        for (Resource children : resource.getChildren()) {
            deep(children);
        }
    }

    public void copyResourceByName(String one, String toOne) {
        Resource resource = findOneByName(one);
        Resource toResource = findOneByName(toOne);
        deep(resource);
        saveClone(resource.getChildren(),toResource);
    }

    /**
     * 获取集团开通的应用树
     * @param appName
     * @param communityGroupId
     * @return
     */
    @Cacheable(value="ResourceTreeNode", key = "'byCommunityGroupId:'+#communityGroupId+':AndAppName'+#appName")
    public ResourceTreeNode getResourceTreeByCommunityGroup(String appName, Long communityGroupId) {
        ResourceTreeNode resourceTreeNode = getResourceTree(appName,null);
        if ( communityGroupId != null ) {
            Authority authority = authorityService.getOfficeOwnerRole(communityGroupId,false);
            if (authority!=null){
                Set<Resource> resources = new HashSet<>();
                Set<Authority> authorities = new HashSet<>();
                putResource(authorities,authority,resources);
                checkResourceTreeNode(resourceTreeNode.getChildren(),resources);
            }
        }
        return resourceTreeNode;
    }

    private void putResource(Set<Authority> authorities, Authority authority, Set<Resource> resources) {
        authorities.add(authority);
        resources.addAll(authority.getResources());
        for (Authority inherit : authority.getInherits()) {
            if (!authorities.contains(inherit)){
                putResource(authorities, inherit,resources);
            }
        }
    }

    private void checkResourceTreeNode(Set<ResourceTreeNode> treeNodes, Set<Resource> resources) {
        if (treeNodes == null) return;
        List<ResourceTreeNode> removes = new ArrayList<>();
        for (ResourceTreeNode treeNode : treeNodes) {
            if (!resources.contains(new Resource().id(treeNode.getId()))){
                removes.add(treeNode);
            } else {
                checkResourceTreeNode(treeNode.getChildren(),resources);
            }
        }
        treeNodes.removeAll(removes);
    }
}
