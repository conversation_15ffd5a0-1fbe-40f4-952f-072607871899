package com.ihomeui.uaa.web.rest.dto;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2018/5/28
 */
public class PostDTO {

    private Long id;

    private String code;

    private String name;

    private String remarks;

    private List<String> employeeNames;

    private List<EmployeeDTO> employees;

    private Set<String> authorities;

    private boolean platformFlag;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public List<EmployeeDTO> getEmployees() {
        return employees;
    }

    public void setEmployees(List<EmployeeDTO> employees) {
        this.employees = employees;
    }

    public List<String> getEmployeeNames() {
        return employeeNames;
    }

    public void setEmployeeNames(List<String> employeeNames) {
        this.employeeNames = employeeNames;
    }

    public void getAuthorities(Set<String> authorities) {
        this.authorities = authorities;
    }

    public Set<String> getAuthorities() {
        return authorities;
    }

    public void setAuthorities(Set<String> authorities) {
        this.authorities = authorities;
    }

    public void setPlatformFlag(boolean platformFlag) {
        this.platformFlag = platformFlag;
    }

    public boolean getPlatformFlag() {
        return platformFlag;
    }
}
