package com.ihomeui.uaa.client.base;

import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class BaseRemoteService {

    private BaseClient baseClient;

    public BaseRemoteService(BaseClient baseClient){
        this.baseClient = baseClient;
    }

    @Cacheable(value="DictItemText", key = "'byKindAndValue:'+#kind+':'+#value")
    public String getDictItemText(String kind, String value){
        DictItem item = baseClient.getDictItem(kind, value).getBody();
        if(item == null) {
            return null;
        }
        return item.getText();
    }

    @Cacheable(value="DictItems", key = "'byKind:' + #kind")
    public List<DictItem> getDictItems(String kind){
        return baseClient.getDicts(kind).getBody();
    }

    @Cacheable(value="DictMap", keyGenerator = "keyValueCachingKeyGenerator")
    public Map<String, String> getDictMap(String... kinds){
        Map<String, String> map = new HashMap<>();
        List<DictItem> dicts = baseClient.getDicts(kinds).getBody();
        for (DictItem dict: dicts) {
            map.put(dict.getKind()+dict.getValue(),dict.getText());
        }
        return map;
    }

    @Cacheable(value="SettingValue", key = "'byName:'+#communityGroupId + ':'+ #communityId+':'+#name")
    public String getSettingValue(Long communityGroupId, Long communityId, String name) {
        return baseClient.getSettingValue(name, communityGroupId, communityId).getBody();
    }

    @Cacheable(value="Settings", key = "'byId:'+#communityGroupId + ':'+ #communityId")
    public Map<String, String> getSettings(Long communityGroupId, Long communityId) {
        return baseClient.getSettingsMap(communityGroupId, communityId, null).getBody();
    }

    @Cacheable(value="SettingsByPrefix", key = "'byId:'+#communityGroupId+':'+#communityId+':'+#prefix")
    public Map<String, String> getSettingsByPrefix(Long communityGroupId, Long communityId,String prefix) {
        return baseClient.getSettingsMap(communityGroupId, communityId,prefix).getBody();
    }

    @Cacheable(value="SysSettings", key = "'SysSettings'")
    public Map<String, String> getSysSettings() {
        return baseClient.getSettingsMap(null, null, null).getBody();
    }

    @Cacheable(value="CommunityGroupSettings", key = "'byId:'+#communityGroupId")
    public Map<String, String> getCommunityGroupSettings(Long communityGroupId){
        if(communityGroupId == null) {
            return new HashMap<>();
        }
        return baseClient.getSettingsMap(communityGroupId, null, null).getBody();
    }

    @Cacheable(value="CommunitySettings", key = "'byId:'+#communityId")
    public Map<String, String> getCommunitySettings(Long communityId){
        if(communityId == null) {
            return new HashMap<>();
        }
        return baseClient.getSettingsMap(null, communityId, null).getBody();
    }

    public DomainConfig getDomainConfigByDomain(String domainName) {
        return baseClient.getDomainConfigByDomain(domainName).getBody();
    }
}
