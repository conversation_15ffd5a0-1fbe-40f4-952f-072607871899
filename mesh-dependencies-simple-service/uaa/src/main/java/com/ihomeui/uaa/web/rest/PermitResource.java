package com.ihomeui.uaa.web.rest;

import com.ihomeui.mesh.criteria.CriteriaParser;
import com.ihomeui.mesh.web.utils.HeaderUtil;
import com.ihomeui.mesh.web.utils.PaginationUtil;
import com.ihomeui.uaa.domain.Permit;
import com.ihomeui.uaa.service.PermitService;
import com.ihomeui.uaa.service.PermitTreeNode;
import com.ihomeui.uaa.web.rest.dto.PermitDTO;
import com.ihomeui.uaa.web.rest.mapper.PermitMapper;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.ConfigAttribute;
import org.springframework.web.bind.annotation.*;
import javax.persistence.criteria.Predicate;
import javax.validation.Valid;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.*;

/**
 * REST controller for managing Permit.
 */
@RestController
@RequestMapping("/api")
public class PermitResource {

    private final Logger log = LoggerFactory.getLogger(PermitResource.class);

    private final PermitService permitService;

    private final PermitMapper permitMapper;

    public PermitResource(PermitService permitService, PermitMapper permitMapper) {
        this.permitService = permitService;
        this.permitMapper = permitMapper;
    }

    /**
     * POST  /permits : Create a new permit.
     *
     * @param permitDTO the permitDTO to create
     * @return the ResponseEntity with status 201 (Created) and with body the new permitDTO, or with status 400 (Bad Request) if the permit has already an ID
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PostMapping("/permits")
    @Timed
    public ResponseEntity<PermitDTO> createPermit(@Valid @RequestBody PermitDTO permitDTO) throws URISyntaxException {
        log.debug("REST request to save Permit : {}", permitDTO);
        if (permitDTO.getId() != null) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert("permit", "idexists", "A new permit cannot already have an ID")).body(null);
        }

        Permit permit = permitService.save(
            permitMapper.permitDTOToPermit(permitDTO),
            permitDTO.getAuthorities()
        );
        PermitDTO result = permitMapper.permitToPermitDTO(permit);

        return ResponseEntity.created(new URI("/api/permits/" + result.getId()))
            .headers(HeaderUtil.createEntityCreationAlert("permit", result.getId().toString()))
            .body(result);
    }

    /**
     * PUT  /permits : Updates an existing permit.
     *
     * @param permitDTO the permitDTO to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated permitDTO,
     * or with status 400 (Bad Request) if the permitDTO is not valid,
     * or with status 500 (Internal Server Error) if the permitDTO couldnt be updated
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PutMapping("/permits")
    @Timed
    public ResponseEntity<PermitDTO> updatePermit(@Valid @RequestBody PermitDTO permitDTO) throws URISyntaxException {
        log.debug("REST request to update Permit : {}", permitDTO);
        if (permitDTO.getId() == null) {
            return createPermit(permitDTO);
        }

        Permit permit = permitService.save(
            permitMapper.permitDTOToPermit(permitDTO),
            permitDTO.getAuthorities()
        );
        PermitDTO result = permitMapper.permitToPermitDTO(permit);

        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert("permit", permitDTO.getId().toString()))
            .body(result);
    }

    /**
     * GET  /permits : get all the permits.
     *
     * @param pageable the pagination information
     * @return the ResponseEntity with status 200 (OK) and the list of permits in body
     */
    @GetMapping("/permits")
    @Timed
    public ResponseEntity<List<PermitDTO>> getAllPermits(@RequestParam(required = false) String query,
                                                         @RequestParam(required = false) String serviceName,
                                                         @RequestParam(required = false) Long parentId,
                                                         @RequestParam(required = false) String[] kinds,
                                                         @ApiParam Pageable pageable) {
        log.debug("REST request to get a page of Permits");
        CriteriaParser criteriaParser = new CriteriaParser(query, new String[]{"name","label"});
        Page<PermitDTO> page = permitService.findAll((root, cq, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            Predicate p1 = criteriaParser.toPredicate(root, cq, cb);
            if(p1 != null) {
                predicates.add(p1);
            }
            if (parentId != null){
                if(parentId > 0) {
                    predicates.add(cb.equal(root.get("parent"), parentId));
                } else // -1 读取根节点
                {
                    predicates.add(cb.isNull(root.get("parent")));
                }
            }
            if (StringUtils.isNotBlank(serviceName)){
                predicates.add(cb.equal(root.get("serviceName"), serviceName));
            }
            if (kinds!=null){
                predicates.add(root.get("kind").in(kinds));
            }
            return cb.and(predicates.toArray(new Predicate[0]));
        }, pageable).map(permit -> permitMapper.permitToPermitDTO(permit));

        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/permits");
        return new ResponseEntity<>(page.getContent(), headers, HttpStatus.OK);
    }

    /**
     * GET  /permits/:id : get the "id" permit.
     *
     * @param id the id of the permitDTO to retrieve
     * @return the ResponseEntity with status 200 (OK) and with body the permitDTO, or with status 404 (Not Found)
     */
    @GetMapping("/permits/{id}")
    @Timed
    public ResponseEntity<PermitDTO> getPermit(@PathVariable Long id) {
        log.debug("REST request to get Permit : {}", id);

        PermitDTO permitDTO = permitMapper.permitToPermitDTO(permitService.findOne(id));

        return Optional.ofNullable(permitDTO)
            .map(result -> new ResponseEntity<>(
                result,
                HttpStatus.OK))
            .orElse(new ResponseEntity<>(HttpStatus.NOT_FOUND));
    }

    @GetMapping("/_permits/get_ant_matchers")
    @Timed
    public ResponseEntity<Map<String, Collection<ConfigAttribute>>> getAntMatchers(
        @RequestParam(required = true)String serviceName,
        @RequestParam(required = true)String requestMethod
    ){
        log.debug("REST request to get antMatchers : serviceName={}, requestMethod={}", serviceName, requestMethod);
        return new ResponseEntity<>(permitService.getAntMatchers(serviceName, requestMethod), HttpStatus.OK);
    }

    @GetMapping("/_permits/get_permissions")
    @Timed
    public ResponseEntity<Map<String, Collection<String>>> getPermissions( @RequestParam String serviceName ) {
        log.debug("REST request to get getPermissions serviceName = {}", serviceName);
        return new ResponseEntity<>(permitService.getPermissions(serviceName), HttpStatus.OK);
    }

    /**
     * DELETE  /permits/:id : delete the "id" permit.
     *
     * @param id the id of the permitDTO to delete
     * @return the ResponseEntity with status 200 (OK)
     */
    @DeleteMapping("/permits/{id}")
    @Timed
    public ResponseEntity<Void> deletePermit(@PathVariable Long id) {
        log.debug("REST request to delete Permit : {}", id);
        permitService.delete(id);
        return ResponseEntity.ok().headers(HeaderUtil.createEntityDeletionAlert("permit", id.toString())).build();
    }



    @GetMapping("/permit-tree")
    @Timed
    public ResponseEntity<PermitTreeNode> getPermitTree(@ApiParam(value="指定根节点接口权限引用名，如果未指定则返回整个接口权限树")
                                                        @RequestParam(required = false) String name) {
        log.debug("REST request to get Resource : name={}", name);
        PermitTreeNode treeNode = permitService.getPermitTree(name);
        return Optional.ofNullable(treeNode)
            .map(result -> new ResponseEntity<>(
                result,
                HttpStatus.OK))
            .orElse(new ResponseEntity<>(HttpStatus.NOT_FOUND));
    }

    @GetMapping("/permit-tree-list")
    @Timed
    public ResponseEntity<List<PermitTreeNode>> getPermitTreeList(@ApiParam(value="指定根节点接口权限名，可以指定多个接口权限名，使用','分融，如未指定则获取根下的所有节点")
                                                                  @RequestParam(required = false) String names) {
        log.debug("REST request to get Resource : names={}", names);
        List<PermitTreeNode> treeNodeList = permitService.getPermitTreeList(names);
        return Optional.ofNullable(treeNodeList)
            .map(result -> new ResponseEntity<>(
                result,
                HttpStatus.OK))
            .orElse(new ResponseEntity<>(HttpStatus.NOT_FOUND));
    }
}
