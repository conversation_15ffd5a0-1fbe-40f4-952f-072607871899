package com.ihomeui.uaa.openauth.weixn;

import com.alibaba.fastjson.JSONObject;
import com.ihomeui.mesh.config.OtherApiWrapper;
import com.ihomeui.mesh.utils.StringUtils;
import com.ihomeui.uaa.openauth.bo.OpenAuthUserInfo;
import com.ihomeui.uaa.openauth.weixn.exception.WeixinApiException;
import com.ihomeui.uaa.domain.enumeration.AuthIdType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

@Service
public class WeixinHttpApi extends OtherApiWrapper {

    private final Logger log = LoggerFactory.getLogger(WeixinHttpApi.class);

    private static final String AUTH_URL = "https://api.weixin.qq.com/sns/oauth2/access_token?appid=%s&secret=%s&code=%s&grant_type=authorization_code";
    private static final String ACCESS_TOKEN_URL = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s";
    private static final String CREATE_QR_CODE = "https://api.weixin.qq.com/cgi-bin/qrcode/create?access_token=%s";
    private static final String AUTH_USER_INFO_URL = "https://api.weixin.qq.com/sns/userinfo?access_token=%s&openid=%s&lang=zh_CN";
    private static final String GET_USER_INFO_URL = "https://api.weixin.qq.com/cgi-bin/user/info?access_token=%s&openid=%s&lang=zh_CN";
    private static final String TINY_APP_LOGIN = "https://api.weixin.qq.com/sns/jscode2session?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code";

    protected WeixinHttpApi(RestTemplate otherApiRestTemplate) {
        super(otherApiRestTemplate);
    }

    /**
     * 微信公众号授权
     * @param appId 应用ID
     * @param appSecret 应用秘钥
     * @param code 重定向获取的code
     * @return 指定返回对象
     */
    public OpenAuthUserInfo oauth2(String appId, String appSecret, String code) throws WeixinApiException {
        String url = String.format(AUTH_URL,appId,appSecret,code);
        ResponseEntity result = doGet(url);
        Object body = result.getBody();
        if (body == null){
            throw new WeixinApiException((Integer) null);
        }
        JSONObject weixinAuthRet = JSONObject.parseObject(body.toString());
        log.debug("调用授权的接口返回数据{}",weixinAuthRet);

        OpenAuthUserInfo openAuthUserInfo = new OpenAuthUserInfo();
        openAuthUserInfo.setAuthAppId(appId);

        String unionId = weixinAuthRet.getString(WeixinAuth.UNIONID);
        openAuthUserInfo.setAuthUserId(weixinAuthRet.getString(WeixinAuth.OPENID));
        if (StringUtils.isNotBlank(unionId)){
            openAuthUserInfo.setAuthIdType(AuthIdType.WEIXIN_NNIONID);
            openAuthUserInfo.setAuthId(unionId);
        } else {
            openAuthUserInfo.setAuthId(weixinAuthRet.getString(WeixinAuth.OPENID));
            openAuthUserInfo.setAuthIdType(AuthIdType.WEIXIN_OPENID);
        }
        try {
            JSONObject weixinAuthUserInfo =
                userInfo(weixinAuthRet.getString(WeixinAuth.ACCESS_TOKEN), openAuthUserInfo.getAuthUserId());
            log.debug("调用获取用户信息的接口返回数据{}",weixinAuthUserInfo);
            openAuthUserInfo.setAuthUserName(weixinAuthUserInfo.getString(WeixinAuthUserInfo.NICKNAME));
        } catch (WeixinApiException e){
            log.error("调取微信用户名接口异常{}",e.getMessage());
        }
        return openAuthUserInfo;
    }

    private JSONObject userInfo(String accessToken, String authUserId) throws WeixinApiException {
        String url = String.format(AUTH_USER_INFO_URL,accessToken,authUserId);
        ResponseEntity result = doGet(url);
        Object body = result.getBody();
        if (body == null){
            throw new WeixinApiException((Integer) null);
        }
        JSONObject jsonObject = JSONObject.parseObject(body.toString());
        WeixinApiException.isSuccess(jsonObject);
        return jsonObject;
    }

    public JSONObject getAccessToken(String appId, String appSecret) throws WeixinApiException {
        log.debug("获取 AccessToken appId={} appSecret={}",appId,appSecret);
        ResponseEntity responseEntity = doGet(String.format(ACCESS_TOKEN_URL,appId,appSecret));
        Object result = responseEntity.getBody();
        if (result == null) throw new WeixinApiException();
        JSONObject accessTokenJson = JSONObject.parseObject((String) result);
        log.debug("获取token 返回数据 {}",accessTokenJson.toJSONString());

        String accessToken = accessTokenJson.getString("access_token");
        if ( accessToken == null ) {
            throw new WeixinApiException(accessTokenJson);
        }
        accessTokenJson.put("create_time", System.currentTimeMillis());
        return accessTokenJson;
    }

    public JSONObject createQrcode(String accessToken, Map<String, Object> params) throws WeixinApiException {
        ResponseEntity responseEntity = doPost(String.format(CREATE_QR_CODE,accessToken),params);
        if ( responseEntity.getStatusCode().equals(HttpStatus.OK) ) {
            Object result = responseEntity.getBody();
            if (result == null) throw new WeixinApiException();
            JSONObject jsonObject = JSONObject.parseObject((String) result);
            log.debug("生产二维码返回数据 {}",jsonObject.toJSONString());
            if ( jsonObject.getString("url") != null ) {
                return jsonObject;
            }
            throw new WeixinApiException(jsonObject);
        }
        throw new WeixinApiException();
    }

    public OpenAuthUserInfo getUserInfo(String accessToken, String openId) throws WeixinApiException {
        Object result = doGet(String.format(GET_USER_INFO_URL,accessToken,openId)).getBody();
        if (result == null) throw new WeixinApiException();

        JSONObject weixinAuthUserInfo = JSONObject.parseObject((String) result);
        log.debug("获取token 返回数据 {}",weixinAuthUserInfo.toJSONString());
        if (weixinAuthUserInfo.getString(WeixinAuthUserInfo.OPENID) == null){
            throw new WeixinApiException(weixinAuthUserInfo);
        }
        OpenAuthUserInfo openAuthUserInfo = new OpenAuthUserInfo();
        openAuthUserInfo.setAuthId(weixinAuthUserInfo.getString(WeixinAuthUserInfo.UNIONID));
        openAuthUserInfo.setAuthUserId(weixinAuthUserInfo.getString(WeixinAuthUserInfo.OPENID));
        openAuthUserInfo.setAuthUserName(weixinAuthUserInfo.getString(WeixinAuthUserInfo.NICKNAME));
        if ( openAuthUserInfo.getAuthId() != null ) openAuthUserInfo.setAuthIdType(AuthIdType.WEIXIN_NNIONID);
        if ( openAuthUserInfo.getAuthId() == null ) {
            openAuthUserInfo.setAuthIdType(AuthIdType.WEIXIN_OPENID);
            openAuthUserInfo.setAuthId(openAuthUserInfo.getAuthUserId());
        }
        return openAuthUserInfo;
    }

    public OpenAuthUserInfo getAuthUserInfo(String accessToken, String openId) throws WeixinApiException {
        Object result = doGet(String.format(AUTH_USER_INFO_URL,accessToken,openId)).getBody();
        if (result == null) throw new WeixinApiException();

        JSONObject weixinAuthUserInfo = JSONObject.parseObject((String) result);
        log.debug("获取token 返回数据 {}",weixinAuthUserInfo.toJSONString());
        if (weixinAuthUserInfo.getString(WeixinAuthUserInfo.OPENID) == null){
            throw new WeixinApiException(weixinAuthUserInfo);
        }
        OpenAuthUserInfo openAuthUserInfo = new OpenAuthUserInfo();
        openAuthUserInfo.setAuthId(weixinAuthUserInfo.getString(WeixinAuthUserInfo.UNIONID));
        openAuthUserInfo.setAuthUserId(weixinAuthUserInfo.getString(WeixinAuthUserInfo.OPENID));
        openAuthUserInfo.setAuthUserName(weixinAuthUserInfo.getString(WeixinAuthUserInfo.NICKNAME));
        if ( openAuthUserInfo.getAuthId() != null ) openAuthUserInfo.setAuthIdType(AuthIdType.WEIXIN_NNIONID);
        if ( openAuthUserInfo.getAuthId() == null ) {
            openAuthUserInfo.setAuthIdType(AuthIdType.WEIXIN_OPENID);
            openAuthUserInfo.setAuthId(openAuthUserInfo.getAuthUserId());
        }
        return openAuthUserInfo;
    }

    public  JSONObject tinyAppLogin(String appId, String appSecret, String code) throws WeixinApiException {
        String url = String.format(TINY_APP_LOGIN,appId,appSecret,code);
        ResponseEntity result = doGet(url);
        Object body = result.getBody();
        if (body == null){
            throw new WeixinApiException();
        }
        JSONObject wxResult=JSONObject.parseObject(body.toString());
        if(StringUtils.isNotBlank(wxResult.getString("errcode"))){
            Map<Integer, String> map = new HashMap<>();
            map.put(wxResult.getInteger("errcode"),wxResult.getString("errmsg"));
            throw new WeixinApiException(map);
        }
        return wxResult;
    }


    public static class WeixinAuth {
        static final String OPENID = "openid";
        static final String UNIONID = "unionid";
        static final String ACCESS_TOKEN = "access_token";
    }

    public static class WeixinAuthUserInfo {
        static final String NICKNAME = "nickname";
        static final String OPENID = "openid";
        static final String UNIONID = "unionid";
    }
}
