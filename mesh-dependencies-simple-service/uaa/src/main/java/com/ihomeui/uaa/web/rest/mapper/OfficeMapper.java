package com.ihomeui.uaa.web.rest.mapper;

import com.ihomeui.uaa.domain.Authority;
import com.ihomeui.uaa.domain.Office;
import com.ihomeui.uaa.domain.User;
import com.ihomeui.uaa.web.rest.dto.*;
import org.mapstruct.*;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Mapper for the entity Office and its dto OfficeDTO.
 */
@Mapper(componentModel = "spring", uses = {})
public interface OfficeMapper {

    @Mapping(source = "master.id", target = "masterId")
    @Mapping(source = "parent.id", target = "parentId")
    OfficeDTO officeToOfficeDTO(Office office);

    List<OfficeDTO> officesToOfficeDTOs(List<Office> offices);

    @Mapping(source = "masterId", target = "master")
    @Mapping(source = "parentId", target = "parent")
    @Mapping(target = "children", ignore = true)
    @Mapping(target = "users", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdDate", ignore = true)
    @Mapping(target = "lastModifiedBy", ignore = true)
    @Mapping(target = "lastModifiedDate", ignore = true)
    Office officeDTOToOffice(OfficeDTO officeDTO);

    List<Office> officeDTOsToOffices(List<OfficeDTO> officeDTOs);

    @Mapping(source = "pathName", target = "code")
    PostDTO officeToPostDTO(Office office);

    @Mapping(target = "code",ignore = true)
    Office postDTOToOffice(PostDTO post);

    @Mapping(source = "parent.id", target = "parentId")
    @Mapping(source = "pathName", target = "code")
    @Mapping(target = "children",ignore = true)
    DepartmentDTO officeToDepartmentDTO(Office office);

    @Mapping(target = "code",ignore = true)
    @Mapping(source = "parentId", target = "parent")
    @Mapping(target = "children",ignore = true)
    Office departmentDTOToOffice(DepartmentDTO department);

    @Mapping(source = "parent.id", target = "parentId")
    @Mapping(source = "pathName", target = "code")
    @Mapping(target = "children",ignore = true)
    BranchRegionDTO officeToBranchRegionDTO(Office office);

    @Mapping(target = "code",ignore = true)
    @Mapping(source = "parentId", target = "parent")
    @Mapping(target = "children",ignore = true)
    Office branchRegionToOffice(BranchRegionDTO branchRegion);

    @Mapping(source = "pathName", target = "code")
    CommunityDTO officeToCommunityDTO(Office office);

    @Mapping(target = "code",ignore = true)
    Office communityDTOToOffice(CommunityDTO community);

    default Set<String> stringsFromAuthorities (Set<Authority> authorities) {
        return authorities.stream().map(Authority::getName)
                .collect(Collectors.toSet());
    }

    default Set<Authority> authoritiesFromStrings(Set<String> strings) {
        if ( strings == null ) return null;
        return strings.stream().map(string -> {
            Authority auth = new Authority();
            auth.setName(string);
            return auth;
        }).collect(Collectors.toSet());
    }

    default Set<Long> longsFromOffices (Set<Office> offices) {
        return offices.stream().map(Office::getId)
                .collect(Collectors.toSet());
    }

    default Set<Office> officesFromLongs(Set<Long> longs) {
        return longs.stream().map(id -> {
            Office office = new Office();
            office.setId(id);
            return office;
        }).collect(Collectors.toSet());
    }

    default User userFromId(Long id) {
        if (id == null) {
            return null;
        }
        User user = new User();
        user.setId(id);
        return user;
    }

    default Office officeFromId(Long id) {
        if (id == null) {
            return null;
        }
        Office office = new Office();
        office.setId(id);
        return office;
    }

    List<OfficeDTO> officesToOfficeDTOs(Set<Office> offices);

    List<BranchRegionDTO> officesToBranchRegionDTOs(List<Office> allBranchRegions);

    Set<BranchRegionDTO> officesToBranchRegionDTOSets(List<Office> allBranchRegions);

    List<CommunityDTO> officesToCommunityDTOs(List<Office> allCommunities);

    @Mapping(target = "children" ,ignore = true)
    @Mapping(target = "parentId", source = "parent.id")
    SimpleOfficeDTO officeToSimpleOfficeDTO(Office office);

    List<SimpleOfficeDTO> officeSetToSimpleOfficeDTOs(Set<Office> myCommunities);

    List<SimpleOfficeDTO> officesToSimpleOfficeDTOs(List<Office> allBranchRegions);
}
