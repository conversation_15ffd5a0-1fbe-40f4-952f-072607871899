package com.ihomeui.uaa.web.rest.vm;

import java.io.Serializable;
import java.text.Collator;
import java.util.Comparator;

/**
 * Created by Administrator on 2017/8/10.
 */
public class SimpleCommunityVM implements Serializable,Comparable<SimpleCommunityVM> {

    private Long id;
    private String name;
    private Long communityGroupId;
    private String communityGroupName;
    private String area;

    public SimpleCommunityVM(){}

    public SimpleCommunityVM(Long id, String name, Long communityGroupId, String communityGroupName){
        this.id = id;
        this.name = name;
        this.communityGroupId = communityGroupId;
        this.communityGroupName = communityGroupName;
    }

    public SimpleCommunityVM(Long id, String name, Long communityGroupId, String communityGroupName,String area){
        this.id = id;
        this.name = name;
        this.communityGroupId = communityGroupId;
        this.communityGroupName = communityGroupName;
        this.area = area;
    }


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getCommunityGroupId() {
        return communityGroupId;
    }

    public void setCommunityGroupId(Long communityGroupId) {
        this.communityGroupId = communityGroupId;
    }

    public String getCommunityGroupName() {
        return communityGroupName;
    }

    public void setCommunityGroupName(String communityGroupName) {
        this.communityGroupName = communityGroupName;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    @Override
    public int compareTo(SimpleCommunityVM vm){
        String s1 = (communityGroupName == null ? "":communityGroupName) +"." + (area == null ? "":area) + "." + (name == null ? "":name);
        String s2 = (vm.communityGroupName == null ? "":vm.communityGroupName) +"." + (vm.area == null ? "":vm.area) + "." + (vm.name == null ? "":vm.name);

        Comparator cmp = Collator.getInstance(java.util.Locale.CHINA);
        return cmp.compare(s1,s2);//s1.compareTo(s2);
    }
}
