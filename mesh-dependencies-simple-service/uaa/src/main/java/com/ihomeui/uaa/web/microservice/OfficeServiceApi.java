package com.ihomeui.uaa.web.microservice;

import com.ihomeui.uaa.domain.Office;
import com.ihomeui.uaa.domain.Office_;
import com.ihomeui.uaa.domain.User;
import com.ihomeui.uaa.domain.constants.OfficeType;
import com.ihomeui.uaa.service.OfficeService;
import com.ihomeui.uaa.service.UserService;
import com.ihomeui.uaa.web.rest.dto.OfficeDTO;
import com.ihomeui.uaa.web.rest.dto.SimpleOfficeDTO;
import com.ihomeui.uaa.web.rest.dto.UserDTO;
import com.ihomeui.uaa.web.rest.mapper.OfficeMapper;
import com.ihomeui.uaa.web.rest.mapper.UserMapper;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/micro-service")
public class OfficeServiceApi {

    private final UserService userService;

    private final OfficeService officeService;

    private final OfficeMapper officeMapper;

    private final UserMapper userMapper;

    public OfficeServiceApi(UserService userService, OfficeService officeService,
                            OfficeMapper officeMapper, UserMapper userMapper
    ) {
        this.userService = userService;
        this.officeService = officeService;
        this.officeMapper = officeMapper;
        this.userMapper = userMapper;
    }

    @ApiOperation("获取用户绑定的商户id数组")
    @GetMapping("/user-office-ids")
    public ResponseEntity<List<Long>> getUserOfficeIds(
        @RequestParam Long userId
    ){
        User user = userService.findOne(userId);
        if ( user == null ) {
            return ResponseEntity.ok(new ArrayList<>());
        }
        return ResponseEntity.ok(user.getOffices().stream().map(Office::getId).collect(Collectors.toList()));
    }

    @GetMapping("/offices/get-office-location")
    public ResponseEntity<Map<String,String>> getOfficeLocation(
        @RequestParam Long id
    ){
        Office office = officeService.findOne(id);
        Map<String,String> resultMap = new HashMap<>();
        resultMap.put("latitude",office.getLatitude());
        resultMap.put("longitude",office.getLongitude());
        return ResponseEntity.ok(resultMap);
    }

    @ApiOperation("获取机构")
    @GetMapping("/simple-office")
    public ResponseEntity<List<SimpleOfficeDTO>> getSimpleOffices(
        @RequestParam Long[] ids
    ){
        List<SimpleOfficeDTO> simpleOffices = officeService.findAll((root, cq, cb) -> root.get(Office_.id).in(ids))
            .stream()
            .map(officeMapper::officeToSimpleOfficeDTO)
            .collect(Collectors.toList());
        return ResponseEntity.ok(simpleOffices);
    }

    @ApiOperation("获取机构")
    @GetMapping("/merchant-master")
    public ResponseEntity<Long> getMerchantMaster(
        @RequestParam Long merchantId
    ){
        Office office = officeService.findOne(merchantId);
        if (office.getMaster() != null){
            return ResponseEntity.ok(office.getMaster().getId());
        }
        return ResponseEntity.notFound().build();
    }

    @ApiOperation("获取用户默认商户ID")
    @GetMapping("/default-merchant-id")
    public ResponseEntity<Long> getDefaultMerchantId(
        @RequestParam Long userId
    ){
        User user = userService.findOne(userId);
        Office office = officeService.getMyDefaultMerchant(user);
        return ResponseEntity.ok(office.getId());
    }

    @ApiOperation("判断是否是机构管理员")
    @GetMapping("/offices/is-office-owner")
    public ResponseEntity<Boolean> isOfficeOwner(
        @RequestParam Long officeId,
        @RequestParam Long userId
    ){
        return ResponseEntity.ok(officeService.isOfficeOwner(officeId,userId));
    }

    @ApiOperation("判断是否是机构Master")
    @GetMapping("/offices/is-office-master")
    public ResponseEntity<Boolean> isOfficeMaster(
        @RequestParam Long officeId,
        @RequestParam Long userId
    ){
        return ResponseEntity.ok(officeService.isOfficeMaster(officeId,userId));
    }

    @ApiOperation("获取管理员所在的机构")
    @GetMapping("/offices/master-office")
    public ResponseEntity<OfficeDTO> getMasterOffice(
        @RequestParam Long userId
    ){
        return ResponseEntity.ok(
            officeMapper.officeToOfficeDTO(officeService.getMasterOffice(userId))
        );
    }

    @ApiOperation("获取机构master")
    @GetMapping("/offices/office-master")
    public ResponseEntity<UserDTO> getOfficeMaster(
        @RequestParam Long officeId
    ){
        Office office = officeService.findOne(officeId);
        if (office!=null){
            User user = office.getMaster();
            if (user!=null){
                return ResponseEntity.ok(userMapper.userToUserDTO(user));
            }
        }
        return ResponseEntity.notFound().build();
    }

    @ApiOperation("获取用户关联的公司ID的父公司ID")
    @GetMapping("/offices/user-parent-company-ids")
    public ResponseEntity<Set<Long>> getUserParentCompanyIds(
        @RequestParam Long userId
    ){
        return ResponseEntity.ok(
            officeService.getOwnerParentOffices(userId,null, new HashSet<String>(){{
                add(OfficeType.COMPANY);
            }}).stream().map(Office::getId).collect(Collectors.toSet())
        );
    }

    @ApiOperation("获取用户直属机构")
    @GetMapping("/offices/user_owned_ids")
    public ResponseEntity<List<Long>> getUserOwnedIds(
        @RequestParam Long userId,
        @RequestParam String ancestorOfficeCode,
        @RequestParam( defaultValue = OfficeType.COMPANY ) String officeType,
        @RequestParam( defaultValue = "false" ) Boolean includeSubCompanies
    ){
        return ResponseEntity.ok(
            officeService.findTypedOfficeIdsInAncestor(
                userId,
                ancestorOfficeCode,
                officeType,
                includeSubCompanies
            )
        );
    }

    @ApiOperation("获取公司")
    @GetMapping("/offices/companies")
    public ResponseEntity<List<OfficeDTO>> getCompanies(
        @RequestParam String parentCode,
        @RequestParam Integer page,
        @RequestParam Integer size,
        @RequestParam(required = false) Set<Long> ignore
    ){
        return ResponseEntity.ok(
            officeService.findAll((root, cq, cb) -> cb.and(
                cb.equal(root.get(Office_.parent).get(Office_.code),parentCode),
                cb.equal(root.get(Office_.officeType),OfficeType.COMPANY)
            ),PageRequest.of(page,size)).stream().filter(office -> {
                if( ignore == null ) return true;
                return !ignore.contains(office.getId());
            }).map(officeMapper::officeToOfficeDTO).collect(Collectors.toList())
        );
    }

}
