# ===================================================================
# Spring Boot configuration for the "dev" profile.
# ===================================================================

logging:
    level:
        com.jmt: INFO
        # ${mesh.application.base-package}表达式只能生效在值上

eureka:
    instance:
        prefer-ip-address: true
    client:
        service-url:
            defaultZone: ${mesh.registry.uri}/eureka/

management:
    health:
        elasticsearch:
            enabled: false

spring:
    profiles:
        active: dev
        include:
            - swagger
    devtools: # 开发工具配置
        restart:
            enabled: true
        livereload:
            enabled: false # we use Webpack dev server + BrowserSync for livereload   advertising
    jackson:
        serialization:
            indent-output: true
    datasource:
        type: com.alibaba.druid.pool.DruidDataSource
        url: *************************************************************************************************************************************************************************************
#        url: ***********************************************************************************************************************************************************************************
        username: platform
        password: Jmt12&^34*&56
   #     url: *****************************************************************************************************************************************************
  #      username: root
 #       password: root
#        hikari:
#            poolName: Hikari
#            auto-commit: false
#            data-source-properties:
#                cachePrepStmts: true
#                prepStmtCacheSize: 250
#                prepStmtCacheSqlLimit: 2048
#                useServerPrepStmts: true
#    jpa:
#        database-platform: org.hibernate.dialect.MySQL5InnoDBDialect
#        database: MYSQL
#        show-sql: true
#        properties:
#            hibernate.id.new_generator_mappings: true
#            hibernate.connection.provider_disables_autocommit: true
#            hibernate.cache.use_second_level_cache: true
#            hibernate.cache.use_query_cache: false
#            hibernate.generate_statistics: false
#            hibernate.cache.region.factory_class: com.hazelcast.hibernate.HazelcastCacheRegionFactory
#            hibernate.cache.hazelcast.instance_name: ${mesh.application.name}
#            hibernate.cache.use_minimal_puts: true
#            hibernate.cache.hazelcast.use_lite_member: true

    liquibase:
        # Remove 'faker' if you do not want the sample data to be loaded automatically
        contexts: dev, faker

    messages:
        cache-duration: PT1S # 1 second, see the ISO 8601 standard
    thymeleaf:
        cache: false

server:
    port: ${mesh.application.server-port}
