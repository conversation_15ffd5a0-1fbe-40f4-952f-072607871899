package com.platform.util;

import com.alibaba.fastjson.JSON;
import com.platform.JmtAppHelper;
import com.platform.client.uaa.AppletsType;
import com.platform.client.uaa.UaaClient;
import com.platform.client.uaa.UserOpenAuthInfoDto;
import com.platform.utils.SpringContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Optional;

@Slf4j
public class UserUtil {

    private static JmtAppHelper JMT_APP_HELPER = SpringContextUtils.getBean(JmtAppHelper.class);

    private static final RedisUtil REDIS_UTIL = SpringContextUtils.getBean(RedisUtil.class);

    private static final String LOGIN_USER_INFO = "LOGIN:USER_INFO:RECHARGE:";

    private static final long LOGIN_USER_INFO_EXPIRE_TIME = 2 * 3600;

    private static final UaaClient uaaClient=SpringContextUtils.getBean(UaaClient.class);
    
    public static UserOpenAuthInfoDto getSysUser(){
        UserOpenAuthInfoDto userInfo=null;
        String obj= REDIS_UTIL.get(LOGIN_USER_INFO+JMT_APP_HELPER.getCurrentUserId());
        if(StringUtils.isNotBlank(obj)){
            userInfo = JSON.parseObject(obj,UserOpenAuthInfoDto.class);
        }else {
            userInfo=uaaClient.getUserAuthInfo(getLoginUserId(), "RECHARGE");
            Optional.ofNullable(userInfo).ifPresent(user -> {
                REDIS_UTIL.set(LOGIN_USER_INFO+JMT_APP_HELPER.getCurrentUserId(), JSON.toJSONString(user),LOGIN_USER_INFO_EXPIRE_TIME);
            });
        }
        return userInfo;
    }

    public static Long getLoginUserId(){
        return JMT_APP_HELPER.getCurrentUserId();
    }
}
