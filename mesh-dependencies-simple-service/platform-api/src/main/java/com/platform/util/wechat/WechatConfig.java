package com.platform.util.wechat;

import com.platform.utils.ResourceUtil;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLContexts;

import javax.net.ssl.SSLContext;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.security.KeyStore;

@SuppressWarnings("deprecation")
public class WechatConfig {

    private static SSLConnectionSocketFactory sslcsf;

    public static SSLConnectionSocketFactory getSslcsf() {
        if (null == sslcsf) {
            setSsslcsf();
        }
        return sslcsf;
    }

    private static void setSsslcsf() {

        try {
            String mchId=ResourceUtil.getConfigByName("wx.mchId");//商户号
            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            Thread.currentThread().getContextClassLoader();
            // 读取本机存放的PKCS12证书文件
           // InputStream instream = new WechatRefundApiResult().getClass().getResourceAsStream(ResourceUtil.getConfigByName("wx.certName"));
            String path =  WechatRefundApiResult.class.getClassLoader().getResource(ResourceUtil.getConfigByName("wx.certName")).getPath();

          //  FileInputStream instream = new FileInputStream("C:\Users\<USER>\Desktop\apiclient_cert.p12");
            FileInputStream instream  = new FileInputStream(new File(path));
            System.out.println(instream.toString().length());
            try {
                keyStore.load(instream, mchId.toCharArray());
            } finally {
                instream.close();
            }
            SSLContext sslcontext = SSLContexts.custom().loadKeyMaterial(keyStore, mchId.toCharArray()).build();
            sslcsf = new SSLConnectionSocketFactory(sslcontext, new String[]{"TLSv1"}, null, SSLConnectionSocketFactory.BROWSER_COMPATIBLE_HOSTNAME_VERIFIER);
            System.out.println("结束");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


}
