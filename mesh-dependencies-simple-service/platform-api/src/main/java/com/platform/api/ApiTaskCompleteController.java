package com.platform.api;

import com.platform.annotation.LoginUser;
import com.platform.entity.*;
import com.platform.service.ApiTaskCompleteService;
import com.platform.service.ApiUserService;
import com.platform.service.SysTaskService;
import com.platform.util.ApiBaseAction;
import com.platform.util.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @program platform
 * @description 任务完成
 * @date 2019-09-11 17:00
 **/
@Api(tags = "任务")
@RestController
@RequestMapping("/auth/api/task")
public class ApiTaskCompleteController extends ApiBaseAction {

    @Autowired
    private ApiTaskCompleteService apiTaskCompleteService;
    @Autowired
    private SysTaskService sysTaskService;
    @Autowired
    private ApiUserService userService;

    /**
     * 查看个人任务列表
     *
     * @return
     */
    @ApiOperation(value = "列表")
    @GetMapping(value = "/list")
    public Object list(String userId) {
        UserVo userVo = new UserVo();
        Long l = Long.parseLong(userId);
        userVo.setUserId((l));
        List<TaskCompleteList> list = apiTaskCompleteService.list(userVo);
        return toResponsSuccess(list);

    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "/save")
    public Object save(@LoginUser UserVo loginUser, String taskId) {
        try {
            if (loginUser == null || loginUser.getWeixin_openid() == null || taskId == null || "".equals(taskId)) {
                return toResponsFail("参数有误");
                // return R.error("参数有误");
            } else {
                SysTaskEntity info = sysTaskService.info(taskId);
                if (info.getTaskStatus() == 1) {
                    //是否已经完成过该任务
                    int id = Integer.parseInt(taskId);
                    String openid = loginUser.getWeixin_openid();
                    int num = apiTaskCompleteService.queryTaskComplete(id, openid);
                    if (num > 0) {
                        return toResponsFail("该任务已经完成过了");
                    }
                    TaskComplete taskComplete = new TaskComplete();
                    taskComplete.setTaskId(Integer.valueOf(taskId));
                    taskComplete.setTaskIntegral(info.getTaskIntegral());
                    String userId = String.valueOf(loginUser.getUserId());
                    taskComplete.setUserId(Integer.valueOf(userId));
                    taskComplete.setOpenid(loginUser.getWeixin_openid());
                    taskComplete.setCreateTime(new Date());
                    taskComplete.setTaskName(info.getTaskName());
                    taskComplete.setTaskContent(info.getTaskContent());
                    taskComplete.setTaskType(info.getTaskType());
                    taskComplete.setTaskUrl(info.getTaskUrl());
                    taskComplete.setTaskPublicAddress(info.getTaskPublicAddress());
                    apiTaskCompleteService.save(taskComplete);
                    //任务完成
                    //更改用户的分钟和积分
                    UserVo userVo = userService.queryByOpenId(loginUser.getWeixin_openid());

                    int integral = userVo.getIntegral() + info.getTaskIntegral();//任务完成的积分
                    userVo.setIntegral(integral);//积分
                    userService.update(userVo);

                } else {
                    return toResponsFail("无效任务");
                }
            }
        } catch (Exception e) {
            return toResponsFail("失败");
        }
        return toResponsMsgSuccess("成功");
    }


    @ApiOperation("新增完成任务")
    @RequestMapping(value = "/addTaskComplete", method = RequestMethod.POST)
    public Result<Object> insertTaskComplete(@RequestBody @Valid AddNideshopTaskComplete taskComplete) {
        return apiTaskCompleteService.insertTaskComplete(taskComplete);
    }
}
