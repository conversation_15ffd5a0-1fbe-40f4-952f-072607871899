package com.platform.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * 任务配置表(NideshopTask)实体类
 *
 * <AUTHOR>
 * @since 2020-03-23 14:36:19
 */
@Data
@ApiModel
public class NideshopTask implements Serializable {
    private static final long serialVersionUID = 894474244904555168L;
    /**
    * 主键 任务id
    */
    private Integer taskId;
    /**
    * 任务名称
    */
    private String taskName;
    /**
    * 任务内容
    */
    private String taskContent;
    /**
    * 任务类型
    */
    private Integer taskType;
    /**
    * 任务积分
    */
    private Integer taskIntegral;
    /**
    * 任务完成次数
    */
    private Integer taskNumber;
    /**
    * 状态0：禁用 1：启用
    */
    private Integer taskStatus;
    /**
    * 创建时间
    */
    private Date createTime;
    /**
    * 任务url链接
    */
    private String taskUrl;
    /**
    * 任务公众号
    */
    private String taskPublicAddress;
    /**
    * 视频路劲
    */
    private String videourl;
    /**
    * 视频时长
    */
    private Integer videotime;
    /**
    * 观看时长
    */
    private Integer watchtime;
    /**
     * 观看视频地址
     */
    private String videoImageUrl;




}