package com.platform.client.uaa;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
public class UserPointsRecordDTO {

    private Long uaaId;

    private Integer integralNum;

    private Integer  integralSource;

    private Integer  integralSourceDetail;

    private Integer  integralType;

    private String  integralDetail;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;
}
