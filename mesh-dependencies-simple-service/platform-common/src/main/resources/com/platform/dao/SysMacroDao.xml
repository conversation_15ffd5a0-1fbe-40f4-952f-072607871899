<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.platform.dao.SysMacroDao">

    <resultMap type="com.platform.entity.SysMacroEntity" id="sysMacroMap">
        <result property="id" column="id"/>
        <result property="parentId" column="parent_id"/>
        <result property="name" column="name"/>
        <result property="value" column="value"/>
        <result property="status" column="status"/>
        <result property="type" column="type"/>
        <result property="orderNum" column="order_num"/>
        <result property="remark" column="remark"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <select id="queryObject" resultType="com.platform.entity.SysMacroEntity">
		select
			`id`,
			`parent_id`,
			`name`,
			`value`,
			`status`,
			`type`,
			`order_num`,
			`remark`,
			`gmt_create`,
			`gmt_modified`
		from sys_macro
		where id = #{id}
	</select>

    <select id="queryList" resultType="com.platform.entity.SysMacroEntity">
        select
        `id`,
        `parent_id`,
        `name`,
        `value`,
        `status`,
        `type`,
        `order_num`,
        `remark`,
        `gmt_create`,
        `gmt_modified`
        from sys_macro
        WHERE 1=1
        <if test="type != null">
            AND type = #{type}
        </if>
        <choose>
            <when test="sidx != null and sidx.trim() != ''">
                order by ${sidx} ${order}
            </when>
            <otherwise>
                order by id desc
            </otherwise>
        </choose>
        <if test="offset != null and limit != null">
            limit #{offset}, #{limit}
        </if>
    </select>

    <select id="queryTotal" resultType="int">
        select count(*) from sys_macro
        WHERE 1=1
        <if test="type != null">
            AND type = #{type}
        </if>
    </select>

    <insert id="save" parameterType="com.platform.entity.SysMacroEntity" useGeneratedKeys="true" keyProperty="id">
		insert into sys_macro(
			parent_id,
			`name`,
			`value`,
			`status`,
			`type`,
			`order_num`,
			`remark`,
			`gmt_create`,
			`gmt_modified`)
		values(
			#{parentId},
			#{name},
			#{value},
			#{status},
			#{type},
			#{orderNum},
			#{remark},
			#{gmtCreate},
			#{gmtModified})
	</insert>

    <update id="update" parameterType="com.platform.entity.SysMacroEntity">
        update sys_macro
        <set>
            <if test="parentId != null">`parent_id` = #{parentId},</if>
            <if test="name != null">`name` = #{name},</if>
            <if test="value != null">`value` = #{value},</if>
            <if test="status != null">`status` = #{status},</if>
            <if test="type != null">`type` = #{type},</if>
            <if test="orderNum != null">`order_num` = #{orderNum},</if>
            <if test="remark != null">`remark` = #{remark},</if>
            <if test="gmtCreate != null">`gmt_create` = #{gmtCreate},</if>
            <if test="gmtModified != null">`gmt_modified` = #{gmtModified}</if>
        </set>
        where id = #{id}
    </update>

    <delete id="delete">
		delete from sys_macro where id = #{value}
	</delete>

    <delete id="deleteBatch">
        delete from sys_macro where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="queryMacrosByValue" resultType="com.platform.entity.SysMacroEntity">
        select
        `id`,
        `parent_id`,
        `name`,
        `value`,
        `status`,
        `type`,
        `order_num`,
        `remark`,
        `gmt_create`,
        `gmt_modified`
        from sys_macro
        WHERE 1=1
          AND exists(SELECT 1 from sys_macro m WHERE m.id = sys_macro.parent_id and m.value =#{value} )
          AND `type` = '1'
    </select>
</mapper>