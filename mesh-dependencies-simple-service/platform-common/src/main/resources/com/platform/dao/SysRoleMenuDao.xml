<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.platform.dao.SysRoleMenuDao">

	<insert id="save">
		insert into sys_role_menu
		(
			`role_id`, 
			`menu_id`
		)
		values
		<foreach collection="menuIdList" item="item" index="index" separator="," >
		(
			#{roleId}, 
			#{item} 
		)
		</foreach>
	</insert>
	
	<delete id="delete">
		delete from sys_role_menu where role_id = #{value}
	</delete>
	
	<select id="queryMenuIdList" resultType="long">
		select menu_id from sys_role_menu where role_id = #{value}
	</select>

</mapper>