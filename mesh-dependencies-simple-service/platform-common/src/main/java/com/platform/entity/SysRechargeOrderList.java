package com.platform.entity;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @program platform
 * @description 充值订单查询分页列表
 * @date 2019-09-18 15:50
 **/
@Data
public class SysRechargeOrderList {

    /**充值订单id*/
    private String orderId;
    /**用户id*/
    private String userId;
    /**微信用户openid*/
    private String openid;
    /**充值金额*/
    private int rechargeAmount;
    /**充值时间*/
    private int rechargeTime;
    /**充值内容*/
    private String rechargeContent;
    /**充值赠送的积分*/
    private int rechargeIntegral;
    /**支付时间*/
    private String PaymentTime;
    /**创建时间*/
    private Date createTime;
    /**用户名称*/
    private String username;
    /**用户昵称*/
    private String nickname;
    /**用户手机号*/
    private String mobile;
}
