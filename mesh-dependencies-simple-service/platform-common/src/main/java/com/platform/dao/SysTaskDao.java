package com.platform.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.platform.entity.SysTaskEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SysTaskDao {

    /**任务配置列表*/
    List<SysTaskEntity> list(Page<SysTaskEntity> page, @Param(value = "order") String order);
    /**查看任务内容详情*/
    SysTaskEntity info(String taskId);
    /**新增任务内容*/
    int save(SysTaskEntity sysTaskEntity);
    /**修改任务内容*/
    int update(SysTaskEntity sysTaskEntity);
    /**删除*/
    int delete(String taskId);
}
