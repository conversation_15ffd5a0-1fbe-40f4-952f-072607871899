package com.jmt.config.oss;
import com.jmt.common.util.OssBootUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OssBootConfiguration {

    @Value("${jmt.oss.endpoint}")
    private String endpoint;
    @Value("${jmt.oss.accessKey}")
    private String accessKeyId;
    @Value("${jmt.oss.secretKey}")
    private String accessKeySecret;
    @Value("${jmt.oss.bucketName}")
    private String bucketName;
    @Value("${jmt.oss.staticDomain}")
    private String staticDomain;


    @Bean
    public void initOssBootConfiguration() {
        OssBootUtil.setEndPoint(endpoint);
        OssBootUtil.setAccessKeyId(accessKeyId);
        OssBootUtil.setAccessKeySecret(accessKeySecret);
        OssBootUtil.setBucketName(bucketName);
        OssBootUtil.setStaticDomain(staticDomain);
    }
}