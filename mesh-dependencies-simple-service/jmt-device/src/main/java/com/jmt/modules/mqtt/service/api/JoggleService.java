package com.jmt.modules.mqtt.service.api;

import com.alibaba.fastjson.JSONObject;

public interface JoggleService {
    //同步时间
    JSONObject syncTime(JSONObject jsonObject);
    // 同步设备剩余电量
    JSONObject batteryValue(JSONObject jsonObject);
    //同步设备
    JSONObject cogradient(JSONObject jsonObject);
    //同步视频
    JSONObject synVideo(JSONObject jsonObject);
    //同步插播视频
    JSONObject synInVideo(JSONObject jsonObject);
    //升级
    JSONObject version(JSONObject jsonObject);
    //绑定设备
    JSONObject binding(JSONObject jsonObject);
    //播放日志接口
    JSONObject playLog(JSONObject jsonObject);
    //餐厅设备信息(开关机时间，静音时长，音量控制-全局，商家一号位视频）
    String bUserEqInfo(JSONObject jsonObject);
    //B端解除
    String untying(JSONObject jsonObject);
    //B端设备状态
    JSONObject queryEqInfo(JSONObject jsonObject);
    //同步部署订单
    JSONObject deployOrder(JSONObject jsonObject);
}
