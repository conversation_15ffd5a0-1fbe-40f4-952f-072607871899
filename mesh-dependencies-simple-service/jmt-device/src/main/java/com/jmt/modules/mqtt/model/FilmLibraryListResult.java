package com.jmt.modules.mqtt.model;

import com.jmt.modules.mqtt.entity.FilmLibrary;
import lombok.Data;
import java.util.Date;

/**
 * 工单列表查询结果
 */
@Data
public class FilmLibraryListResult extends FilmLibrary {

    /**投放人 广告工单中的客户名称*/
    private String customName;
    /**主键*/
    private String id;
    /**广告工单号*/
    private String jobNum;
    /**广告工单号*/
    private String adJobNum;
    /**广告合同编号*/
    private String contractNum;
    /**客户联系方式*/
    private String customPhone;
    /**客户联系人*/
    private String customLiaison;
    /**营业执照*/
    private String businessLicense;
    /**合同金额*/
    private String amountMoney;
    /**视频关联id 片源库id*/
    private Integer filmId;
    /**是否投放类型A 0 投放 1 不投放*/
    private int eqTypeA;
    /**是否投放类型B 0 投放 1 不投放*/
    private int eqTypeB;
    /**是否投放类型C 0 投放 1 不投放*/
    private int eqTypeC;
    /**创建人名字*/
    private String creatorName;
    /**创建人工号*/
    private String creatorNum;
    /**创建时间*/
    private Date createTime;
    /**0:新增未审批 1：主管审批 2：财务审批 3：审片员审批 4:审批通过 5：驳回 */
    private int status;
    private int filmStatus;
}
