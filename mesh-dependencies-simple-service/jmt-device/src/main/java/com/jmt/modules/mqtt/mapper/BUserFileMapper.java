package com.jmt.modules.mqtt.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jmt.modules.mqtt.entity.BUserFile;
import com.jmt.modules.mqtt.model.BUserFileList;
import com.jmt.modules.mqtt.model.FilmLibraryListQueryCriteria;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import java.util.Map;

public interface BUserFileMapper {

    /**
     * 查找商家的图片
     * bCode 商家编号
     * bFileType 类型 0:图片 1：视频
     */
    List<BUserFile> queryBUserFile(@Param("bCode") String bCode, @Param("bFileType") String bFileType);


    /**
     * 查找商家的图片
     * bCode 商家编号
     * bFileType 类型 0:图片 1：视频
     */
    List<BUserFile> queryBUserFiles(@Param("bCode") String bCode,@Param("bFileType") String bFileType);

    /**
     * 删除商家上传的视频/图片
     * id 视频/图片的id
     * @return
     */
    int deleteBUserVideo(@Param("id")String id);

    /**
     * @Description 商家上传文件
     * @param  bUserFile
     * 0图片 1文件
     * <AUTHOR>
     * @date 2019/8/26 0026
     * @return
     */
    int insertBUserVideo(BUserFile bUserFile);

    /**
     * @Description 查找文件
     * @param id 文件/视频、图片 id
     * <AUTHOR>
     * @date 2019/8/26 0026
     * @return
     */
    BUserFile queryUserFileByFileId(@Param("id")String id );


    //全网查询商户的第一个视频
    BUserFile querynetWorkFile(@Param("bCode") String bCode);


    /**
     * 描述: 商户文件列表
     * @method  listBUserFile
     * @date: 2020/4/2 0002
     * @author: hanshangrong
     * @param page
     * @param filmLibraryListQueryCriteria
     * @return List<BUserFileList>
     */
    List<BUserFileList>  listBUserFile(Page<BUserFileList> page, FilmLibraryListQueryCriteria filmLibraryListQueryCriteria);
    //更新视频
    int updateFileForVideo(Map<String,Object> map);

    //更新图片
    int updateFileForImg(Map<String,Object> map);
    //查询图片
    BUserFile queryFileForImg(@Param("imgType") String imgType,@Param("bCode") String bCode);

    /**
     * 描述:  通过商户编号和文件类型修改状态
     * @method: updateFileStatusByBcodeAndType
     * @author: HSR
     * @date: 2020/9/30
     * @param bCode 商户编号
     * @param status  状态
     * @param type  类型
     * @return: int
     * @exception:
    **/
    int updateFileStatusByBcodeAndType(@Param("bCode") String bCode,@Param("status") int status,@Param("type") int type);
}
