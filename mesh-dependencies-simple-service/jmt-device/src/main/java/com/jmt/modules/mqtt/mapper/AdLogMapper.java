package com.jmt.modules.mqtt.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jmt.modules.mqtt.entity.AdLog;
import com.jmt.modules.mqtt.model.AdLogListQueryCriteria;
import com.jmt.modules.mqtt.model.AdLogListResult;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface AdLogMapper extends BaseMapper<AdLog> {
    /**
     * 描述:  投放日志列表
     * @method  listAdLog
     * @date: 2020/4/8 0008
     * @author: hanshangrong
     * @param page
     * @param adLogListQueryCriteria
     * @return List<AdLogListResult>
     */
    List<AdLogListResult> listAdLog(Page<AdLogListResult> page, AdLogListQueryCriteria adLogListQueryCriteria);


    //新增日志
    void addLog(AdLog adLog);


    //更新日志
    void upAdlog(AdLog adLog);

    //查询
    AdLog queryAdlog(@Param("eqCode") String eqCode,@Param("bCode") String bCode,@Param("filmMd5") String filmMd5);


}