<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.modules.mqtt.mapper.BUserMapper">
    <resultMap id="bUser" type="com.jmt.modules.mqtt.entity.BUser">
        <result column="B_ID" property="bId"/>
        <result column="B_CODE" property="bCode"/>
        <result column="B_SHOP_NAME" property="bShopName"/>
        <result column="B_CONTACTS" property="bContacts"/>
        <result column="B_PHONE" property="bPhone"/>
        <result column="B_BUSINESS_LICENSE" property="bBusinessLicense"/>
        <result column="B_AREA_PROVINCE" property="bAreaProvince"/>
        <result column="B_AREA_CITY" property="bAreaCity"/>
        <result column="B_AREA_COUNTY" property="bAreaCounty"/>
        <result column="B_AREA_PROVINCE_NUM" property="bAreaProvinceNum"/>
        <result column="B_AREA_CITY_NUM" property="bAreaCityNum"/>
        <result column="B_AREA_COUNTY_NUM" property="bAreaCountyNum"/>
        <result column="B_ADDRESS" property="bAddress"/>
        <result column="B_TYPE" property="bType"/>
        <result column="B_AMOUNT_DESK" property="bAmountDesk"/>
        <result column="B_AMOUNT_DESK_Y" property="bAmountDeskY"/>
        <result column="B_AMOUNT_DESK_F" property="bAmountDeskF"/>
        <result column="B_MEASURE" property="bMeasure"/>
        <result column="B_START_LEVEL" property="bStartLevel"/>
        <result column="B_CUISINE" property="bCuisine"/>
        <result column="B_PARTNER_CODE" property="bPartnerCode"/>
        <result column="B_PARTNER_NAEM" property="bPartnerName"/>
        <result column="B_ATTRIBUTE" property="bAttribute"/>
        <result column="B_PERSON_CONSUMPTION" property="bPersonConsumption"/>
        <result column="B_VISITORS_FLOWRATE" property="bVisitorsFlowrate"/>
        <result column="B_VOLUME" property="bVolume"/>
        <result column="B_TIME_ONE" property="bTimeOne"/>
        <result column="B_TIME_TWO" property="bTimeTwo"/>
        <result column="B_TIME_THREE" property="bTimeThree"/>
        <result column="B_PAYMENT_INTEGRAL" property="bPaymentIntegral"/>
        <result column="B_EQ_TYPE_A_NUM" property="bEqTypeANum"/>
        <result column="B_EQ_TYPE_B_NUM" property="bEqTypeBNum"/>
        <result column="B_EQ_TYPE_C_NUM" property="bEqTypeCNum"/>
    </resultMap>
    <resultMap id="bUserListResult" type="com.jmt.modules.mqtt.model.BUserListResult">
        <result column="B_ID" property="bId"/>
        <result column="B_CODE" property="bCode"/>
        <result column="B_SHOP_NAME" property="bShopName"/>
        <result column="B_CONTACTS" property="bContacts"/>
        <result column="B_PHONE" property="bPhone"/>
        <result column="B_ADDRESS" property="bAddress"/>
        <result column="bArea" property="bArea"/>
        <result column="B_PARTNER_NAEM" property="bPartnerName"/>
        <result column="B_REGISTER_TIME" property="bRegisterTime"/>
        <result property="bEqTypeANum" column="B_EQ_TYPE_A_NUM"/>
        <result property="bEqTypeBNum" column="B_EQ_TYPE_B_NUM"/>
        <result property="bEqTypeCNum" column="B_EQ_TYPE_C_NUM"/>
        <result column="B_AREA_PROVINCE" property="bAreaProvince"/>
        <result column="B_AREA_CITY" property="bAreaCity"/>
        <result column="B_AREA_COUNTY" property="bAreaCounty"/>
        <result column="B_AREA_PROVINCE_NUM" property="bAreaProvinceNum"/>
        <result column="B_AREA_CITY_NUM" property="bAreaCityNum"/>
        <result column="B_AREA_COUNTY_NUM" property="bAreaCountyNum"/>
    </resultMap>

    <resultMap id="bUserResult" type="com.jmt.modules.mqtt.model.BUserResult">
        <result column="bArea" property="bArea"/>
        <result column="B_PARTNER_NAEM" property="bPartnerName"/>
    </resultMap>
    <select id="queryBuserInf" resultMap="bUser">
          select * from b_user where b_code=#{bCode}
    </select>

    <insert id="addUser" parameterType="com.jmt.modules.mqtt.entity.BUser" useGeneratedKeys="true"
            keyProperty="bId">
               insert into b_user (B_CODE,B_SHOP_NAME,B_CONTACTS,B_PHONE,B_BUSINESS_LICENSE,B_AREA_PROVINCE,B_AREA_CITY,B_AREA_COUNTY,B_AREA_PROVINCE_NUM,B_AREA_CITY_NUM,B_AREA_COUNTY_NUM,
               B_ADDRESS,B_TYPE,B_AMOUNT_DESK_Y,B_MEASURE,B_AMOUNT_DESK_F,B_START_LEVEL,B_CUISINE,B_PARTNER_CODE,B_PARTNER_NAEM,B_ATTRIBUTE,B_PERSON_CONSUMPTION,
               B_VISITORS_FLOWRATE,B_VOLUME,B_TIME_ONE,B_TIME_TWO,B_TIME_THREE,B_PAYMENT_INTEGRAL,B_PERSON_PHONE,B_MUTEVOLUME,B_MUTETIME,B_REGISTER_TIME) values
               (#{bCode},#{bShopName},#{bContacts},#{bPhone},#{bBusinessLicense},
               #{bAreaProvince},#{bAreaCity},#{bAreaCounty},#{bAreaProvinceNum},#{bAreaCityNum},#{bAreaCountyNum},
               #{bAddress},#{bType},#{bAmountDeskY},#{bMeasure},#{bAmountDeskF},#{bStartLevel},#{bCuisine},#{bPartnerCode},#{bPartnerName},
               #{bAttribute},#{bPersonConsumption},#{bVisitorsFlowrate},#{bVolume},#{bTimeOne},#{bTimeTwo},#{bTimeThree},#{bPaymentIntegral},
               #{bPersonPhone},#{bMuteVolume},#{bMuteTime},#{bRegisterTime}
               )
    </insert>


    <select id="queryUserNum" parameterType="com.jmt.modules.mqtt.model.IndexStatisticsQueryCriteria"
            resultType="int">
        select count(*) from b_user
        <where>
            <if test="areaProvinceNum!=null and areaProvinceNum!=''">
                and B_AREA_PROVINCE_NUM=#{areaProvinceNum}
            </if>
            <if test="areaCityNum !=null and areaCityNum!=''">
                and B_AREA_CITY_NUM=#{areaCityNum}
            </if>
            <if test="areaCountyNum!=null and areaCountyNum!=''">
                and B_AREA_COUNTY_NUM=#{areaCountyNum}
            </if>
        </where>
    </select>

    <select id="queryUserNums" resultType="int" parameterType="map">
        select count(*) from b_user
        <where>
            <if test="areaProvinceNum!=null">
                and B_AREA_PROVINCE_NUM=#{areaProvinceNum}
            </if>
            <if test="areaCityNum !=null">
                and B_AREA_CITY_NUM=#{areaCityNum}
            </if>
            <if test="areaCountyNum!=null">
                and B_AREA_COUNTY_NUM=#{areaCountyNum}
            </if>
        </where>
    </select>
    <!--????????-->
    <update id="updateInfo" parameterType="com.jmt.modules.mqtt.entity.BUser">
        update b_user
        <set>
            <if test="bShopName!=null ">
                B_SHOP_NAME=#{bShopName},
            </if>
            <if test="bContacts!=null ">
                B_CONTACTS=#{bContacts},
            </if>
            <if test="bPhone!=null ">
                B_PHONE=#{bPhone},
            </if>
            <if test="bBusinessLicense !=null ">
                B_BUSINESS_LICENSE=#{bBusinessLicense},
            </if>
            <if test="bAreaProvince !=null ">
                B_AREA_PROVINCE=#{bAreaProvince},
            </if>
            <if test="bAreaCity !=null">
                B_AREA_CITY=#{bAreaCity},
            </if>
            <if test="bAreaCounty !=null ">
                B_AREA_COUNTY=#{bAreaCounty},
            </if>
            <if test="bAreaProvinceNum !=null and bAreaProvinceNum !=0 ">
                B_AREA_PROVINCE_NUM=#{bAreaProvinceNum},
            </if>
            <if test="bAreaCityNum !=null and bAreaCityNum !=0 ">
                B_AREA_CITY_NUM=#{bAreaCityNum},
            </if>
            <if test="bAreaCountyNum !=null and bAreaCountyNum !=0">
                B_AREA_COUNTY_NUM=#{bAreaCountyNum},
            </if>
            <if test="bAddress !=null ">
                B_ADDRESS=#{bAddress},
            </if>
            <if test="bType!=null">
                B_TYPE=#{bType},
            </if>
            <if test="bAmountDeskY!=null ">
                B_AMOUNT_DESK_Y=#{bAmountDeskY},
            </if>
            <if test="bMeasure!=null ">
                B_MEASURE=#{bMeasure},
            </if>
            <if test="bAmountDeskF!=null ">
                B_AMOUNT_DESK_F=#{bAmountDeskF},
            </if>
            <if test="bAmountDesk!=null ">
                B_AMOUNT_DESK=#{bAmountDesk},
            </if>
            <if test="bEqTypeANum!=null ">
                B_EQ_TYPE_A_NUM=#{bEqTypeANum},
            </if>
            <if test="bEqTypeBNum!=null ">
                B_EQ_TYPE_B_NUM=#{bEqTypeBNum},
            </if>
            <if test="bEqTypeCNum!=null ">
                B_EQ_TYPE_C_NUM=#{bEqTypeCNum},
            </if>
            <if test="bStartLevel!=null ">
                B_START_LEVEL=#{bStartLevel},
            </if>
            <if test="bCuisine!=null ">
                B_CUISINE=#{bCuisine},
            </if>
            <if test="bPartnerCode!=null ">
                B_PARTNER_CODE=#{bPartnerCode},
            </if>
            <if test="bPartnerName!=null ">
                B_PARTNER_NAEM=#{bPartnerName},
            </if>
            <if test="bAttribute!=null ">
                B_ATTRIBUTE=#{bAttribute},
            </if>
            <if test="bPersonConsumption!=null ">
                B_PERSON_CONSUMPTION=#{bPersonConsumption},
            </if>
            <if test="bVisitorsFlowrate !=null ">
                B_VISITORS_FLOWRATE=#{bVisitorsFlowrate},
            </if>
            <if test="bPaymentIntegral !=null ">
                B_PAYMENT_INTEGRAL=#{bPaymentIntegral},
            </if>
            <if test="bPersonPhone !=null ">
                B_PERSON_PHONE=#{bPersonPhone},
            </if>
            <if test="bRegisterTime !=null ">
                B_REGISTER_TIME=#{bRegisterTime},
            </if>
        </set>
        <where>
            B_CODE =#{bCode}
        </where>
    </update>


    <update id="updateUserInfo" parameterType="map">
        update b_user
        <set>
            <if test="bVolume !=null and bVolume !=''">
                B_VOLUME = #{bVolume},
            </if>
            <if test="bMuteVolume !=null and bMuteVolume !=''">
                B_MUTEVOLUME = #{bMuteVolume},
            </if>
            <if test="bMuteTime !=null and bMuteTime !=''">
                B_MUTETIME =#{bMuteTime},
            </if>
            <if test="bTimeOne !=null and bTimeOne !=''">
                B_TIME_ONE =#{bTimeOne},
            </if>
            <if test="bTimeTwo !=null and bTimeTwo !=''">
                B_TIME_TWO = #{bTimeTwo},
            </if>
            <if test="bTimeThree !=null and bTimeThree !=''">
                B_TIME_THREE =#{bTimeThree},
            </if>
            <if test="A !=null and A !=''">
                B_EQ_TYPE_A_NUM =#{A},
            </if>
            <if test="B !=null and B !=''">
                B_EQ_TYPE_B_NUM = #{B},
            </if>
            <if test="C !=null and C !=''">
                B_EQ_TYPE_C_NUM =#{C}
            </if>
        </set>
        <where>
            B_CODE = #{bCode}
        </where>
    </update>


    <select id="queryLikeLimit" resultMap="bUserListResult" parameterType="map">
        SELECT B_ID,concat_ws('/',B_AREA_PROVINCE,B_AREA_CITY,B_AREA_COUNTY) as bArea,
        B_CODE,B_SHOP_NAME,B_CONTACTS,B_PHONE,B_ADDRESS,B_PARTNER_NAEM,B_REGISTER_TIME,
        B_EQ_TYPE_A_NUM, B_EQ_TYPE_B_NUM,B_EQ_TYPE_C_NUM,B_AREA_PROVINCE,B_AREA_CITY,B_AREA_COUNTY,
        B_AREA_PROVINCE_NUM,B_AREA_CITY_NUM,B_AREA_COUNTY_NUM
        FROM b_user
        <where>
            <if test="map.provinceNum !=null and map.provinceNum != ''">
                and B_AREA_PROVINCE_NUM =#{map.provinceNum}
            </if>
            <if test="map.cityNum !=null and map.cityNum != ''">
                and B_AREA_CITY_NUM = #{map.cityNum}
            </if>
            <if test="map.countyNum !=null and map.countyNum != ''">
                and B_AREA_COUNTY_NUM = #{map.countyNum}
            </if>
            <if test="map.bCode !=null and map.bCode != ''">
                and (B_CODE like concat('%',#{map.bCode},'%') or
                B_SHOP_NAME like concat('%',#{map.bCode},'%') or
                B_PARTNER_NAEM like concat('%',#{map.bCode},'%') or
                B_CONTACTS like concat('%',#{map.bCode},'%')
                )
            </if>
            <if test="map.startDate !=null and map.startDate !=''">
                and B_REGISTER_TIME &gt;=CONCAT(#{map.startDate},' 00:00:00')
            </if>
            <if test="map.sendDate !=null and map.sendDate !=''">
                and B_REGISTER_TIME &lt;=CONCAT(#{map.sendDate},' 23:59:59')
            </if>
        </where>
    </select>

    <select id="queryLikeLimits" resultMap="bUserListResult">
        SELECT B_ID,CONCAT_WS('/',B_AREA_PROVINCE,B_AREA_CITY,B_AREA_COUNTY) AS bArea,
        B_CODE,B_SHOP_NAME,B_CONTACTS,B_PHONE,B_ADDRESS,B_PARTNER_NAEM,B_REGISTER_TIME,
        (SELECT COUNT(*) FROM eq_all_list WHERE B_CODE = t1.B_CODE AND EQ_TYPE = '1' ) AS B_EQ_TYPE_A_NUM,
        (SELECT COUNT(*) FROM eq_all_list WHERE B_CODE = t1.B_CODE AND EQ_TYPE = '2' ) AS B_EQ_TYPE_B_NUM,
        (SELECT COUNT(*) FROM eq_all_list WHERE B_CODE = t1.B_CODE AND EQ_TYPE = '3' ) AS B_EQ_TYPE_C_NUM,
        B_AREA_PROVINCE,
        B_AREA_CITY,
        B_AREA_COUNTY,
        B_AREA_PROVINCE_NUM,
        B_AREA_CITY_NUM,
        B_AREA_COUNTY_NUM
        FROM b_user t1
        <where>
            <if test="provinceNum !=null and provinceNum != ''">
                and t1.B_AREA_PROVINCE_NUM =#{provinceNum}
            </if>
            <if test="cityNum !=null and cityNum != ''">
                and t1.B_AREA_CITY_NUM = #{cityNum}
            </if>
            <if test="countyNum !=null and countyNum != ''">
                and t1.B_AREA_COUNTY_NUM = #{countyNum}
            </if>
            <if test="bCode !=null and bCode != ''">
                and t1.B_CODE=#{bCode}
            </if>
        </where>
    </select>


    <select id="queryBUserByBCode" resultMap="bUserResult">

                SELECT *,concat_ws('',B_AREA_PROVINCE,B_AREA_CITY,B_AREA_COUNTY,B_ADDRESS) as bArea
                FROM b_user where B_CODE = #{bCode}

    </select>

    <select id="queryPendingEquipment" resultType="integer" parameterType="map">
        SELECT IFNULL(SUM(IFNULL(mEqNumA,0)+IFNULL(mEqNumB,0)+IFNULL(mEqNumB,0)),0) FROM b_user u,b_work_deployorder d
        WHERE u.B_CODE = d.mCodeName AND stauts = 0
        <if test="areaProvinceNum!=null">
            and B_AREA_PROVINCE_NUM=#{areaProvinceNum}
        </if>
        <if test="areaCityNum !=null">
            and B_AREA_CITY_NUM=#{areaCityNum}
        </if>
        <if test="areaCountyNum!=null">
            and B_AREA_COUNTY_NUM=#{areaCountyNum}
        </if>
    </select>
</mapper>