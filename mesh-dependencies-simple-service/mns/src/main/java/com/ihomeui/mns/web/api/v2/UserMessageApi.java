package com.ihomeui.mns.web.api.v2;

import com.ihomeui.mesh.criteria.CriteriaParser;
import com.ihomeui.mesh.utils.DTOUtils;
import com.ihomeui.mesh.utils.JsonUtil;
import com.ihomeui.mesh.utils.StringUtils;
import com.ihomeui.mns.MnsAppHelper;
import com.ihomeui.mns.client.base.BaseRemoteService;
import com.ihomeui.mns.client.base.DictConstants;
import com.ihomeui.mns.client.base.SettingConstants;
import com.ihomeui.mns.client.uaa.AppNavModuleInfo;
import com.ihomeui.mns.client.uaa.UAARemoteService;
import com.ihomeui.mns.domain.Message;
import com.ihomeui.mns.domain.UserMessage;
import com.ihomeui.mns.service.UserMessageBoardService;
import com.ihomeui.mns.service.UserMessageService;
import com.ihomeui.mns.web.api.v2.vm.UserMessageDetailVM;
import com.ihomeui.mns.web.api.v2.vm.UserMessageVM;
import com.ihomeui.mns.web.api.v2.vm.UserMessageVMForTemplateJs;
import com.ihomeui.mesh.web.utils.PaginationUtil;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.inject.Inject;
import javax.persistence.criteria.Predicate;
import java.net.URISyntaxException;
import java.time.*;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by Administrator on 2017/7/10.
 */
@RestController
@RequestMapping("/api/2.0")
public class UserMessageApi {

    private final Logger log = LoggerFactory.getLogger(UserMessageApi.class);

    @Inject
    private UserMessageService userMessageService;

    @Inject
    private UserMessageBoardService userMessageBoardService;

    @Inject
    private MnsAppHelper mnsAppHelper;

    @Inject
    private UAARemoteService uaaRemoteService;

    @Inject
    private BaseRemoteService baseRemoteService;

    @GetMapping("/_common/_my/messages")
    @Timed
    public ResponseEntity<List<UserMessageVM>> getMyMessages(
        @ApiParam(value="应用名") @RequestParam(required = false) String appName,
        @ApiParam(value="消息分类") @RequestParam(required = false) String kinds,
        @RequestParam(required = false) String relatedModule,
        @RequestParam(required = false) String query,
        @RequestParam(required = false) Long communityGroupId,
        @RequestParam(required = false) Long communityId,
        @ApiParam(value="指定是否是读取未读消息（默认为false）。")
        @RequestParam(required = false , defaultValue = "false" ) Boolean onlyNewMessage,
        @PageableDefault(size = 100,sort = {"sendTime"}, direction = Sort.Direction.DESC, value = 100)
        @ApiParam Pageable pageable
    ) throws URISyntaxException {
        Map<String, AppNavModuleInfo> appNavMaps = StringUtils.isBlank(appName)? null : uaaRemoteService.getAppNavMap(appName);

        Page<UserMessageVM> page = userMessageService.findAll((root, cq, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            if (query!=null){
                CriteriaParser parser = new CriteriaParser(query, "templateCode,title,content,remark");
                Predicate predicate = parser.toPredicate(root,cq,cb);
                if ( predicate != null){
                    predicates.add(predicate);
                }
            }
            if ( communityGroupId != null ) {
                predicates.add(
                    cb.or(
                        cb.isNull(root.get("communityGroupId")),
                        cb.equal(root.get("communityGroupId"),communityGroupId)
                    )
                );
            }
            if ( communityId != null ) {
                predicates.add(
                    cb.or(
                        cb.isNull(root.get("communityId")),
                        cb.equal(root.get("communityId"),communityId)
                    )
                );
            }
            predicates.add(cb.equal(root.get("userId"),mnsAppHelper.getCurrentUserId()));
            if ( kinds != null ) {
                predicates.add(root.get("kind").in(Arrays.asList(
                    kinds.split(",")
                )));
            }
            if ( relatedModule != null ) {
                predicates.add(cb.equal(root.get("message").get("relatedModule"),relatedModule));
            }
            if ( appName != null ) {
                predicates.add(cb.like(root.get("message").get("targetAppNames"),"%" + appName + "%"));
            }
            if (onlyNewMessage == null || onlyNewMessage) {
                predicates.add(cb.equal(root.get("readOver"),false));
            }
            return cb.and(predicates.toArray(new Predicate[0]));
        }, pageable).map(userMessage -> {
            UserMessageVM vm = new UserMessageVM();
            Message message = userMessage.getMessage();
            BeanUtils.copyProperties(message, vm);
            BeanUtils.copyProperties(userMessage, vm);
            vm.setDoneUserName(uaaRemoteService.getUserNameById(vm.getDoneUserId()));
            if(appNavMaps != null) {
                AppNavModuleInfo appNav = appNavMaps.get(vm.getRelatedModule());
                if (appNav != null) {
                    vm.setRelatedModuleLabel(appNav.getLabel());
                    vm.setRelatedModuleIcon(appNav.getIconPhotoForMessage());
                    vm.setRelatedModuleActionUri(appNav.getAction());
                    vm.setIconPhotoForMessage(appNav.getIconPhotoForMessage());
                }
            }
            return vm;
        });

        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/2.0/_common/_my/messages");
        return new ResponseEntity<>(page.getContent(), headers, HttpStatus.OK);
    }

    @PostMapping("/_common/_my/messages/{ids}/{action}")
    @Timed
    public ResponseEntity<Void> readMyMessages(@ApiParam(value = "以逗号分隔的用户消息ID")
                                               @PathVariable("ids") String ids,
                                               @ApiParam(value = "更改个人消息状态，参数可选值：READ=已读,DONE=完成")
                                               @PathVariable("action") String action)
    {
        Long userId = mnsAppHelper.getCurrentUserId();
        if(userId == null || StringUtils.isBlank(ids)) return new ResponseEntity<>(HttpStatus.NOT_FOUND);

        Long[] idArray = StringUtils.splitIgnoreError(ids,",", Long.class);
        if(action.equalsIgnoreCase("READ")) {
            for (Long id : idArray) userMessageService.messageRead(userId, id);
        }else if(action.equalsIgnoreCase("DONE")){
            for (Long id : idArray) userMessageService.messageDone(userId, id);
        }else{
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        }
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @GetMapping("/_common/_my/message-boards")
    @Timed
    public ResponseEntity<List<UserMessageVM>> getMessageBoards(
        @ApiParam(value="应用名", required = true)
        @RequestParam String appName,
        @RequestParam(required = false) Long communityGroupId,
        @RequestParam(required = false) Long communityId,
        @ApiParam(value="消息分类")
        @RequestParam(required = false) String kinds,
        @ApiParam(value="指定我的消息动态板块名（消息看板）。")
        @RequestParam(required = false) String boards,
        @ApiParam(value="获取自指定时间之后发送的消息（时间格式YYYY-MM-dd,如：2017-08-01）")
        @RequestParam(required = false) LocalDate timestamp,
        @ApiParam(value="指定是否是读取未读消息（默认为false）。")
        @RequestParam(required = false,defaultValue = "false") Boolean onlyNewMessage,
        @PageableDefault(size = 100,sort = {"sendTime"}, direction = Sort.Direction.DESC, value = 20)
        @ApiParam Pageable pageable
    ) throws URISyntaxException {
        Map<String, AppNavModuleInfo> appNavMaps = uaaRemoteService.getAppNavMap(appName);

        LocalDateTime fromTime = null;
        if ( timestamp != null ) {
            fromTime = timestamp.atStartOfDay();
        }
        Page<UserMessageVM> page = userMessageBoardService.findAll(communityGroupId, communityId, mnsAppHelper.getCurrentUserId(), appName, kinds, boards, fromTime, onlyNewMessage, null, pageable)
            .map(board -> {
                UserMessageVM vm = new UserMessageVM();
                Message message = board.getMessage();
                UserMessage userMessage = board.getUserMessage();
                BeanUtils.copyProperties(message, vm);
                BeanUtils.copyProperties(userMessage, vm);
                BeanUtils.copyProperties(board, vm);
                vm.setMessageId(message.getId());
                vm.setUserMessageId(userMessage.getId());
                vm.setBoard(board.getBoard());
                vm.setBoardName(baseRemoteService.getDictItemText(DictConstants.Names.USER_MESSAGE_BOARD, board.getBoard()));
                vm.setDoneUserName(uaaRemoteService.getUserNameById(vm.getDoneUserId()));
                AppNavModuleInfo appNav = appNavMaps.get(vm.getRelatedModule());
                if(appNav != null){
                    vm.setRelatedModuleLabel(appNav.getLabel());
                    vm.setRelatedModuleIcon(appNav.getIconPhotoForMessage());
                    vm.setRelatedModuleActionUri(appNav.getAction());
                    vm.setIconPhotoForMessage(appNav.getIconPhotoForMessage());
                }
                return vm;
            }
        );
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/2.0/_common/_my/message-boards");
        return new ResponseEntity<>(page.getContent(), headers, HttpStatus.OK);
    }

    @GetMapping("/_common/_my/message-boards/{userMessageId}/detail")
    @Timed
    public ResponseEntity<UserMessageDetailVM> getMessageBoardsDetail(
        @PathVariable Long userMessageId,
        @RequestParam(defaultValue = "OTHER") String board
    ){
        return Optional.ofNullable(userMessageBoardService.findMyOneByUserMessageIdAndBoard(userMessageId,board,mnsAppHelper.getCurrentUserId()))
            .map(messageBoard -> {
                UserMessageDetailVM vm = new UserMessageDetailVM();
                Message message = messageBoard.getMessage();
                UserMessage userMessage = messageBoard.getUserMessage();
                BeanUtils.copyProperties(message, vm);
                BeanUtils.copyProperties(userMessage, vm);
                BeanUtils.copyProperties(messageBoard, vm);
                vm.setMessageId(message.getId());
                vm.setUserMessageId(userMessage.getId());
                vm.setBoard(messageBoard.getBoard());
                vm.setBoardName(baseRemoteService.getDictItemText(DictConstants.Names.USER_MESSAGE_BOARD, messageBoard.getBoard()));
                vm.setDoneUserName(uaaRemoteService.getUserNameById(vm.getDoneUserId()));

                Map<String, AppNavModuleInfo> appNavMap = uaaRemoteService.getAppNavMap(message.getTargetAppNames());
                AppNavModuleInfo resourceTreeNode = appNavMap.get(message.getRelatedModule());
                if ( resourceTreeNode != null ){
                    vm.setRelatedModuleLabel(resourceTreeNode.getLabel());
                    vm.setRelatedModuleIcon(resourceTreeNode.getIconPhotoForMessage());
                    vm.setIconPhotoForMessage(resourceTreeNode.getIconPhotoForMessage());
                }
                userMessageService.messageRead(mnsAppHelper.getCurrentUserId(), userMessage.getId());
                return ResponseEntity.ok(vm);
            }).orElse(new ResponseEntity<>(HttpStatus.NOT_FOUND));
    }

    /**
     * @see UserMessageApi#getMyUserMessageDetailByMessageId
     * @param messageId
     * @return
     */
    @GetMapping("/_common/_my/user-messages/detail-by-message-id")
    @Timed
    @Deprecated
    public ResponseEntity<UserMessageDetailVM> getMyUserMessageDetailByMessageId_old(
        @RequestParam Long messageId
    ){
        return getMyUserMessageDetailByMessageId(messageId);
    }

    @GetMapping("/_common/_my/messages/{messageId}/detail")
    @Timed
    public ResponseEntity<UserMessageDetailVM> getMyUserMessageDetailByMessageId(
        @PathVariable Long messageId
    ){
        return Optional.ofNullable(userMessageService.findOneByMessageIdAndUserId(messageId,mnsAppHelper.getCurrentUserId()))
            .map(this::getUserMessageDetailVMResponseEntity)
            .orElse(new ResponseEntity<>(HttpStatus.NOT_FOUND));
    }
    @GetMapping("/_common/_my/user-messages/{userMessageId}/detail")
    @Timed
    public ResponseEntity<UserMessageDetailVM> getMessageBoardsDetail(
        @PathVariable Long userMessageId
    ){
        return Optional.ofNullable(userMessageService.findMyOne(userMessageId,mnsAppHelper.getCurrentUserId()))
            .map(this::getUserMessageDetailVMResponseEntity)
            .orElse(new ResponseEntity<>(HttpStatus.NOT_FOUND));
    }

    private ResponseEntity<UserMessageDetailVM> getUserMessageDetailVMResponseEntity(UserMessage userMessage) {
        UserMessageDetailVM vm = new UserMessageDetailVM();
        Message message = userMessage.getMessage();
        BeanUtils.copyProperties(message, vm);
        BeanUtils.copyProperties(userMessage, vm);
        vm.setMessageId(message.getId());
        vm.setUserMessageId(userMessage.getId());
        vm.setDoneUserName(uaaRemoteService.getUserNameById(vm.getDoneUserId()));
        Map<String, AppNavModuleInfo> appNavMap = uaaRemoteService.getAppNavMap(message.getTargetAppNames());

        AppNavModuleInfo resourceTreeNode = appNavMap.get(message.getRelatedModule());
        if ( resourceTreeNode != null ){
            vm.setRelatedModuleLabel(resourceTreeNode.getLabel());
            vm.setRelatedModuleIcon(resourceTreeNode.getIconPhotoForMessage());
            vm.setIconPhotoForMessage(resourceTreeNode.getIconPhotoForMessage());
        }
        userMessageService.messageRead(mnsAppHelper.getCurrentUserId(), userMessage.getId());
        return ResponseEntity.ok(vm);
    }

    @GetMapping("/_common/_my/messages/{messageId}/detail-for-template-js")
    @Timed
    public ResponseEntity<UserMessageVMForTemplateJs> getUserMessageForTempalteJsByMessageId(
        @PathVariable Long messageId
    ){
        return Optional.ofNullable(userMessageService.findOneByMessageIdAndUserId(messageId,mnsAppHelper.getCurrentUserId()))
            .map(this::toUserMessageVMForTemplateJsResponseEntity)
            .orElse(new ResponseEntity<>(HttpStatus.NOT_FOUND));
    }

    @GetMapping("/_common/_my/user-messages/{userMessageId}/detail-for-template-js")
    @Timed
    public ResponseEntity<UserMessageVMForTemplateJs> getUserMessageForTempalteJs(
        @PathVariable Long userMessageId
    ){
        return Optional.ofNullable(userMessageService.findMyOne(userMessageId,mnsAppHelper.getCurrentUserId()))
            .map(this::toUserMessageVMForTemplateJsResponseEntity)
            .orElse(new ResponseEntity<>(HttpStatus.NOT_FOUND));
    }

    private ResponseEntity<UserMessageVMForTemplateJs> toUserMessageVMForTemplateJsResponseEntity(UserMessage userMessage){
        Message message = userMessage.getMessage();
        UserMessageVMForTemplateJs dto = new UserMessageVMForTemplateJs();
        DTOUtils.copyFields(message, dto);
        DTOUtils.copyFields(userMessage, dto);
        dto.setSenderName(uaaRemoteService.getUserNameById(message.getSenderId()));
        dto.setCommunityName(uaaRemoteService.getOfficeName(message.getCommunityId()));
        dto.setCommunityGroupName(uaaRemoteService.getOfficeName(message.getCommunityGroupId()));
        dto.setDoneUserName(uaaRemoteService.getUserNameById(dto.getDoneUserId()));
        Map<String, AppNavModuleInfo> appNavMap = uaaRemoteService.getAppNavMap(message.getTargetAppNames());

        AppNavModuleInfo resourceTreeNode = appNavMap.get(message.getRelatedModule());
        if ( resourceTreeNode != null ){
            dto.setRelatedModuleLabel(resourceTreeNode.getLabel());
            dto.setRelatedModuleIcon(resourceTreeNode.getIconPhotoForMessage());
            dto.setIconPhotoForMessage(resourceTreeNode.getIconPhotoForMessage());
        }

        try{dto.setRelatedData(JsonUtil.jsonToMap(message.getRelatedDataJson()));}catch(Exception ex){/**/}
        String detailPageTpl = message.getTemplate().getDetailPageTemplate();
        if(StringUtils.isBlank(detailPageTpl))
            detailPageTpl = baseRemoteService.getTheBestSettingValue(message.getCommunityGroupId(), message.getCommunityId(), SettingConstants.DETAIL_PAGE_DEFAULT_TEMPLATE);
        dto.setDetailPageTemplate(detailPageTpl);

        return new ResponseEntity<>(dto, HttpStatus.OK);
    }

    @GetMapping("/_common/_my/home-messages")
    @Timed
    public ResponseEntity<List<UserMessageVM>> getAppHomeMessages(
        @ApiParam(value="应用名", required = true)
        @RequestParam String appName,
        @RequestParam(required = false) Long communityGroupId,
        @ApiParam(value="仅获取几天之内的动态信息（默认3天）")
        @RequestParam(required = false) Integer inDays,
        @ApiParam(value="最多获取多少条动态信息（默认10条）")
        @RequestParam(required = false) Integer maxRows
    ) {
        Long userId = mnsAppHelper.getCurrentUserId();
        if(userId == null) return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        Map<String, AppNavModuleInfo> appNavMaps = uaaRemoteService.getAppNavMap(appName);

        List<UserMessageVM> result = userMessageBoardService
            .findMessagesByModuleLastOne(communityGroupId, userId, appName, inDays == null ? 3 : inDays, maxRows==null ? 10 : maxRows)
            .stream().map(board -> {
                UserMessageVM vm = new UserMessageVM();
                Message message = board.getMessage();
                UserMessage userMessage = board.getUserMessage();
                BeanUtils.copyProperties(message, vm);
                BeanUtils.copyProperties(userMessage, vm);
                BeanUtils.copyProperties(board, vm);
                vm.setDoneUserName(uaaRemoteService.getUserNameById(vm.getDoneUserId()));
                AppNavModuleInfo appNav = appNavMaps.get(vm.getRelatedModule());
                if(appNav != null){
                    vm.setRelatedModuleLabel(appNav.getLabel());
                    vm.setRelatedModuleIcon(appNav.getIconPhotoForMessage());
                    vm.setRelatedModuleActionUri(appNav.getAction());
                    vm.setIconPhotoForMessage(appNav.getIconPhotoForMessage());
                }
                return vm;
            }).collect(Collectors.toList());

        return new ResponseEntity<>(result, HttpStatus.OK);
    }
}
