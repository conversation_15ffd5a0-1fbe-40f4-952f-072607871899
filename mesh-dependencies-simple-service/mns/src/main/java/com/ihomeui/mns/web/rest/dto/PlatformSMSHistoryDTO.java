package com.ihomeui.mns.web.rest.dto;

import java.time.LocalDateTime;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.Objects;


/**
 * A DTO for the PlatformSMSHistory entity.
 */
public class PlatformSMSHistoryDTO implements Serializable {

    private Long id;

    @NotNull
    private LocalDateTime sendTime;

    @NotNull
    @Size(max = 20)
    private String targetPhone;

    @NotNull
    @Size(max = 800)
    private String content;

    @NotNull
    private Boolean isSuccess;

    @NotNull
    @Size(max = 200)
    private String responseMessage;

    private Long accountId;

    private String templateCode;

    private String templateName;

    private String targetUserName;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    public LocalDateTime getSendTime() {
        return sendTime;
    }

    public void setSendTime(LocalDateTime sendTime) {
        this.sendTime = sendTime;
    }
    public String getTargetPhone() {
        return targetPhone;
    }

    public void setTargetPhone(String targetPhone) {
        this.targetPhone = targetPhone;
    }
    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
    public Boolean getIsSuccess() {
        return isSuccess;
    }

    public void setIsSuccess(Boolean isSuccess) {
        this.isSuccess = isSuccess;
    }
    public String getResponseMessage() {
        return responseMessage;
    }

    public void setResponseMessage(String responseMessage) {
        this.responseMessage = responseMessage;
    }

    public Long getAccountId() {
        return accountId;
    }

    public void setAccountId(Long platformSMSAccountId) {
        this.accountId = platformSMSAccountId;
    }

    public String getTemplateName() {
        return templateName;
    }

    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    public String getTargetUserName() {
        return targetUserName;
    }

    public void setTargetUserName(String targetUserName) {
        this.targetUserName = targetUserName;
    }

    public String getTemplateCode() {
        return templateCode;
    }

    public void setTemplateCode(String templateCode) {
        this.templateCode = templateCode;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        PlatformSMSHistoryDTO platformSMSHistoryDTO = (PlatformSMSHistoryDTO) o;

        if ( ! Objects.equals(id, platformSMSHistoryDTO.id)) return false;

        return true;
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(id);
    }

    @Override
    public String toString() {
        return "PlatformSMSHistoryDTO{" +
            "id=" + id +
            ", sendTime='" + sendTime + "'" +
            ", targetPhone='" + targetPhone + "'" +
            ", content='" + content + "'" +
            ", isSuccess='" + isSuccess + "'" +
            ", responseMessage='" + responseMessage + "'" +
            '}';
    }
}
