package com.ihomeui.mns.service.impl;

import com.ihomeui.mns.service.SmsHistoryService;
import com.ihomeui.mns.domain.SmsHistory;
import com.ihomeui.mns.repository.SmsHistoryRepository;
import com.ihomeui.mns.service.dto.SmsHistoryDTO;
import com.ihomeui.mns.service.mapper.SmsHistoryMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * Service Implementation for managing SmsHistory.
 */
@Service
@Transactional
public class SmsHistoryServiceImpl implements SmsHistoryService {

    private final Logger log = LoggerFactory.getLogger(SmsHistoryServiceImpl.class);

    private final SmsHistoryRepository smsHistoryRepository;

    private final SmsHistoryMapper smsHistoryMapper;

    public SmsHistoryServiceImpl(SmsHistoryRepository smsHistoryRepository, SmsHistoryMapper smsHistoryMapper) {
        this.smsHistoryRepository = smsHistoryRepository;
        this.smsHistoryMapper = smsHistoryMapper;
    }

    /**
     * Save a smsHistory.
     *
     * @param smsHistoryDTO the entity to save
     * @return the persisted entity
     */
    @Override
    public SmsHistoryDTO save(SmsHistoryDTO smsHistoryDTO) {
        log.debug("Request to save SmsHistory : {}", smsHistoryDTO);
        SmsHistory smsHistory = smsHistoryMapper.toEntity(smsHistoryDTO);
        smsHistory = smsHistoryRepository.save(smsHistory);
        return smsHistoryMapper.toDto(smsHistory);
    }

    /**
     * Get all the smsHistories.
     *
     * @param pageable the pagination information
     * @return the list of entities
     */
    @Override
    @Transactional(readOnly = true)
    public Page<SmsHistoryDTO> findAll(Pageable pageable) {
        log.debug("Request to get all SmsHistories");
        return smsHistoryRepository.findAll(pageable)
            .map(smsHistoryMapper::toDto);
    }
    @Override
    @Transactional(readOnly = true)
    public Page<SmsHistoryDTO> findAll(Specification<SmsHistory> spec, Pageable pageable) {
        log.debug("Request to get all SmsHistories");
        return smsHistoryRepository.findAll(spec, pageable)
            .map(smsHistoryMapper::toDto);
    }


    /**
     * Get one smsHistory by id.
     *
     * @param id the id of the entity
     * @return the entity
     */
    @Override
    @Transactional(readOnly = true)
    public Optional<SmsHistoryDTO> findOne(Long id) {
        log.debug("Request to get SmsHistory : {}", id);
        return smsHistoryRepository.findById(id)
            .map(smsHistoryMapper::toDto);
    }

    /**
     * Delete the smsHistory by id.
     *
     * @param id the id of the entity
     */
    @Override
    public void delete(Long id) {
        log.debug("Request to delete SmsHistory : {}", id);
        smsHistoryRepository.deleteById(id);
    }
}
