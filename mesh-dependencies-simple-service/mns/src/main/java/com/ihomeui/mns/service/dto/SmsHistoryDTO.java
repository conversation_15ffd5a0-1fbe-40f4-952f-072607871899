package com.ihomeui.mns.service.dto;

import java.time.ZonedDateTime;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.Objects;

/**
 * A DTO for the SmsHistory entity.
 */
public class SmsHistoryDTO implements Serializable {

    private Long id;

    private Long communityGroupId;

    private Long communityId;

    private Long messageId;

    private Long messageTemplateId;

    @Size(max = 50)
    private String receiverName;

    @Size(max = 50)
    private String mobile;

    @Size(max = 200)
    private String content;

    @Size(max = 100)
    private String smsTemplateCode;

    @Size(max = 200)
    private String smsTemplateParam;

    @Size(max = 50)
    private Integer alarmLevel;

    private ZonedDateTime sendTime;

    private Boolean sendSuccess;

    private String errorMessage;

    private String serviceName;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCommunityGroupId() {
        return communityGroupId;
    }

    public void setCommunityGroupId(Long communityGroupId) {
        this.communityGroupId = communityGroupId;
    }

    public Long getCommunityId() {
        return communityId;
    }

    public void setCommunityId(Long communityId) {
        this.communityId = communityId;
    }

    public Long getMessageId() {
        return messageId;
    }

    public void setMessageId(Long messageId) {
        this.messageId = messageId;
    }

    public Long getMessageTemplateId() {
        return messageTemplateId;
    }

    public void setMessageTemplateId(Long messageTemplateId) {
        this.messageTemplateId = messageTemplateId;
    }

    public String getReceiverName() {
        return receiverName;
    }

    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getSmsTemplateCode() {
        return smsTemplateCode;
    }

    public void setSmsTemplateCode(String smsTemplateCode) {
        this.smsTemplateCode = smsTemplateCode;
    }

    public String getSmsTemplateParam() {
        return smsTemplateParam;
    }

    public void setSmsTemplateParam(String smsTemplateParam) {
        this.smsTemplateParam = smsTemplateParam;
    }

    public Integer getAlarmLevel() {
        return alarmLevel;
    }

    public void setAlarmLevel(Integer alarmLevel) {
        this.alarmLevel = alarmLevel;
    }

    public ZonedDateTime getSendTime() {
        return sendTime;
    }

    public void setSendTime(ZonedDateTime sendTime) {
        this.sendTime = sendTime;
    }

    public Boolean isSendSuccess() {
        return sendSuccess;
    }

    public void setSendSuccess(Boolean sendSuccess) {
        this.sendSuccess = sendSuccess;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        SmsHistoryDTO smsHistoryDTO = (SmsHistoryDTO) o;
        if (smsHistoryDTO.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), smsHistoryDTO.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    @Override
    public String toString() {
        return "SmsHistoryDTO{" +
            "id=" + getId() +
            ", communityGroupId=" + getCommunityGroupId() +
            ", communityId=" + getCommunityId() +
            ", messageId=" + getMessageId() +
            ", messageTemplateId=" + getMessageTemplateId() +
            ", receiverName='" + getReceiverName() + "'" +
            ", mobile='" + getMobile() + "'" +
            ", content='" + getContent() + "'" +
            ", smsTemplateCode='" + getSmsTemplateCode() + "'" +
            ", smsTemplateParam='" + getSmsTemplateParam() + "'" +
            ", alarmLevel='" + getAlarmLevel() + "'" +
            ", sendTime='" + getSendTime() + "'" +
            ", sendSuccess='" + isSendSuccess() + "'" +
            ", errorMessage='" + getErrorMessage() + "'" +
            "}";
    }
}
