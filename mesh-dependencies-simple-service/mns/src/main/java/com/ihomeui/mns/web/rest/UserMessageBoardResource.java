package com.ihomeui.mns.web.rest;

import io.micrometer.core.annotation.Timed;
import com.ihomeui.mns.service.UserMessageBoardService;
import com.ihomeui.mns.web.rest.mapper.UserMessageBoardMapper;
import com.ihomeui.mesh.web.utils.HeaderUtil;
import com.ihomeui.mesh.web.utils.PaginationUtil;
import com.ihomeui.mns.web.rest.dto.UserMessageBoardDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.inject.Inject;
import javax.validation.Valid;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Optional;

/**
 * REST controller for managing UserMessageBoard.
 */
@RestController
@RequestMapping("/api")
public class UserMessageBoardResource {

    private final Logger log = LoggerFactory.getLogger(UserMessageBoardResource.class);

    @Inject
    private UserMessageBoardService userMessageBoardService;
    @Inject
    private UserMessageBoardMapper userMessageBoardMapper;

    /**
     * GET  /user-message-boards : get all the userMessageBoards.
     *
     * @param pageable the pagination information
     * @return the ResponseEntity with status 200 (OK) and the list of userMessageBoards in body
     * @throws URISyntaxException if there is an error to generate the pagination HTTP headers
     */
    @GetMapping("/user-message-boards")
    @Timed
    public ResponseEntity<List<UserMessageBoardDTO>> getAllUserMessageBoards(@RequestParam(required = false) String query, Pageable pageable)
        throws URISyntaxException {
        log.debug("REST request to get a page of UserMessageBoards");
        Page<UserMessageBoardDTO> page = userMessageBoardService.findAll(query, pageable)
            .map(userMessageBoard -> userMessageBoardMapper.userMessageBoardToUserMessageBoardDTO(userMessageBoard));
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/user-message-boards");
        return new ResponseEntity<>(page.getContent(), headers, HttpStatus.OK);
    }

    /**
     * GET  /user-message-boards/:id : get the "id" userMessageBoard.
     *
     * @param id the id of the userMessageBoardDTO to retrieve
     * @return the ResponseEntity with status 200 (OK) and with body the userMessageBoardDTO, or with status 404 (Not Found)
     */
    @GetMapping("/user-message-boards/{id}")
    @Timed
    public ResponseEntity<UserMessageBoardDTO> getUserMessageBoard(@PathVariable Long id) {
        log.debug("REST request to get UserMessageBoard : {}", id);
        UserMessageBoardDTO userMessageBoardDTO = userMessageBoardMapper.userMessageBoardToUserMessageBoardDTO(
            userMessageBoardService.findOne(id)
        );
        return Optional.ofNullable(userMessageBoardDTO)
            .map(result -> new ResponseEntity<>(
                result,
                HttpStatus.OK))
            .orElse(new ResponseEntity<>(HttpStatus.NOT_FOUND));
    }

}
