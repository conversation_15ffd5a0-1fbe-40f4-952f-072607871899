package com.ihomeui.mns.service.notice;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.MoreObjects;
import com.google.common.collect.Sets;
import com.ihomeui.mesh.utils.JsonUtil;
import com.ihomeui.mesh.MeshAppContext;
import com.ihomeui.mns.client.base.BaseRemoteService;
import com.ihomeui.mns.client.base.dto.CommunityDTO;
import com.ihomeui.mns.client.uaa.SimpleUserInfo;
import com.ihomeui.mns.client.uaa.UAARemoteService;
import com.ihomeui.mns.domain.*;
import com.ihomeui.mns.service.UserMessageService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.text.SimpleDateFormat;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by Administrator on 2017/7/13.
 */
public class MessageNoticeDataSource {
    private final Logger log = LoggerFactory.getLogger(MessageNoticeDataSource.class);

    private static final SimpleDateFormat defaultDateTimeFormatter = new SimpleDateFormat("yyyy年MM月dd日 HH:mm:ss");

    private static final String SETTINGS_NAME___SYS = "sysSettings";
    private static final String SETTINGS_NAME___COMMUNITY_GROUP = "communityGroupSettings";
    private static final String SETTINGS_NAME___COMMUNITY = "communitySettings";

    private UAARemoteService _uaaRemoteService;
    private BaseRemoteService _baseRemoteService;
    private UserMessageService _userMessageService;

    private MessageNoticeType messageNoticeType;
    private MessageTemplate messageTemplate;
    private MessageTemplateNoticeSetting messageTemplateNoticeSetting;

    private Long communityGroupId;
    private Long communityId;
    private CommunityDTO community;

    private UserMessage tempMessageReceiverMessage = null; // 由调用方手动设置的userMessage
    private SimpleUserInfo[] tempMessageReceivers = null;  // 由调用方手动设置的messageReceivers

    // ------------------------------------------------------
    // 这些属性可以用于消息模板中的参数引用
    private Message message = null;
    private UserMessage userMessage = null;            // 从message中分析的userMessage
    private SimpleUserInfo messageSender = null;       // 从message中分析的messageSender
    private SimpleUserInfo messageReceiver = null;     // 从message中分析的messageReceiver
    private SimpleUserInfo[] messageReceivers = null;  // 从message中分析的messageReceivers
    private Map<String, Object> messageData = null;
    private Map<String, String> sysSettings = null;
    private Map<String, String> communityGroupSettings = null;
    private Map<String, String> communitySettings = null;
    // ------------------------------------------------------

    Set<Long> messageTargetUsers;
    private Map<String, String> additionalNoticeBeanParam = new HashMap<>();


    public MessageNoticeDataSource(){}
    public MessageNoticeDataSource(Message message){
        this.message = message;
    }
    public MessageNoticeDataSource(Message message, MessageNoticeType messageNoticeType, MessageTemplate messageTemplate, MessageTemplateNoticeSetting noticeSetting){
        this.message = message;
        this.messageNoticeType = messageNoticeType;
        this.messageTemplate = messageTemplate;
        this.messageTemplateNoticeSetting = noticeSetting;
    }

    public UAARemoteService uaaRemoteService() {
        if(_uaaRemoteService == null) _uaaRemoteService = MeshAppContext.getBean(UAARemoteService.class);
        return _uaaRemoteService;
    }

    public BaseRemoteService baseRemoteService() {
        if(_baseRemoteService == null) _baseRemoteService = MeshAppContext.getBean(BaseRemoteService.class);
        return _baseRemoteService;
    }

    public UserMessageService userMessageService() {
        if(_userMessageService == null) _userMessageService = MeshAppContext.getBean(UserMessageService.class);
        return _userMessageService;
    }


    //<editor-fold desc="属性 getter / setter">

    public Long getCommunityGroupId() {
        return communityGroupId != null ? communityGroupId : ( message != null ? message.getCommunityGroupId() : communityGroupId );
    }

    public void setCommunityGroupId(Long communityGroupId) {
        this.communityGroupId = communityGroupId;
    }

    public Long getCommunityId() {
        return communityId != null ? communityId : ( message != null ? message.getCommunityId() : communityId );
    }

    public void setCommunityId(Long communityId) {
        this.communityId = communityId;
    }

    public CommunityDTO getCommunity() {
        if (community == null){
            Long communityId = getCommunityId();
            log.debug("获取小区 communityId={}",communityId);
            if (communityId!=null) community = baseRemoteService().getCommunity(communityId);
        }
        log.debug("获取小区{}",community);
        return community;
    }

    public MessageNoticeDataSource community(CommunityDTO community) {
        this.community = community;
        return this;
    }

    public void setCommunity(CommunityDTO community) {
        this.community = community;
    }

    /**
     * 获取恰当的设置，设置优先次序：communitySettings &gt; communityGroupSettings &gt; sysSettings
     * @return
     */
    public Map<String, String> getDefaultSettings(){
        if(this.getCommunityId() != null) return getCommunitySettings();
        if(this.getCommunityGroupId() != null) return getCommunityGroupSettings();
        return getSysSettings();
    }

    public Map<String, String> getSysSettings() {
        if(sysSettings == null){
            sysSettings = baseRemoteService().getSysSettings();
            if(sysSettings == null) sysSettings = new HashMap<>();
        }
        return sysSettings;
    }

    public Map<String, String> getCommunityGroupSettings() {
        if(communityGroupSettings == null){
            if(communityGroupId == null && message != null) communityGroupId = message.getCommunityGroupId();
            if(communityGroupId == null && communityId != null) communityGroupId = uaaRemoteService().getCommunityGroupIdByDescendant(communityId);
            if(communityGroupId != null) communityGroupSettings = baseRemoteService().getCommunityGroupSettings(communityGroupId);
            //if(communityGroupSettings == null) communityGroupSettings = new HashMap<>();
        }
        return communityGroupSettings == null ? getSysSettings() : communityGroupSettings;
    }

    public Map<String, String> getCommunitySettings() {
        if(communitySettings == null){
            if(communityId == null && message != null) communityId = message.getCommunityId();
            if(communityId != null) communitySettings = baseRemoteService().getCommunitySettings(communityId);
            //if(communitySettings == null) communitySettings = new HashMap<>();
        }
        return communitySettings == null ? getCommunityGroupSettings() : communitySettings;
    }


    public Message getMessage() {
        return message;
    }


    public MessageTemplate getMessageTemplate() {
        if(messageTemplate == null && message != null){
            messageTemplate = message.getTemplate();
        }
        if(messageTemplate == null && messageTemplateNoticeSetting != null){
            messageTemplate = messageTemplateNoticeSetting.getTemplate();
        }
        return messageTemplate;
    }
    public MessageNoticeDataSource messageTemplate(MessageTemplate messageTemplate) {
        this.messageTemplate = messageTemplate;
        return this;
    }
    public void setMessageTemplate(MessageTemplate messageTemplate) {
        this.messageTemplate = messageTemplate;
    }



    public MessageTemplateNoticeSetting getMessageTemplateNoticeSetting() {
        return messageTemplateNoticeSetting;
    }
    public MessageNoticeDataSource messageTemplateNoticeSetting(MessageTemplateNoticeSetting messageTemplateNoticeSetting) {
        this.messageTemplateNoticeSetting = messageTemplateNoticeSetting;
        return this;
    }
    public void setMessageTemplateNoticeSetting(MessageTemplateNoticeSetting messageTemplateNoticeSetting) {
        this.messageTemplateNoticeSetting = messageTemplateNoticeSetting;
    }



    public MessageNoticeType getMessageNoticeType() {
        if(messageNoticeType == null){
            MessageTemplateNoticeSetting noticeSetting = getMessageTemplateNoticeSetting();
            if(noticeSetting != null) messageNoticeType = noticeSetting.getNoticeType();
        }
        return messageNoticeType;
    }
    public MessageNoticeDataSource messageNoticeType(MessageNoticeType messageNoticeType) {
        this.messageNoticeType = messageNoticeType;
        return this;
    }
    public void setMessageNoticeType(MessageNoticeType messageNoticeType) {
        this.messageNoticeType = messageNoticeType;
    }


    public Set<Long> getMessageTargetUsers() {
        if(messageTargetUsers == null){
            messageTargetUsers = new HashSet<>();
            if(message != null) {
                splitToLongAndAddToSet(messageTargetUsers, message.getToUsers());
                // 添加用户组、机构下的用户
                messageTargetUsers.addAll(uaaRemoteService().getUserIds(
                    parseSplitLongsAndJoin(message.getToUserGroups()),
                    parseSplitLongsAndJoin(message.getToOffices(), message.getToCommunitiesEmployee(), message.getToCommunityGroupsEmployee())
                ));
                // 添加社区、社区集团下的用户
                if (StringUtils.isNotBlank(message.getToCommunityGroupsCustomer()) || StringUtils.isNotBlank(message.getToCommunitiesCustomer())) {
                    messageTargetUsers.addAll(baseRemoteService().getChooseUserIds(message.getToCommunityGroupsCustomer(), message.getToCommunitiesCustomer()));
                }
            }
        }
        return messageTargetUsers;
    }
    public MessageNoticeDataSource messageTargetUsers(Set<Long> messageTargetUsers) {
        this.messageTargetUsers = messageTargetUsers;
        return this;
    }
    public void setMessageTargetUsers(Long... targetUsers) {
        this.setMessageTargetUsers(Sets.newHashSet(targetUsers));
    }
    public void setMessageTargetUsers(Set<Long> targetUsers) {
        this.messageTargetUsers = messageTargetUsers;
    }



    public Map<String, Object> getMessageData() {
        if (messageData == null && message != null && StringUtils.isNotBlank(message.getRelatedDataJson())) {
            messageData = jsonDataToMap(message.getRelatedDataJson());
        }
        return messageData;
    }
    public void setMessageData(Map<String, Object> messageData){
        this.messageData = messageData;
    }
    public void setMessageData(String messageData){
        this.messageData = jsonDataToMap(messageData);
    }


    public SimpleUserInfo getMessageSender() {
        if(messageSender == null && message != null && message.getSenderId() != null) {
            messageSender = uaaRemoteService().getSimpleUserInfo(message.getSenderId());
        }
        if(messageSender == null) messageSender = new SimpleUserInfo();
        return messageSender;
    }
    public void setMessageSender(SimpleUserInfo sender){
        this.messageSender = sender;
    }



    public UserMessage getUserMessage() {
        if(tempMessageReceiverMessage!=null) return tempMessageReceiverMessage;

        SimpleUserInfo receiver = getMessageReceiver();
        if(message != null && receiver != null && (userMessage == null||!userMessage.getUserId().equals(receiver.getId()) )) {
            userMessage = userMessageService().findOneByMessageIdAndUserId(message.getId(), receiver.getId());
        }
        return userMessage;
    }
    public void setUserMessage(UserMessage userMessage) {
        this.tempMessageReceiverMessage = userMessage;
    }


    public SimpleUserInfo getMessageReceiver() {
        if(tempMessageReceivers != null && tempMessageReceivers.length > 0) return tempMessageReceivers[0];

        Iterator<Long> targetUsers = getMessageTargetUsers().iterator();
        if(!targetUsers.hasNext()) return null;

        Long userId = targetUsers.next();
        if(messageReceiver == null || !messageReceiver.getId().equals(userId))
            messageReceiver = uaaRemoteService().getSimpleUserInfo(userId);
        return messageReceiver;
    }
    public MessageNoticeDataSource messageReceiver(SimpleUserInfo messageReceiver) {
        this.setMessageReceiver(messageReceiver);
        return this;
    }
    public MessageNoticeDataSource messageReceiver(Long userId) {
        this.setMessageReceiver(userId);
        return this;
    }
    public void setMessageReceiver(Long userId) {
        try {
            setMessageReceiver(uaaRemoteService().getSimpleUserInfoHasIntactMobilePhone(userId));
        }catch(Exception ex){
            // do nothing
        }
    }
    public void setMessageReceiver(SimpleUserInfo messageReceiver) {
        this.tempMessageReceivers = messageReceiver==null?null:new SimpleUserInfo[]{ messageReceiver };
    }

    public SimpleUserInfo[] getMessageReceivers() {
        if(tempMessageReceivers != null) return tempMessageReceivers;

        if(messageReceivers == null) {
            List<SimpleUserInfo> users = new ArrayList<>();
            for (Long userId : getMessageTargetUsers()) {
                users.add(uaaRemoteService().getSimpleUserInfoHasIntactMobilePhone(userId));
            }
            messageReceivers = users.toArray(new SimpleUserInfo[0]);
        }
        return messageReceivers;
    }
    public void setMessageReceivers(SimpleUserInfo... messageReceivers) {
        this.tempMessageReceivers = messageReceivers;
    }

    //</editor-fold>

    //<editor-fold desc="通知类参数设置">

    public Map<String, String> getAdditionalNoticeBeanParam() {
        return additionalNoticeBeanParam;
    }
    public void setAdditionalNoticeBeanParam(Map<String, String> additionalNoticeBeanParam) {
        this.additionalNoticeBeanParam = additionalNoticeBeanParam;
    }
    public void putAdditionalNoticeBeanParam(String paramName, String paramValue) {
        additionalNoticeBeanParam.put(paramName, paramValue);
    }
    /**
     * 分析NoticeBean参数设置中含的系统参数引用(以${sysSetting.XXX}的形式引用)，返回转换后的Bean参数配置
     * @param noticeBeanParamsJsonData
     * @return
     */
    public Map<String, String> parseNoticeBeanParamValues(String noticeBeanParamsJsonData){
        Map<String, String> map = new HashMap<>();

        map.putAll(additionalNoticeBeanParam);
        map.putAll(parseDataReferenceStatementToMap(noticeBeanParamsJsonData));

        return map;
    }
    public Map<String, String> parseNoticeBeanParamValues(MessageNoticeType noticeType){
        if(noticeType == null) return additionalNoticeBeanParam;
        return parseNoticeBeanParamValues(noticeType.getNoticeBeanParams());
    }

    //</editor-fold>

    //<editor-fold desc="消息模板参数引用处理">

    /**
     * 生成消息标题（解析消息标题文本中的变量引用）
     * @return
     */
    public String generateMessageTitle() {
        return generateMessageTitle(false);
    }
    public String generateMessageTitle(boolean skipNoticeSetting) {
        if(!skipNoticeSetting) {
            // 优先使用消息通知方式中的设置
            MessageTemplateNoticeSetting noticeSetting = getMessageTemplateNoticeSetting();
            if (noticeSetting != null) {
                if (!StringUtils.isAllBlank(noticeSetting.getTitleTemplate(), noticeSetting.getContentTemplate())) {
                    return generateMessageTemplateValue(noticeSetting.getTitleTemplate(), noticeSetting.getTitleParams());
                }
            }
        }

        // 其次，使用消息主体中设置的title
        if (message != null && !StringUtils.isAllBlank(message.getTitle(),message.getContent())) {
            return generateMessageTemplateValue(message.getTitle(), null);
        }

        // 最后使用消息模板中的设置
        MessageTemplate template = getMessageTemplate();
        if(template != null) {
            if (!StringUtils.isAllBlank(template.getTitleTemplate(), template.getContentTemplate())) {
                return generateMessageTemplateValue(template.getTitleTemplate(), template.getTitleParams());
            }
        }
        return null;
    }

    /**
     * 生成消息内容（解析消息内容文本中的变量引用）
     * @return
     */
    public String generateMessageContent() {
        return generateMessageContent(false);
    }
    public String generateMessageContent(boolean skipNoticeSetting) {
        if(!skipNoticeSetting) {
            // 优先使用消息通知方式中的设置
            MessageTemplateNoticeSetting noticeSetting = getMessageTemplateNoticeSetting();
            if (noticeSetting != null) {
                if (!StringUtils.isAllBlank(noticeSetting.getTitleTemplate(),noticeSetting.getContentTemplate())) {
                    return generateMessageTemplateValue(noticeSetting.getContentTemplate(), noticeSetting.getContentParams());
                }
            }
        }

        // 其次，使用消息主体中设置的content
        if (message != null && !StringUtils.isAllBlank(message.getTitle(), message.getContent())) {
            return generateMessageTemplateValue(message.getContent(), null);
        }

        // 最后使用消息模板中的设置
        MessageTemplate template = getMessageTemplate();
        if(template != null) {
            if (!StringUtils.isAllBlank(template.getTitleTemplate(),template.getContentTemplate())) {
                return generateMessageTemplateValue(template.getContentTemplate(), template.getContentParams());
            }
        }
        return null;
    }

    /**
     * 获取消息详情页面地址
     * @return
     */
    public String generateDetailPageUrl(){
        Object v = parseDataReferenceStatement(messageTemplate.getDetailPageUrl(), false);
        return null == v ? null : String.valueOf(v);
    }

    /**
     * 强制使用NoticeSetting的设置生成消息内容（主要用于短信服务和微信模板消息）
     * @return
     */
    public String resolveNoticeSettingMessageContent() {
        MessageTemplateNoticeSetting setting = getMessageTemplateNoticeSetting();
        if(StringUtils.isNotBlank(setting.getContentTemplate())) {
            return generateMessageTemplateValue(setting.getContentTemplate(), setting.getContentParams());
        }else if(StringUtils.isNotBlank(setting.getTitleTemplate())){
            return generateMessageTemplateValue(setting.getTitleTemplate(), setting.getTitleParams());
        }else{
            return JsonUtil.toJson(getMessageData());
        }
    }



    /**
     * 解析模板文本中引用的参数
     * @param textTemplate
     * @param paramsRef
     * @return
     */
    public String generateMessageTemplateValue(String textTemplate, String paramsRef) {
        // 解析系统参数引用
        textTemplate = parseDataReferenceStatementToString(textTemplate);

        if (StringUtils.isNotBlank(textTemplate) && StringUtils.isNotBlank(paramsRef)) {
            return String.format(textTemplate, parseTextTemplateParamsRef(paramsRef));
        }
        return textTemplate;
    }

    /**
     * 解析参数值
     * @param params
     * @return
     */
    private Object[] parseTextTemplateParamsRef(String params){
        if(StringUtils.isBlank(params)) return new Object[]{};

        List<Object> paramValues = new ArrayList<>();

        for(String param : params.split(",")) {
            if (StringUtils.isBlank(param)) {
                paramValues.add(null);
                continue;
            }
            paramValues.add(parseDataReferenceStatement(param, true));
        }
        return paramValues.toArray();
    }

    /**
     * 分析JSON字符串中含的系统参数引用(以${sysSetting.XXX}的形式引用)，替换参数值后转换为Map对象。
     * @param jsonMapData
     * @return
     */
    public Map<String, String> parseDataReferenceStatementToMap(String jsonMapData) {
        if(StringUtils.isBlank(jsonMapData)) return new HashMap<>();

        String parsedData = parseDataReferenceStatementToString(jsonMapData);
        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.readValue(parsedData, new TypeReference<HashMap<String, String>>() {});
        }catch(Exception ex){
            return new HashMap<>();
        }
    }
    private String parseDataReferenceStatementToString(String dataReferenceStatement){
        return dataReferenceStatement == null ? null : parseDataReferenceStatement(dataReferenceStatement, false).toString();
    }

    private Object parseDataReferenceStatement(String dataReferenceStatement, boolean checkSimpleMatchIfPatternNotFoundMatcher){
        if(dataReferenceStatement == null) return null;

        String parsedValue = dataReferenceStatement.trim();

        // 首先解析系统参数引用
        boolean isMatched = false;
        Pattern pattern = Pattern.compile("\\$\\{(.*?)\\}");
        Matcher matcher = pattern.matcher(parsedValue);
        while (matcher.find()) {
            isMatched = true;
            String findKey = matcher.group();
            String realKey = findKey.substring(2, findKey.length() - 1).trim();

            parsedValue = StringUtils.replace(
                parsedValue, findKey, MoreObjects.firstNonNull(transDataReferenceToValue(realKey), "").toString()
            );
        }
        if (isMatched) return parsedValue;

        // 分析使用“,”分隔的引用变量名
        if(checkSimpleMatchIfPatternNotFoundMatcher){
            return transDataReferenceToValue(parsedValue);
        }
        return parsedValue;
    }

    private Object transDataReferenceToValue(String dataReference){
        String fixedRef = dataReference.trim();
        Object targetObj;

        String[] names = StringUtils.split(fixedRef, ".");
        if (SETTINGS_NAME___SYS.equals(names[0])
            || SETTINGS_NAME___COMMUNITY_GROUP.equals(names[0])
            || SETTINGS_NAME___COMMUNITY.equals(names[0])) {
            int splitPos = fixedRef.indexOf(".");
            names = new String[]{fixedRef.substring(0, splitPos), fixedRef.substring(splitPos + 1, fixedRef.length())};
        }

        targetObj = this;
        for (String name : names) {
            name = name.trim();

            if (StringUtils.isBlank(name)) continue;
            targetObj = geTargetObjectFieldValue(targetObj, name);
            if(targetObj == null) break;
        }

        if (targetObj instanceof String) {
            String strValue = (String)targetObj;
            if(strValue.contains("T")&&strValue.contains(":")&&strValue.contains("+")&&strValue.length()>19) {
                try{
                    // 尝试转换ZonedDateTime格式
                    strValue = defaultDateTimeFormatter.format(Date.from(ZonedDateTime.parse(strValue).toInstant()));
                }catch(Exception ex){
                    // do nothing
                }
            }
            targetObj = StringUtils.replace(strValue, "\"", "\\\"");
        }
        return targetObj;
    }

    //</editor-fold>

    //<editor-fold desc="其它工具方法">

    /**
     * 取对象字段或属性值
     * @param obj
     * @param fieldName
     * @return
     */
    private Object geTargetObjectFieldValue(Object obj, String fieldName) {
        if (obj == null || StringUtils.isBlank(fieldName)) return null;

        fieldName = fieldName.trim();

        Class<?> clzz = obj.getClass();

        // 如果是map，直接取键值
        if(obj instanceof Map){
            return ((Map)obj).get(fieldName);
        }

        // 直接查Field字段(如果字段值为空，则继续查看属性)
        try {
            Field field = clzz.getField(fieldName);
            Object value = field.get(obj);
            if(value != null) return value;
        }catch(Exception ex){}

        try {
            // 再查getter、is方法
            Method method = null;
            String firstUpperFieldName = fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
            try {
                method = clzz.getMethod("get" + firstUpperFieldName);
            }catch(Exception ex){
                // do nothing
            }
            if(method != null) return method.invoke(obj);

            try {
                method = clzz.getMethod("is" + firstUpperFieldName);
            }catch(Exception ex){
                // do nothing
            }
            if(method != null) return method.invoke(obj);

        }catch (Exception e) {
            // do nothing
        }
        return null;
    }

    /**
     * 将json数据转换Map，如果是多级对象自动转为多级Map
     * @param jsonData
     * @return 返回MapP对象（如果解析出错，返回空的Map对象）
     */
    private Map<String, Object> jsonDataToMap(String jsonData){
        ObjectMapper mapper = new ObjectMapper();
        try {
            JsonNode root = mapper.readTree(jsonData);
            return jsonEntryToMap(root);
        }catch(IOException ex){
            log.error("解析JSON数据出错：jsonData={}", jsonData);
            return new HashMap<>();
        }
    }
    private Map<String, Object> jsonEntryToMap(JsonNode valueNode){
        Map<String, Object> map = new HashMap<>();
        Iterator<Map.Entry<String, JsonNode>> elements = valueNode.fields();
        while (elements.hasNext()) {
            Map.Entry<String, JsonNode> node = elements.next();
            String key = node.getKey();
            JsonNode value = node.getValue();
            if(value.isNull()) map.put(key, null);
            if(value.isValueNode()) map.put(key, node.getValue().asText());
            else map.put(key, jsonEntryToMap(value));
        }
        return map;
    }

    private String parseSplitLongsAndJoin(String... str){
        Set<Long> longs = splitToLongAndAddToSet(str);
        return StringUtils.join(longs, ",");
    }
    private Set<Long> splitToLongAndAddToSet(String... str){
        return splitToLongAndAddToSet(null, str);
    }
    private Set<Long> splitToLongAndAddToSet(Set<Long> toSet, String... str){
        String joinStr = StringUtils.join(str, ",");

        if(toSet == null) toSet = new HashSet<>();
        if(StringUtils.isBlank(joinStr)) return toSet;

        for(String s : joinStr.split(",")){
            if(StringUtils.isBlank(s)) continue;
            try {
                toSet.add(Long.valueOf(s));
            }catch(Exception ex){}
        }
        return toSet;
    }

    //</editor-fold>
}
