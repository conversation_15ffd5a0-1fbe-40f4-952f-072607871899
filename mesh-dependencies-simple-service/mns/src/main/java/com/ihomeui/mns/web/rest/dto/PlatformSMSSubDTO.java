package com.ihomeui.mns.web.rest.dto;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.Objects;


/**
 * A DTO for the PlatformSMSSub entity.
 */
public class PlatformSMSSubDTO implements Serializable {

    private Long id;

    @NotNull
    @Size(max = 50)
    private String name;

    @Size(max = 800)
    private String detail;

    @NotNull
    @Size(max = 500)
    private String bindTemplateCodes;

    @NotNull
    @Size(max = 80)
    private String code;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
    public String getDetail() {
        return detail;
    }

    public void setDetail(String detail) {
        this.detail = detail;
    }
    public String getBindTemplateCodes() {
        return bindTemplateCodes;
    }

    public void setBindTemplateCodes(String bindTemplateCodes) {
        this.bindTemplateCodes = bindTemplateCodes;
    }
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        PlatformSMSSubDTO platformSMSSubDTO = (PlatformSMSSubDTO) o;

        if ( ! Objects.equals(id, platformSMSSubDTO.id)) return false;

        return true;
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(id);
    }

    @Override
    public String toString() {
        return "PlatformSMSSubDTO{" +
            "id=" + id +
            ", name='" + name + "'" +
            ", detail='" + detail + "'" +
            ", bindTemplateCodes='" + bindTemplateCodes + "'" +
            ", code='" + code + "'" +
            '}';
    }
}
