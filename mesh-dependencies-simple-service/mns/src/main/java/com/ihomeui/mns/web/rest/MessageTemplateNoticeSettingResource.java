package com.ihomeui.mns.web.rest;

import io.micrometer.core.annotation.Timed;
import com.ihomeui.mns.service.MessageTemplateNoticeSettingService;
import com.ihomeui.mns.web.rest.mapper.MessageTemplateNoticeSettingMapper;
import com.ihomeui.mesh.web.utils.HeaderUtil;
import com.ihomeui.mesh.web.utils.PaginationUtil;
import com.ihomeui.mns.web.rest.dto.MessageTemplateNoticeSettingDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.inject.Inject;
import javax.validation.Valid;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Optional;

/**
 * REST controller for managing MessageTemplateNoticeSetting.
 */
@RestController
@RequestMapping("/api")
public class MessageTemplateNoticeSettingResource {

    private final Logger log = LoggerFactory.getLogger(MessageTemplateNoticeSettingResource.class);

    @Inject
    private MessageTemplateNoticeSettingService messageTemplateNoticeSettingService;
    @Inject
    private MessageTemplateNoticeSettingMapper messageTemplateNoticeSettingMapper;

    /**
     * POST  /message-template-notice-settings : Create a new messageTemplateNoticeSetting.
     *
     * @param messageTemplateNoticeSettingDTO the messageTemplateNoticeSettingDTO to create
     * @return the ResponseEntity with status 201 (Created) and with body the new messageTemplateNoticeSettingDTO, or with status 400 (Bad Request) if the messageTemplateNoticeSetting has already an ID
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PostMapping("/message-template-notice-settings")
    @Timed
    public ResponseEntity<MessageTemplateNoticeSettingDTO> createMessageTemplateNoticeSetting(@Valid @RequestBody MessageTemplateNoticeSettingDTO messageTemplateNoticeSettingDTO) throws URISyntaxException {
        log.debug("REST request to save MessageTemplateNoticeSetting : {}", messageTemplateNoticeSettingDTO);
        if (messageTemplateNoticeSettingDTO.getId() != null) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert("messageTemplateNoticeSetting", "idexists", "A new messageTemplateNoticeSetting cannot already have an ID")).body(null);
        }
        MessageTemplateNoticeSettingDTO result = messageTemplateNoticeSettingMapper.messageTemplateNoticeSettingToMessageTemplateNoticeSettingDTO(
            messageTemplateNoticeSettingService.save(messageTemplateNoticeSettingMapper.messageTemplateNoticeSettingDTOToMessageTemplateNoticeSetting(messageTemplateNoticeSettingDTO))
        );
        return ResponseEntity.created(new URI("/api/message-template-notice-settings/" + result.getId()))
            .headers(HeaderUtil.createEntityCreationAlert("messageTemplateNoticeSetting", result.getId().toString()))
            .body(result);
    }

    /**
     * PUT  /message-template-notice-settings : Updates an existing messageTemplateNoticeSetting.
     *
     * @param messageTemplateNoticeSettingDTO the messageTemplateNoticeSettingDTO to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated messageTemplateNoticeSettingDTO,
     * or with status 400 (Bad Request) if the messageTemplateNoticeSettingDTO is not valid,
     * or with status 500 (Internal Server Error) if the messageTemplateNoticeSettingDTO couldnt be updated
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PutMapping("/message-template-notice-settings")
    @Timed
    public ResponseEntity<MessageTemplateNoticeSettingDTO> updateMessageTemplateNoticeSetting(@Valid @RequestBody MessageTemplateNoticeSettingDTO messageTemplateNoticeSettingDTO) throws URISyntaxException {
        log.debug("REST request to update MessageTemplateNoticeSetting : {}", messageTemplateNoticeSettingDTO);
        if (messageTemplateNoticeSettingDTO.getId() == null) {
            return createMessageTemplateNoticeSetting(messageTemplateNoticeSettingDTO);
        }
        MessageTemplateNoticeSettingDTO result = messageTemplateNoticeSettingMapper.messageTemplateNoticeSettingToMessageTemplateNoticeSettingDTO(
            messageTemplateNoticeSettingService.save(messageTemplateNoticeSettingMapper.messageTemplateNoticeSettingDTOToMessageTemplateNoticeSetting(messageTemplateNoticeSettingDTO))
        );
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert("messageTemplateNoticeSetting", messageTemplateNoticeSettingDTO.getId().toString()))
            .body(result);
    }

    /**
     * GET  /message-template-notice-settings : get all the messageTemplateNoticeSettings.
     *
     * @param pageable the pagination information
     * @return the ResponseEntity with status 200 (OK) and the list of messageTemplateNoticeSettings in body
     * @throws URISyntaxException if there is an error to generate the pagination HTTP headers
     */
    @GetMapping("/message-template-notice-settings")
    @Timed
    public ResponseEntity<List<MessageTemplateNoticeSettingDTO>> getAllMessageTemplateNoticeSettings(Pageable pageable)
        throws URISyntaxException {
        log.debug("REST request to get a page of MessageTemplateNoticeSettings");
        Page<MessageTemplateNoticeSettingDTO> page = messageTemplateNoticeSettingService.findAll(pageable)
            .map(messageTemplateNoticeSetting -> messageTemplateNoticeSettingMapper.messageTemplateNoticeSettingToMessageTemplateNoticeSettingDTO(messageTemplateNoticeSetting));
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/message-template-notice-settings");
        return new ResponseEntity<>(page.getContent(), headers, HttpStatus.OK);
    }

    /**
     * GET  /message-template-notice-settings/:id : get the "id" messageTemplateNoticeSetting.
     *
     * @param id the id of the messageTemplateNoticeSettingDTO to retrieve
     * @return the ResponseEntity with status 200 (OK) and with body the messageTemplateNoticeSettingDTO, or with status 404 (Not Found)
     */
    @GetMapping("/message-template-notice-settings/{id}")
    @Timed
    public ResponseEntity<MessageTemplateNoticeSettingDTO> getMessageTemplateNoticeSetting(@PathVariable Long id) {
        log.debug("REST request to get MessageTemplateNoticeSetting : {}", id);
        MessageTemplateNoticeSettingDTO messageTemplateNoticeSettingDTO = messageTemplateNoticeSettingMapper.messageTemplateNoticeSettingToMessageTemplateNoticeSettingDTO(
            messageTemplateNoticeSettingService.findOne(id)
        );
        return Optional.ofNullable(messageTemplateNoticeSettingDTO)
            .map(result -> new ResponseEntity<>(
                result,
                HttpStatus.OK))
            .orElse(new ResponseEntity<>(HttpStatus.NOT_FOUND));
    }

    /**
     * DELETE  /message-template-notice-settings/:id : delete the "id" messageTemplateNoticeSetting.
     *
     * @param id the id of the messageTemplateNoticeSettingDTO to delete
     * @return the ResponseEntity with status 200 (OK)
     */
    @DeleteMapping("/message-template-notice-settings/{id}")
    @Timed
    public ResponseEntity<Void> deleteMessageTemplateNoticeSetting(@PathVariable Long id) {
        log.debug("REST request to delete MessageTemplateNoticeSetting : {}", id);
        messageTemplateNoticeSettingService.delete(id);
        return ResponseEntity.ok().headers(HeaderUtil.createEntityDeletionAlert("messageTemplateNoticeSetting", id.toString())).build();
    }

}
