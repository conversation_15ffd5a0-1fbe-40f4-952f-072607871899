package com.ihomeui.pay.client.base;

import com.fasterxml.jackson.annotation.JsonInclude;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Objects;


/**
 * A DTO for the Setting entity.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SettingDTO implements Serializable {

    private Long id;

    @NotNull
    private Long seq;

    @NotNull
    @Size(max = 64)
    private String name;

    @NotNull
    @Size(max = 4096)
    private String value;

    @NotNull
    @Size(max = 256)
    private String label;

    @Size(max = 256)
    private String remarks;

    @NotNull
    private Boolean isSystem;

    @NotNull
    private Boolean isPublic;

    @NotNull
    private Boolean isReadOnly;


    private Long parentId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    public Long getSeq() {
        return seq;
    }

    public void setSeq(Long seq) {
        this.seq = seq;
    }
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getRemarks() {
        return remarks;
    }
    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public Boolean getIsSystem() {
        return isSystem;
    }
    public void setIsSystem(Boolean isSystem) {
        this.isSystem = isSystem;
    }

    public Boolean getIsPublic() {
        return isPublic;
    }
    public void setIsPublic(Boolean isPublic) {
        this.isPublic = isPublic;
    }

    public Boolean getIsReadOnly() {
        return isReadOnly;
    }
    public void setIsReadOnly(Boolean isReadOnly) {
        this.isReadOnly = isReadOnly;
    }

    public Long getParentId() {
        return parentId;
    }
    public void setParentId(Long settingId) {
        this.parentId = settingId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        SettingDTO settingDTO = (SettingDTO) o;

        if ( ! Objects.equals(id, settingDTO.id)) return false;

        return true;
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(id);
    }

    @Override
    public String toString() {
        return "SettingDTO{" +
            "id=" + id +
            ", seq=" + seq +
            ", name='" + name + '\'' +
            ", value='" + value + '\'' +
            ", label='" + label + '\'' +
            ", remarks='" + remarks + '\'' +
            ", isSystem=" + isSystem +
            ", isPublic=" + isPublic +
            ", isReadOnly=" + isReadOnly +
            ", parentId=" + parentId +
            '}';
    }
}
