package com.ihomeui.pay.web.rest;

import io.micrometer.core.annotation.Timed;
import com.ihomeui.common.utils.CriteriaParser;
import com.ihomeui.mesh.web.rest.errors.BadRequestAlertException;
import com.ihomeui.mesh.web.rest.util.PaginationUtil;
import com.ihomeui.pay.PayAppHelper;
import com.ihomeui.pay.domain.PayConfig;
import com.ihomeui.pay.domain.PayOrder;
import com.ihomeui.pay.domain.PayOrderHistory;
import com.ihomeui.pay.domain.constant.PayConstants;
import com.ihomeui.pay.service.PayOrderHistoryService;
import com.ihomeui.pay.service.PayOrderService;
import com.ihomeui.pay.service.PayService;
import com.ihomeui.pay.service.schedules.ScheduledTask;
import com.ihomeui.pay.web.rest.dto.PayOrderDTO;
import com.ihomeui.pay.web.rest.mapper.PayOrderMapper;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.inject.Inject;
import javax.validation.Valid;
import java.net.URISyntaxException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * REST controller for managing PayOrder.
 */
@RestController
@RequestMapping("/api")
public class PayOrderResource {
    private final Logger log = LoggerFactory.getLogger(PayOrderResource.class);

    private PayOrderService payOrderService;

    private PayOrderMapper payOrderMapper;

    private PayOrderHistoryService payOrderHistoryService;

    private PayAppHelper payAppHelper;

    public PayOrderResource(PayOrderService payOrderService, PayOrderMapper payOrderMapper, PayOrderHistoryService payOrderHistoryService, PayAppHelper payAppHelper) {
        this.payOrderService = payOrderService;
        this.payOrderMapper = payOrderMapper;
        this.payOrderHistoryService = payOrderHistoryService;
        this.payAppHelper = payAppHelper;
    }

    /**
     * GET  /pay-orders : get all the payOrders.
     *
     * @param pageable the pagination information
     * @return the ResponseEntity with status 200 (OK) and the list of payOrders in body
     * @throws URISyntaxException if there is an error to generate the pagination HTTP headers
     */
    @GetMapping("/pay-orders")
    @Timed
    public ResponseEntity<List<PayOrderDTO>> getAllPayOrders(@ApiParam(value = "社区ID")
                                                            @RequestParam(required = false) Long communityId,
                                                            @RequestParam(required = false) Long communityGroupId,
                                                            @RequestParam(required = false) String query,
                                                             @RequestParam(required = false) String keywords,
                                                            @ApiParam(value="订单类型,可以指定多个类型使用','分隔")
                                                            @RequestParam(required = false) String orderKinds,
                                                            @ApiParam Pageable pageable)
                                                            throws URISyntaxException {
        log.debug("REST request to get a page of PayOrders");

        // 以下两个语句用兼容旧的接口调用
        String theKeywords = keywords == null ? query : keywords;
        String theQuery = keywords == null? null: query;

        Page<PayOrderDTO> page = payOrderService.findAll((root, cq, cb) ->{
            return (new CriteriaParser(theKeywords,"orderId,orderTitle,orderDetail,openPayTradeNo"))
                .andExpression(theQuery)
                .andEqual("communityGroupId", communityGroupId)
                .andEqual("communityId", communityId)
                .andIn("orderKind", orderKinds)
                .toPredicate(root, cq, cb);
            },pageable).map(
            payOrderMapper::payOrderToPayOrderDTO
        );
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/pay-orders");
        return new ResponseEntity<>(page.getContent(), headers, HttpStatus.OK);
    }

    /**
     * GET  /pay-orders/:id : get the "id" payOrder.
     *
     * @param id the id of the payOrderDTO to retrieve
     * @return the ResponseEntity with status 200 (OK) and with body the payOrderDTO, or with status 404 (Not Found)
     */
    @GetMapping("/pay-orders/{id}")
    @Timed
    public ResponseEntity<PayOrderDTO> getPayOrder(@PathVariable Long id) {
        log.debug("REST request to get PayOrder : {}", id);
        PayOrderDTO payOrderDTO = payOrderMapper.payOrderToPayOrderDTO(
            payOrderService.findOne(id)
        );
        return Optional.ofNullable(payOrderDTO)
            .map(result -> new ResponseEntity<>(
                result,
                HttpStatus.OK))
            .orElse(new ResponseEntity<>(HttpStatus.NOT_FOUND));
    }

    @Inject
    private ScheduledTask scheduledTask;

    @GetMapping("/doBusiness/now")
    @Timed
    public ResponseEntity<Void> doBusiness() {
        scheduledTask.doBusiness();
        return ResponseEntity.ok().build();
    }

    /**
     * 发起微信扫码支付
     */
    @PostMapping("/pay-orders/to-wx-native-pay")
    public ResponseEntity<?> wxNativePay(
        @RequestParam String orderId
    ) {
        PayOrder payOrder = payOrderService.findOneByOrderId(orderId);
        if (payOrder == null){
            return new ResponseEntity<>("该订单已经失效或不存在",HttpStatus.BAD_REQUEST);
        }
        try {
            if (!( payOrder.getOrderState().equals(PayConstants.OrderState.NEW) ||
                payOrder.getOrderState().equals(PayConstants.OrderState.PRE_ORDER_SUCCESS)
            )
                || payOrder.getExpireTime().isBefore(LocalDateTime.now())
                ){
                return new ResponseEntity<>("已经支付的订单不能重复发起支付",HttpStatus.BAD_REQUEST);
            }
            payOrder.setSourceKind("WEIXIN_NATIVE");
            payOrder.setPayExtendParams(null);
            payOrder.setOpenPayKind(PayConstants.OpenPayKind.WXPAY);
            payOrder = payOrderService.save(payOrder);
            //向支付系统统一下单
            PayService payService = PayService.getPayService(payOrder.getOpenPayKind());
            if ( payService == null ) {
                return new ResponseEntity<>("支付类型参数错误 " + payOrder.getOpenPayKind(),HttpStatus.BAD_REQUEST);
            }
            payService.preOrder(payOrder);
        } catch (Exception e) {
            payOrder.setOrderState(PayConstants.OrderState.PRE_ORDER_ERROR);
            payOrder.setOpenPayPreMessage(e.getMessage());
            payOrderService.saveToHistory(payOrder);
            return new ResponseEntity<>(e.getMessage(),HttpStatus.BAD_REQUEST);
        }
        //返回完整支付系统返回信息给客户端
        return ResponseEntity.ok(payOrder);
    }

    @GetMapping("/_pay-orders/{queryId}")
    public ResponseEntity<PayOrderDTO> findOrder(
        @PathVariable @Valid String queryId
    ){
        PayOrderDTO payOrderVM = null;
        List<PayOrderHistory> payOrderHistories = payOrderHistoryService.findAll((root, cq, cb) -> cb.or(
            cb.equal(root.get("id").as(String.class),queryId),
            cb.equal(root.get("businessId"),queryId),
            cb.equal(root.get("openPayTradeNo"),queryId),
            cb.equal(root.get("orderId"),queryId)
        ),PageRequest.of(0,1)).getContent();
        if ( payOrderHistories.size() != 0 ) {
            payOrderVM = payOrderMapper.payOrderHistoryToPayOrderDTO(payOrderHistories.get(0));
        } else {
            List<PayOrder> payOrders = payOrderService.findAll((root, cq, cb) -> cb.or(
                cb.equal(root.get("id").as(String.class),queryId),
                cb.equal(root.get("businessId"),queryId),
                cb.equal(root.get("openPayTradeNo"),queryId),
                cb.equal(root.get("orderId"),queryId)
            ),PageRequest.of(0,1)).getContent();
            if ( payOrders.size() != 0 ){
                payOrderVM = payOrderMapper.payOrderToPayOrderDTO(payOrders.get(0));
            }
        }
        return Optional.ofNullable(payOrderVM)
            .map(result -> new ResponseEntity<>(
                result,
                HttpStatus.OK))
            .orElse(new ResponseEntity<>(HttpStatus.NOT_FOUND));
    }

    /**
     * 电脑支付
     */
    @PostMapping("/pay-orders/start-pay")
    public ResponseEntity<PayOrderDTO> startPay(
        @RequestParam String orderId,
        @RequestParam String openPayKind,
        @RequestParam(required = false) String payExtendParams,
        @RequestParam(required = false) String pageReturnUrl
    ) {
        PayOrder payOrder = payOrderService.findOneByOrderId(orderId);

        if (payOrder == null){
            throw new BadRequestAlertException("该订单已经失效或不存在");
        }

        if (
            !(
                payOrder.getOrderState().equals(PayConstants.OrderState.NEW) ||
                payOrder.getOrderState().equals(PayConstants.OrderState.PRE_ORDER_SUCCESS) ||
                payOrder.getOrderState().equals(PayConstants.OrderState.PRE_ORDER_ERROR)
            )
        ){
            throw new BadRequestAlertException("已经支付的订单不能重复发起支付");
        }

        if (payOrder.getExpireTime().isBefore(LocalDateTime.now())){
            throw new BadRequestAlertException("订单已经过期");
        }

        try {
            payOrder.setUserId(payAppHelper.getCurrentUserId());
            payOrder.setSourceKind(PayConstants.SourceKind.BROWSER_PC);
            if (StringUtils.isNotBlank(payExtendParams)){
                payOrder.setPayExtendParams(payExtendParams);
            }

            if ( StringUtils.isNotEmpty(openPayKind) ){
                payOrder.setOpenPayKind(openPayKind);
            }
            if (StringUtils.isNotEmpty(pageReturnUrl)){
                payOrder.setPageReturnUrl(pageReturnUrl);
            }
            payOrder = payOrderService.save(payOrder);
            //向支付系统统一下单
            PayService payService = PayService.getPayService(payOrder.getOpenPayKind());
            if (payService != null){
                payService.preOrder(payOrder);
            } else {
                throw new BadRequestAlertException("不支持的支付类型");
            }
        } catch (Exception e) {
            log.error("发起支付失败 {} ",e.getMessage());
            if (!PayConstants.OrderState.PRE_ORDER_ERROR.equals(payOrder.getOrderState())) {
                payOrder.setOrderState(PayConstants.OrderState.PRE_ORDER_ERROR);
                payOrder.setOpenPayPreMessage(e.getMessage());
            }
            payOrderService.save(payOrder);
            throw new BadRequestAlertException(StringUtils.isNotBlank(payOrder.getOpenPayPreMessage()) ? payOrder.getOpenPayPreMessage() : e.getMessage());
        }
        payOrder = payOrderService.findOneByOrderId(orderId);
        return new ResponseEntity<>(
            payOrderMapper.payOrderToPayOrderDTO(payOrder),
            HttpStatus.OK
        );
    }
}
