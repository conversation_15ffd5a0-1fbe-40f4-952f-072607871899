package com.ihomeui.pay.service.provider.ccbpay;

import java.io.Serializable;

/**
 * 建行支付商户配置
 */
public class CCBPayConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    private String merchantId;

    private String posId;

    private String branchId;

    private String publicKey;

    private String quPwd;

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getPosId() {
        return posId;
    }

    public void setPosId(String posId) {
        this.posId = posId;
    }

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }

    public String getPublicKey() {
        return publicKey;
    }

    public void setPublicKey(String publicKey) {
        this.publicKey = publicKey;
    }

    public String getQuPwd() {
        return quPwd;
    }

    public void setQuPwd(String quPwd) {
        this.quPwd = quPwd;
    }

    public String getPub() {
        if (publicKey == null) return null;
        return publicKey.substring(publicKey.length()-30, publicKey.length());
    }

    @Override
    public String toString() {
        return "CCBPayConfig{" +
            "merchantId='" + merchantId + '\'' +
            ", posId='" + posId + '\'' +
            ", branchId='" + branchId + '\'' +
            ", publicKey='" + publicKey + '\'' +
            ", pub='" + getPub() + '\'' +
            ", quPwd='" + quPwd + '\'' +
            '}';
    }
}
