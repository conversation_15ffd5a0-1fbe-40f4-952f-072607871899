package com.ihomeui.pay.client.weixin;

import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * Created by Administrator on 2017/6/12.
 */
@Service
public class WeixinRemoteService {

    @Resource
    private WeixinClient weixinClient;

//    public String getOfficalAppId(Long communityGroupId, Long communityId) {
//        return weixinClient.getOfficalAppId(communityGroupId,communityId).getBody();
//    }
//
//    public void authPublish(Long communityGroupId, Long officalId, Long tinyAppId, String setCommunityIds) {
//        weixinClient.authPublish(communityGroupId,officalId,tinyAppId,setCommunityIds);
//    }
//
//    public String getTinyAppUserMobile(String mobileInfo, String appId, String jsCode) {
//        return weixinClient.getTinyAppUserMobile(appId,mobileInfo,jsCode).getBody();
//    }
//
//    public List<String> getRelatedAppIds(String appId) {
//        return weixinClient.getRelatedAppIds(appId).getBody();
//    }
//
//    public List<String> getAppIds(Long communityGroupId) {
//        return weixinClient.getAppIds(communityGroupId).getBody();
//    }

    @Cacheable(value = "BindCommunityGroupIds",key = "'by:' + #appId")
    public Set<Long> getBindCommunityGroupIds(String appId) {
        return weixinClient.getBindCommunityGroupIds(appId).getBody();
    }

    @Cacheable(value = "BindCommunityIds",key = "'by:' + #appId")
    public Set<Long> getBindCommunityIds(String appId) {
        return weixinClient.getBindCommunityIds(appId).getBody();
    }

    @Cacheable(value = "ParentAppId",key = "'by:' + #appId")
    public String getParentAppId(String appId) {
        return weixinClient.getParentAppId(appId).getBody();
    }
}
