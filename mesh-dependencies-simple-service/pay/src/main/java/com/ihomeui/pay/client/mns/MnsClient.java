package com.ihomeui.pay.client.mns;

import com.ihomeui.mesh.client.AuthorizedFeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * Created by XXM on 2016/12/13.
 */
@AuthorizedFeignClient(name="mns",decode404 = true)
public interface MnsClient {
    /**
     * 发布新消息
     * @param message 消息内容
     * @return
     */
	@RequestMapping(value="/api/messages", method = RequestMethod.POST)
    ResponseEntity<MnsMessage> sendMessage(@RequestBody MnsMessage message);

    @RequestMapping(value="/api/sms/send_package_fetch_code", method = RequestMethod.POST)
    ResponseEntity<Void> sendSmsPackageFetchCode(@RequestParam("mobile") String mobile,
                                                 @RequestParam("code") String code,
                                                 @RequestParam("communityGroupId") Long communityGroupId,
                                                 @RequestParam("communityId") Long communityId);
}
