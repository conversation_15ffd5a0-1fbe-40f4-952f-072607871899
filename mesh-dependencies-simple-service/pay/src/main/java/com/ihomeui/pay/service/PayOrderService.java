package com.ihomeui.pay.service;

import com.ihomeui.pay.domain.PayOrder;
import com.ihomeui.pay.domain.PayOrderHistory;
import com.ihomeui.pay.domain.RefundOrder;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * Service Interface for managing PayOrder.
 */
public interface PayOrderService {

    /**
     * Save a payOrder.
     *
     * @param payOrder the entity to save
     * @return the persisted entity
     */
    PayOrder save(PayOrder payOrder);

    /**
     *  Get all the payOrders.
     *
     *  @param pageable the pagination information
     *  @return the list of entities
     */
    Page<PayOrder> findAll(Pageable pageable);

    Page<PayOrder> findAll(Specification<PayOrder> spec, Pageable pageable);

    /**
     *  Get the "id" payOrder.
     *
     *  @param id the id of the entity
     *  @return the entity
     */
    PayOrder findOne(Long id);

    /**
     *  Delete the "id" payOrder.
     *
     *  @param id the id of the entity
     */
    void delete(Long id);

    PayOrder findOneByOrderId(String orderId);

    PayOrder findOneByBusinessIdAndOrderKind(String businessId, String orderKind);

    PayOrder findOneByBusinessDataAndOrderKind(String businessData, String orderKind);

    PayOrderHistory saveToHistory(PayOrder payOrder);

    PayOrder paySuccess(Map<String, String> params, PayOrder payOrder, String transactionId, String resultCode, LocalDateTime payTime);

    PayOrder payError(Map<String, String> params, PayOrder payOrder, String transactionId, String resultCode);

    RefundOrder refund(Long operateUserId, String orderId, String refundReason, BigDecimal refundAmount, String businessId);

    void refundResult(String refundNo, Boolean successFlag, String refundStatus, String toJSONString);

    void refundDoBusiness(String refundNo, String signKey) throws Exception;

    void refundDoBusiness(RefundOrder refundOrder, String signKey) throws Exception;
}
