package com.ihomeui.pay.service.provider.cjpay.http;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.*;

public class PayRequestUtil {

    private static final Logger log = LoggerFactory.getLogger(PayRequestUtil.class);

    static final int CONN_TIMEOUT = 3000 * 2;
    static final int READ_TIMEOUT = 5000 * 2;

    public static String post(String url, Map<String, String> data, Map<String, String> headers) throws IOException {
        InputStream input = null;
        String content = getParamContent(data);
        if (!content.isEmpty()) {
            byte[] bytes = content.getBytes(StandardCharsets.UTF_8);
            if (headers == null) {
                headers = new HashMap<>();
            }
            headers.put("Content-Length", String.valueOf(bytes.length));
            input = new ByteArrayInputStream(bytes);
        }
        return http("POST", url, input, headers);
    }

    private static String getParamContent(Map<String, String> params) {
        StringBuilder content = new StringBuilder();
        List<String> keys = new ArrayList(params.keySet());
        Collections.sort(keys);
        int index = 0;
        for (String key : keys) {
            Object value = params.get(key);
            if(StringUtils.isNotEmpty(key)&&value!=null&&StringUtils.isNotEmpty(value.toString())) {
                content.append(index == 0 ? "" : "&")
                    .append(key)
                    .append("=")
                    .append(value.toString());
                ++index;
            }
        }
        return content.toString();
    }


    static String http(String method, String url, InputStream dataInput, Map<String, String> headers)
        throws IOException {
        log.info(method + ": " + url);
        HttpURLConnection conn = (HttpURLConnection) new URL(url).openConnection();
        conn.setRequestMethod(method);
        conn.setConnectTimeout(CONN_TIMEOUT);
        conn.setReadTimeout(READ_TIMEOUT);
        conn.setUseCaches(false);
        conn.setInstanceFollowRedirects(false);
        conn.setAllowUserInteraction(false);
        conn.setDoOutput(dataInput != null);
        if (headers != null) {
            for (String key : headers.keySet()) {
                conn.setRequestProperty(key, headers.get(key));
            }
        }
        OutputStream output = null;
        InputStream input = null;
        int code = -1;
        try {
            if (dataInput != null) {
                output = conn.getOutputStream();
                byte[] buffer = new byte[10240];
                int n = 0;
                while ((n = dataInput.read(buffer)) != (-1)) {
                    output.write(buffer, 0, n);
                }
                output.flush();
            }
            code = conn.getResponseCode();
            if (code / 100 != 2) {
                log.error("Bad response code: " + code + ": " + conn.getResponseMessage());
                input = conn.getErrorStream();
                if (input != null) {
                    ByteArrayOutputStream byteArray = new ByteArrayOutputStream(4096);
                    byte[] buffer = new byte[10240];
                    int n = 0;
                    while ((n = input.read(buffer)) != (-1)) {
                        byteArray.write(buffer, 0, n);
                    }
                    log.error("HttpError: " + byteArray.toString("UTF-8"));
                }
                throw new IOException("Bad response: " + code);
            }
            input = conn.getInputStream();
            byte[] buffer = new byte[4096];
            int n = 0;
            ByteArrayOutputStream byteArray = new ByteArrayOutputStream();
            while ((n = input.read(buffer)) != (-1)) {
                byteArray.write(buffer, 0, n);
            }
            return new String(byteArray.toByteArray(), StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("http请求异常 url={}", url, e);
            return null;
        } finally {
            if (output != null) {
                try {
                    output.close();
                } catch (IOException e) {
                }
            }
            if (input != null) {
                try {
                    input.close();
                } catch (IOException e) {
                }
            }
            conn.disconnect();
        }
    }
}

