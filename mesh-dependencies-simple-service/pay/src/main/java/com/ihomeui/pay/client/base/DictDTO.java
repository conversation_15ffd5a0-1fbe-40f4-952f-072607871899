package com.ihomeui.pay.client.base;

import com.fasterxml.jackson.annotation.JsonInclude;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Objects;


/**
 * A DTO for the Dict entity.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DictDTO implements Serializable {

    private Long id;

    @NotNull
    @Size(max = 64)
    private String kind;

    @NotNull
    private Long seq;

    @NotNull
    @Size(max = 256)
    private String value;

    @NotNull
    @Size(max = 128)
    private String text;

    @NotNull
    private Boolean isSystem = false;

    @NotNull
    private Boolean isReadOnly = false;

    @Size(max = 256)
    private String remarks;

    @Size(max = 1024)
    private String data;


    private Long parentId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    public String getKind() {
        return kind;
    }

    public void setKind(String kind) {
        this.kind = kind;
    }
    public Long getSeq() {
        return seq;
    }

    public void setSeq(Long seq) {
        this.seq = seq;
    }
    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }
    public Boolean getIsSystem() {
        return isSystem;
    }

    public void setIsSystem(Boolean isSystem) {
        this.isSystem = isSystem;
    }
    public Boolean getIsReadOnly() {
        return isReadOnly;
    }

    public void setIsReadOnly(Boolean isReadOnly) {
        this.isReadOnly = isReadOnly;
    }
    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }
    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long dictId) {
        this.parentId = dictId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        DictDTO dictDTO = (DictDTO) o;

        if ( ! Objects.equals(id, dictDTO.id)) return false;

        return true;
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(id);
    }

    @Override
    public String toString() {
        return "DictDTO{" +
            "id=" + id +
            ", kind='" + kind + "'" +
            ", seq='" + seq + "'" +
            ", value='" + value + "'" +
            ", text='" + text + "'" +
            ", isSystem='" + isSystem + "'" +
            ", isReadOnly='" + isReadOnly + "'" +
            ", remarks='" + remarks + "'" +
            '}';
    }
}
