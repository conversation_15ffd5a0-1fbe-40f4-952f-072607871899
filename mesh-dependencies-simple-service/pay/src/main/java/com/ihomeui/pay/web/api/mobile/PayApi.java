package com.ihomeui.pay.web.api.mobile;

import com.ihomeui.pay.PayAppHelper;
import com.ihomeui.pay.client.base.BaseRemoteService;
import com.ihomeui.pay.client.base.CommunityDTO;
import com.ihomeui.pay.client.base.constant.SettingConstants;
import com.ihomeui.pay.domain.*;
import com.ihomeui.pay.domain.constant.PayConstants;
import com.ihomeui.pay.service.*;
import com.ihomeui.pay.web.api.mobile.mapper.OpenPayKindMapper;
import com.ihomeui.pay.web.api.mobile.mapper.PayOrderVMMapper;
import com.ihomeui.pay.web.api.mobile.pm.PayOrderPm;
import com.ihomeui.pay.web.api.mobile.vm.OpenPayKindVM;
import com.ihomeui.pay.web.api.mobile.vm.PayOrderVM;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.inject.Inject;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * @date 2017/7/19
 * @created caixy
 */
@RestController
@RequestMapping("/api/2.0")
public class PayApi {

    private final Logger log = LoggerFactory.getLogger(PayApi.class);

    @Inject
    private PayAppHelper payAppHelper;

    @Inject
    private PayOrderService payOrderService;

    @Inject
    private BaseRemoteService baseRemoteService;

    @Inject
    private OrderKindBusinessSettingService orderKindBusinessSettingService;

    @Inject
    private PayOrderHistoryService payOrderHistoryService;

    @Inject
    private OpenPayKindMapper openPayKindMapper;

    @Inject
    private PayOrderVMMapper payOrderVMMapper;

    @Inject
    private PayConfigService payConfigService;


    /**
     * 查询订单
     * 调用第三方支付查询接口
     */
    @PostMapping("/pay/order/{orderId}")
    public ResponseEntity<?> findOrder(@PathVariable @Valid String orderId){
        PayOrderVM payOrderVM;
        PayOrder payOrder = payOrderService.findOneByOrderId(orderId);
        if ( payOrder == null ){
            PayOrderHistory payOrderHistory = payOrderHistoryService.findOneByOrderId(orderId);
            payOrderVM = payOrderVMMapper.payOrderHistoryToPayOrderVM(payOrderHistory);
        } else {
            if ( payOrder.getPaySuccess() == null || !payOrder.getPaySuccess()){
                PayService payService = PayService.getPayService(payOrder.getOpenPayKind());
                if ( payService != null ){
                    payOrder = payService.queryOrder(payOrder);
                }
            }
            payOrderVM = payOrderVMMapper.payOrderToPayOrderVM(payOrder);
        }
        return ResponseEntity.ok().body(payOrderVM);
    }

    /**
     * 下单
     */
    @PostMapping("/pay/order")
    public ResponseEntity<PayOrder> payOrder(
        @Valid @RequestBody PayOrderPm payOrderPm
    ) {
        if ( payOrderPm.getTotleAmount() <= 0D ) {
            return new ResponseEntity("支付金额不能为0元或小于0元",HttpStatus.BAD_REQUEST);
        }

        PayOrder payOrder = new PayOrder();
        BeanUtils.copyProperties(payOrderPm,payOrder);
        OrderKindBusinessSetting setting = orderKindBusinessSettingService.findOneByOrderKind(payOrder.getOrderKind());
        payOrder.setUserId(0L);
        payOrder.setOrderId(UUID.randomUUID().toString().replaceAll("-",""));
        payOrder.setOrderState(PayConstants.OrderState.NEW);

        if (StringUtils.isBlank(payOrder.getOrderTitle())) {
            payOrder.setOrderTitle(setting.getOrderTitle());
        }

        if (StringUtils.isBlank(payOrder.getPageReturnUrl())) {
            payOrder.setPageReturnUrl(setting.getPageReturnUrl());
        }

        if ( payOrder.getCommunityId() != null && payOrder.getCommunityGroupId() == null ) {
            //通过
            CommunityDTO community = baseRemoteService.getCommunityById(payOrder.getCommunityId());
            payOrder.setCommunityGroupId(community.getCommunityGroupId());
        }

        payOrder.setCreateTime(LocalDateTime.now());
        if (payOrder.getExpireTime()==null){
            if (setting.getExpireMinute()!=null){
                payOrder.setExpireTime(payOrder.getCreateTime().plusMinutes(setting.getExpireMinute()));
            }else {
                payOrder.setExpireTime(payOrder.getCreateTime().plusMinutes(30));
            }
        }
        if ( payOrder.getPlatformPay() == null ){
            payOrder.setPlatformPay(false);
        }
        payOrder = payOrderService.save(payOrder);
        return ResponseEntity.ok().body(payOrder);
    }

    /**
     * 发起支付
     */
    @PostMapping("/pay/start")
    public ResponseEntity<Object> payStart(
        @RequestParam String orderId,
        @RequestParam(required = false) String openPayKind,
        @RequestParam String sourceKind,
        @RequestParam(required = false) String payExtendParams,
        @RequestParam(required = false) String pageReturnUrl
    ) {
        Object response;
        //数据准备
        PayOrder payOrder = payOrderService.findOneByOrderId(orderId);
        if (payOrder == null){
            return new ResponseEntity<>("该订单已经失效或不存在",HttpStatus.BAD_REQUEST);
        }
        try {
            if (!( payOrder.getOrderState().equals(PayConstants.OrderState.NEW) ||
                   payOrder.getOrderState().equals(PayConstants.OrderState.PRE_ORDER_SUCCESS)
                 )
                || payOrder.getExpireTime().isBefore(LocalDateTime.now())
            ){
                return new ResponseEntity<>("已经支付的订单不能重复发起支付",HttpStatus.BAD_REQUEST);
            }
            Long userId = null;
            try {
                userId = payAppHelper.getCurrentUserId();
            }catch (Exception ignored){}

            payOrder.setUserId(userId);
            payOrder.setSourceKind(sourceKind);
            if (StringUtils.isNotBlank(payExtendParams)) payOrder.setPayExtendParams(payExtendParams);
            PayConfig payConfig = null;
            if ( StringUtils.isBlank(openPayKind) && StringUtils.isBlank(payOrder.getOpenPayKind()) ){
                payConfig = payConfigService.getPayConfig(payOrder);
                if (payConfig != null) {
                    openPayKind = payConfig.getOpenPayKind();
                    payOrder.setOpenPayKind(payConfig.getOpenPayKind());
                }
            } else if ( StringUtils.isNotEmpty(openPayKind) ){
                payOrder.setOpenPayKind(openPayKind);
            }
            if (StringUtils.isNotEmpty(pageReturnUrl))
                payOrder.setPageReturnUrl(pageReturnUrl);
            payOrder = payOrderService.save(payOrder);
            //向支付系统统一下单
            PayService payService = PayService.getPayService(payOrder.getOpenPayKind());
            if (payService != null){
                response = payService.preOrder(payOrder,payConfig);
            } else {
                return new ResponseEntity<>("不支持的支付类型",HttpStatus.BAD_REQUEST);
            }
        } catch (Exception e) {
            log.error("发起支付失败 {} ",e.getMessage());
            if (!PayConstants.OrderState.PRE_ORDER_ERROR.equals(payOrder.getOrderState())) {
                payOrder.setOrderState(PayConstants.OrderState.PRE_ORDER_ERROR);
                payOrder.setOpenPayPreMessage(e.getMessage());
            }
            payOrderService.saveToHistory(payOrder);
            return new ResponseEntity<>(payOrder.getOpenPayPreMessage(),HttpStatus.BAD_REQUEST);
        }
        HttpHeaders headers = new HttpHeaders();
        headers.add("X-OpenPayKind",openPayKind);
        return new ResponseEntity<Object>(response,headers,HttpStatus.OK);
    }

    /**
     * 获取启用的支付方式
     */
    @GetMapping("/pay/enabled-open-pay-kinds")
    public ResponseEntity<List<OpenPayKindVM>> getEnabledOpenPayKinds(
        @RequestParam Long communityGroupId
    ){
        List<OpenPayKindProviderSetting> openPayKinds = PayService.getDefaultPayService().getEnabledOpenPayKinds(communityGroupId);
        List<OpenPayKindVM> openPayKindVMS = openPayKindMapper.openPayKindProviderSettingsToOpenPayKindVMs(openPayKinds);
        return ResponseEntity.ok(openPayKindVMS);
    }

    /**
     * 发起支付
     */
    @GetMapping("/pay/get-default-type")
    public ResponseEntity<String> getDefaultPayType(
        @RequestParam String orderId,
        @RequestParam(required = false) String payExtendParams
    ) {
        PayOrder payOrder = payOrderService.findOneByOrderId(orderId);
        if(payOrder != null) {
            payOrder.setPayExtendParams(payExtendParams);
            PayConfig payConfig = payConfigService.getPayConfig(payOrder);
            if (payConfig != null){
                String defaultType = payConfig.getOpenPayKind();
                return ResponseEntity.ok(defaultType);
            } else {
                Map<String, String> settings = baseRemoteService
                    .getSettings(payOrder.getCommunityGroupId(),payOrder.getCommunityId(),SettingConstants.Pay.PREFIX);
                String defaultType = settings.get(SettingConstants.Pay.OFFICAL_DEFAULT_TYPE);
                return ResponseEntity.ok(defaultType);
            }
        } else {
            return ResponseEntity.notFound().build();
        }
    }
}
