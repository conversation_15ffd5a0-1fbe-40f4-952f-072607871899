package com.ihomeui.pay.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * 电子收费月报
 */
@ApiModel(description = "电子收费月报")
@Entity
@Table(name = "pay_report_monthly")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class PayReportMonthly implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;

    /**
     * 集团ID
     */
    @ApiModelProperty(value = "集团ID")
    @Column(name = "community_group_id")
    private Long communityGroupId;

    /**
     * 社区ID
     */
    @ApiModelProperty(value = "社区ID")
    @Column(name = "community_id")
    private Long communityId;

    /**
     * 服务
     */
    @ApiModelProperty(value = "服务")
    @Column(name = "service_name")
    private String serviceName;

    /**
     * 时间维度 年月
     */
    @ApiModelProperty(value = "时间维度 年月")
    @Column(name = "report_year_month")
    private String reportYearMonth;

    /**
     * 支付金额统计
     */
    @ApiModelProperty(value = "支付金额统计")
    @Column(name = "total_amount", precision=10, scale=2)
    private BigDecimal totalAmount;

    /**
     * 总订单数
     */
    @ApiModelProperty(value = "总订单数")
    @Column(name = "total_order_count")
    private Integer totalOrderCount;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCommunityGroupId() {
        return communityGroupId;
    }

    public PayReportMonthly communityGroupId(Long communityGroupId) {
        this.communityGroupId = communityGroupId;
        return this;
    }

    public void setCommunityGroupId(Long communityGroupId) {
        this.communityGroupId = communityGroupId;
    }

    public Long getCommunityId() {
        return communityId;
    }

    public PayReportMonthly communityId(Long communityId) {
        this.communityId = communityId;
        return this;
    }

    public void setCommunityId(Long communityId) {
        this.communityId = communityId;
    }

    public String getServiceName() {
        return serviceName;
    }

    public PayReportMonthly serviceName(String serviceName) {
        this.serviceName = serviceName;
        return this;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public String getReportYearMonth() {
        return reportYearMonth;
    }

    public PayReportMonthly reportYearMonth(String reportYearMonth) {
        this.reportYearMonth = reportYearMonth;
        return this;
    }

    public void setReportYearMonth(String reportYearMonth) {
        this.reportYearMonth = reportYearMonth;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public PayReportMonthly totalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
        return this;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public Integer getTotalOrderCount() {
        return totalOrderCount;
    }

    public PayReportMonthly totalOrderCount(Integer totalOrderCount) {
        this.totalOrderCount = totalOrderCount;
        return this;
    }

    public void setTotalOrderCount(Integer totalOrderCount) {
        this.totalOrderCount = totalOrderCount;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        PayReportMonthly payReportMonthly = (PayReportMonthly) o;
        if (payReportMonthly.id == null || id == null) {
            return false;
        }
        return Objects.equals(id, payReportMonthly.id);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(id);
    }

    @Override
    public String toString() {
        return "PayReportMonthly{" +
            "id=" + id +
            ", communityGroupId='" + communityGroupId + "'" +
            ", communityId='" + communityId + "'" +
            ", serviceName='" + serviceName + "'" +
            ", reportYearMonth='" + reportYearMonth + "'" +
            ", totalAmount='" + totalAmount + "'" +
            ", totalOrderCount='" + totalOrderCount + "'" +
            '}';
    }
}
