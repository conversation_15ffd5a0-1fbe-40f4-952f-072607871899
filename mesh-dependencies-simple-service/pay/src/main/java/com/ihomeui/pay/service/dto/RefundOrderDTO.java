package com.ihomeui.pay.service.dto;
import io.swagger.annotations.ApiModelProperty;
import java.time.ZonedDateTime;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Objects;
import com.ihomeui.pay.domain.enumeration.RefundStatus;
import com.ihomeui.pay.domain.enumeration.RefundCallBackStatus;

/**
 * A DTO for the {@link com.ihomeui.pay.domain.RefundOrder} entity.
 */
public class RefundOrderDTO implements Serializable {

    private Long id;

    @NotNull
    @Size(max = 50)
    private String payOrderId;

    @NotNull
    @Size(max = 50)
    private String refundNo;

    @NotNull
    private BigDecimal refundAmount;

    @Size(max = 500)
    private String refundReason;

    @NotNull
    private RefundStatus status;

    @NotNull
    private Long userId;

    @NotNull
    private Long operateUserId;

    @NotNull
    private ZonedDateTime operateTime;

    @Size(max = 1024)
    private String returnMsg;

    @NotNull
    private RefundCallBackStatus callBackStatus;

    /**
     * 如果退款时有指定用这个返回 没有指定 默认使用 支付订单里面的值
     */
    @NotNull
    @Size(max = 255)
    @ApiModelProperty(value = "如果退款时有指定用这个返回 没有指定 默认使用 支付订单里面的值", required = true)
    private String businessId;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPayOrderId() {
        return payOrderId;
    }

    public void setPayOrderId(String payOrderId) {
        this.payOrderId = payOrderId;
    }

    public String getRefundNo() {
        return refundNo;
    }

    public void setRefundNo(String refundNo) {
        this.refundNo = refundNo;
    }

    public BigDecimal getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(BigDecimal refundAmount) {
        this.refundAmount = refundAmount;
    }

    public String getRefundReason() {
        return refundReason;
    }

    public void setRefundReason(String refundReason) {
        this.refundReason = refundReason;
    }

    public RefundStatus getStatus() {
        return status;
    }

    public void setStatus(RefundStatus status) {
        this.status = status;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getOperateUserId() {
        return operateUserId;
    }

    public void setOperateUserId(Long operateUserId) {
        this.operateUserId = operateUserId;
    }

    public ZonedDateTime getOperateTime() {
        return operateTime;
    }

    public void setOperateTime(ZonedDateTime operateTime) {
        this.operateTime = operateTime;
    }

    public String getReturnMsg() {
        return returnMsg;
    }

    public void setReturnMsg(String returnMsg) {
        this.returnMsg = returnMsg;
    }

    public RefundCallBackStatus getCallBackStatus() {
        return callBackStatus;
    }

    public void setCallBackStatus(RefundCallBackStatus callBackStatus) {
        this.callBackStatus = callBackStatus;
    }

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        RefundOrderDTO refundOrderDTO = (RefundOrderDTO) o;
        if (refundOrderDTO.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), refundOrderDTO.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    @Override
    public String toString() {
        return "RefundOrderDTO{" +
            "id=" + getId() +
            ", payOrderId='" + getPayOrderId() + "'" +
            ", refundNo='" + getRefundNo() + "'" +
            ", refundAmount=" + getRefundAmount() +
            ", refundReason='" + getRefundReason() + "'" +
            ", status='" + getStatus() + "'" +
            ", userId=" + getUserId() +
            ", operateUserId=" + getOperateUserId() +
            ", operateTime='" + getOperateTime() + "'" +
            ", returnMsg='" + getReturnMsg() + "'" +
            ", callBackStatus='" + getCallBackStatus() + "'" +
            ", businessId='" + getBusinessId() + "'" +
            "}";
    }
}
