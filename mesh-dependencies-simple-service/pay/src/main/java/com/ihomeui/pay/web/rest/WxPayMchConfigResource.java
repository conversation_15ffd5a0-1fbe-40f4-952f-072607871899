package com.ihomeui.pay.web.rest;

import com.alibaba.fastjson.JSONObject;
import io.micrometer.core.annotation.Timed;
import com.ihomeui.pay.domain.WxPayMchConfig;
import com.ihomeui.pay.domain.constant.PayConstants;
import com.ihomeui.pay.repository.WxPayMchConfigRepository;
import com.ihomeui.pay.service.PayConfigService;
import com.ihomeui.pay.web.rest.dto.PayConfigDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.inject.Inject;
import java.util.*;

/**
 * REST controller for managing WxPayMchConfig.
 */
@RestController
@RequestMapping("/api")
public class WxPayMchConfigResource {

    private final Logger log = LoggerFactory.getLogger(WxPayMchConfigResource.class);

    @Inject
    private PayConfigResource payConfigResource;

    @Inject
    private PayConfigService payConfigService;

    @Inject
    private WxPayMchConfigRepository wxPayMchConfigRepository;

    @PostMapping("/_wx-pay-mch-configs/community-group-config")
    @Timed
    public ResponseEntity<Void> saveCommunityGroupConfig(
        @RequestParam Long communityGroupId,
        @RequestParam String mchType,
        @RequestParam(required = false) String mchId,
        @RequestParam(required = false) String apiKey
    ) {
        PayConfigDTO payConfigDTO = new PayConfigDTO();
        payConfigDTO.setCommunityGroupId(communityGroupId);
        payConfigDTO.setOpenPayKind(PayConstants.OpenPayKind.WXPAY);
        payConfigDTO.setConfig(new HashMap<>());
        Map<String,Object> map = payConfigDTO.getConfig();
        map.put("mchType",mchType);
        map.put("mchId",mchId);
        map.put("apiKey",apiKey);

        payConfigDTO.setServiceName("pms");
        payConfigService.saveConfig(payConfigDTO);

        payConfigDTO.setServiceName("carpark");
        payConfigService.saveConfig(payConfigDTO);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/_wx-pay-mch-configs/community-group-config")
    @Timed
    public ResponseEntity<?> getCommunityGroupConfig(
        @RequestParam Long communityGroupId
    ) {
        List<PayConfigDTO> payConfigDTOS =
            payConfigResource.getConfigs(communityGroupId,null,null,null,null).getBody();
        if (payConfigDTOS!=null && payConfigDTOS.size() > 0){
            return ResponseEntity.ok(payConfigDTOS.get(0).getConfig());
        }else {
            return ResponseEntity.ok("{}");
        }
    }

    @PostMapping("/_wx-pay-mch-configs/community-config")
    @Timed
    public ResponseEntity<?> saveCommunityConfig(
        @RequestParam Long communityId,
        @RequestParam String mchType,
        @RequestParam(required = false) String mchId,
        @RequestParam(required = false) String apiKey
    ) {
        PayConfigDTO payConfigDTO = new PayConfigDTO();
        payConfigDTO.setCommunityId(communityId);
        payConfigDTO.setOpenPayKind(PayConstants.OpenPayKind.WXPAY);
        payConfigDTO.setConfig(new HashMap<>());
        Map<String,Object> map = payConfigDTO.getConfig();
        map.put("mchType",mchType);
        map.put("mchId",mchId);
        map.put("apiKey",apiKey);

        payConfigDTO.setServiceName("pms");
        payConfigService.saveConfig(payConfigDTO);

        payConfigDTO.setServiceName("carpark");
        payConfigService.saveConfig(payConfigDTO);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/_wx-pay-mch-configs/community-config")
    @Timed
    public ResponseEntity<?> getCommunityConfig(
        @RequestParam Long communityId
    ) {
        List<PayConfigDTO> payConfigDTOS =
            payConfigResource.getConfigs(null,communityId,null,null,null).getBody();
        if (payConfigDTOS!=null && payConfigDTOS.size() > 0){
            return ResponseEntity.ok(payConfigDTOS.get(0).getConfig());
        }else {
            return ResponseEntity.ok("{}");
        }
    }


    @PostMapping("/_wx-pay-mch-configs/platform-config")
    @Timed
    public ResponseEntity<Void> savePlatformConfig(
        @RequestParam String appId,
        @RequestParam String mchId,
        @RequestParam String apiKey
    ) {
        PayConfigDTO payConfigDTO = new PayConfigDTO();
        payConfigDTO.setAppId(appId);

        payConfigDTO.setOpenPayKind(PayConstants.OpenPayKind.WXPAY);
        payConfigDTO.setConfig(new HashMap<>());
        Map<String,Object> map = payConfigDTO.getConfig();
        map.put("mchType",PayConstants.WxMchType.GENERAL);
        map.put("mchId",mchId);
        map.put("appId",appId);
        map.put("apiKey",apiKey);

        payConfigService.saveConfig(payConfigDTO);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/_wx-pay-mch-configs/platform-config")
    @Timed
    public ResponseEntity<?> getPlatformConfig(
        @RequestParam String appId
    ) {
        List<PayConfigDTO> payConfigDTOS =
            payConfigResource.getConfigs(null,null,appId,null,null).getBody();
        if (payConfigDTOS!=null && payConfigDTOS.size() > 0){
            return ResponseEntity.ok(payConfigDTOS.get(0).getConfig());
        }else {
            return ResponseEntity.ok("{}");
        }
    }

//    @GetMapping("/_public/_wx-pay-mch-configs/save-all-to-pay-config")
//    @Timed
//    public ResponseEntity<?> saveAllToPayConfig() {
//        for (WxPayMchConfig wxPayMchConfig : wxPayMchConfigRepository.findAll()) {
//            if (PayConstants.WxMchType.SERVICE.equals(wxPayMchConfig.getMchType())) continue;
//
//            PayConfigDTO payConfigDTO = new PayConfigDTO();
//            BeanUtils.copyProperties(wxPayMchConfig,payConfigDTO);
//            payConfigDTO.setOpenPayKind(PayConstants.OpenPayKind.WXPAY);
//            JSONObject jsonObject = (JSONObject) JSONObject.toJSON(wxPayMchConfig);
//            payConfigDTO.setConfig(jsonObject);
//            if (wxPayMchConfig.getCommunityGroupId() == null && wxPayMchConfig.getCommunityId() == null) {
//                payConfigService.saveConfig(payConfigDTO);
//            }else {
//                payConfigDTO.setServiceName("pms");
//                payConfigService.saveConfig(payConfigDTO);
//
//                payConfigDTO.setServiceName("carpark");
//                payConfigService.saveConfig(payConfigDTO);
//            }
//        }
//        return ResponseEntity.ok().build();
//    }
}
