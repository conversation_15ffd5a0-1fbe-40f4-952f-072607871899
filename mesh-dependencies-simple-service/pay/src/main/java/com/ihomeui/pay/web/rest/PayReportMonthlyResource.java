package com.ihomeui.pay.web.rest;

import com.alibaba.fastjson.JSONObject;
import io.micrometer.core.annotation.Timed;
import com.ihomeui.common.utils.CriteriaParser;
import com.ihomeui.common.utils.MapUtil;
import com.ihomeui.mesh.service.JxlsFastExporter;
import com.ihomeui.mesh.service.JxlsService;
import com.ihomeui.mesh.web.rest.util.HeaderUtil;
import com.ihomeui.mesh.web.rest.util.PaginationUtil;
import com.ihomeui.pay.service.PayReportMonthlyService;
import com.ihomeui.pay.service.dto.PayReportMonthlyDTO;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.inject.Inject;
import java.net.URI;
import java.net.URISyntaxException;
import java.time.LocalDateTime;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Stream;

/**
 * REST controller for managing PayReportMonthly.
 */
@RestController
@RequestMapping("/api")
public class PayReportMonthlyResource {

    private final Logger log = LoggerFactory.getLogger(PayReportMonthlyResource.class);

    @Inject
    private PayReportMonthlyService payReportMonthlyService;

    @Inject
    private JxlsService jxlsService;

    /**
     * POST  /pay-report-monthlies : Create a new payReportMonthly.
     *
     * @param payReportMonthlyDTO the payReportMonthlyDTO to create
     * @return the ResponseEntity with status 201 (Created) and with body the new payReportMonthlyDTO, or with status 400 (Bad Request) if the payReportMonthly has already an ID
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PostMapping("/pay-report-monthlies")
    @Timed
    public ResponseEntity<PayReportMonthlyDTO> createPayReportMonthly(@RequestBody PayReportMonthlyDTO payReportMonthlyDTO) throws URISyntaxException {
        log.debug("REST request to save PayReportMonthly : {}", payReportMonthlyDTO);
        if (payReportMonthlyDTO.getId() != null) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert("payReportMonthly", "idexists", "A new payReportMonthly cannot already have an ID")).body(null);
        }
        PayReportMonthlyDTO result = payReportMonthlyService.save(payReportMonthlyDTO);
        return ResponseEntity.created(new URI("/api/pay-report-monthlies/" + result.getId()))
            .headers(HeaderUtil.createEntityCreationAlert("payReportMonthly", result.getId().toString()))
            .body(result);
    }

    /**
     * PUT  /pay-report-monthlies : Updates an existing payReportMonthly.
     *
     * @param payReportMonthlyDTO the payReportMonthlyDTO to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated payReportMonthlyDTO,
     * or with status 400 (Bad Request) if the payReportMonthlyDTO is not valid,
     * or with status 500 (Internal Server Error) if the payReportMonthlyDTO couldnt be updated
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PutMapping("/pay-report-monthlies")
    @Timed
    public ResponseEntity<PayReportMonthlyDTO> updatePayReportMonthly(@RequestBody PayReportMonthlyDTO payReportMonthlyDTO) throws URISyntaxException {
        log.debug("REST request to update PayReportMonthly : {}", payReportMonthlyDTO);
        if (payReportMonthlyDTO.getId() == null) {
            return createPayReportMonthly(payReportMonthlyDTO);
        }
        PayReportMonthlyDTO result = payReportMonthlyService.save(payReportMonthlyDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert("payReportMonthly", payReportMonthlyDTO.getId().toString()))
            .body(result);
    }

    /**
     * GET  /pay-report-monthlies : get all the payReportMonthlies.
     *
     * @param pageable the pagination information
     * @return the ResponseEntity with status 200 (OK) and the list of payReportMonthlies in body
     */
    @GetMapping("/pay-report-monthlies")
    @Timed
    public ResponseEntity<List<PayReportMonthlyDTO>> getAllPayReportMonthlies(
        @RequestParam(required = false) Long communityGroupId,
        @RequestParam(required = false) Long communityId,
        @RequestParam(required = false,defaultValue = "carpark") String serviceName,
        @RequestParam(required = false) String keywords,
        @RequestParam(required = false) String query,
        @ApiParam Pageable pageable
    ) {
        log.debug("REST request to get a page of PayReportMonthlies");
        Page<PayReportMonthlyDTO> page = payReportMonthlyService.findAll(new CriteriaParser()
            .andExpression(query)
            .andKeywords(keywords, "reportYearMonth")
            .andEqual("serviceName", serviceName)
            .andEqual("communityGroupId", communityGroupId)
            .andEqual("communityId", communityId)::toPredicate,pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/pay-report-monthlies");
        return new ResponseEntity<>(page.getContent(), headers, HttpStatus.OK);
    }

    /**
     * GET  /pay-report-monthlies/:id : get the "id" payReportMonthly.
     *
     * @param id the id of the payReportMonthlyDTO to retrieve
     * @return the ResponseEntity with status 200 (OK) and with body the payReportMonthlyDTO, or with status 404 (Not Found)
     */
    @GetMapping("/pay-report-monthlies/{id}")
    @Timed
    public ResponseEntity<PayReportMonthlyDTO> getPayReportMonthly(@PathVariable Long id) {
        log.debug("REST request to get PayReportMonthly : {}", id);
        PayReportMonthlyDTO payReportMonthlyDTO = payReportMonthlyService.findOne(id);
        return Optional.ofNullable(payReportMonthlyDTO)
            .map(result -> new ResponseEntity<>(
                result,
                HttpStatus.OK))
            .orElse(new ResponseEntity<>(HttpStatus.NOT_FOUND));
    }

    /**
     * DELETE  /pay-report-monthlies/:id : delete the "id" payReportMonthly.
     *
     * @param id the id of the payReportMonthlyDTO to delete
     * @return the ResponseEntity with status 200 (OK)
     */
    @DeleteMapping("/pay-report-monthlies/{id}")
    @Timed
    public ResponseEntity<Void> deletePayReportMonthly(@PathVariable Long id) {
        log.debug("REST request to delete PayReportMonthly : {}", id);
        payReportMonthlyService.delete(id);
        return ResponseEntity.ok().headers(HeaderUtil.createEntityDeletionAlert("payReportMonthly", id.toString())).build();
    }

    @PostMapping("/_pay-report-monthlies/build")
    @Timed
    public ResponseEntity<Void> build(
        @RequestParam(required = false) LocalDateTime start,
        @RequestParam(required = false) LocalDateTime end
    ) {
        payReportMonthlyService.build(start,end);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/_pay-report-monthlies/export")
    @Timed
    public ResponseEntity<?> export(
        @RequestParam(required = false) Long communityGroupId,
        @RequestParam(required = false) Long communityId,
        @RequestParam(required = false,defaultValue = "carpark") String serviceName,
        @RequestParam(required = false) String keywords,
        @RequestParam(required = false) String query,
        @ApiParam Pageable pageable
    ) {
        log.debug("REST request to get a page of PayReportMonthlies");
        Stream<Map<String,Object>> data = payReportMonthlyService.findAll(new CriteriaParser()
            .andExpression(query)
            .andKeywords(keywords, "reportYearMonth")
            .andEqual("serviceName", serviceName)
            .andEqual("communityGroupId", communityGroupId)
            .andEqual("communityId", communityId)::toPredicate,pageable.getSort()).stream().map(item-> (JSONObject)JSONObject.toJSON(item));

        JxlsFastExporter jxlsFastExporter = new JxlsFastExporter();
        jxlsFastExporter.setData(data);
        LinkedHashMap<String, String> title = new LinkedHashMap<>();

        title.put("reportYearMonth", "月份");
        title.put("totalOrderCount", "总订单数");
        title.put("totalAmount", "电子支付");

        jxlsFastExporter.setTitle(title);
        return Optional.ofNullable(jxlsService.exportAsync(jxlsFastExporter,"支付交易订单"))
            .map(fileKey->ResponseEntity.ok(MapUtil.create("fileKey", fileKey)))
            .orElseGet(()->ResponseEntity.badRequest().build());
    }

}
