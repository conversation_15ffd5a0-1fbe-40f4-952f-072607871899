package com.ihomeui.pay.web.util;

import com.alipay.api.internal.util.StreamUtil;
import org.apache.commons.lang3.StringUtils;

import java.io.*;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.*;
import java.util.Base64;

/**
 * @date 2017/7/21
 * @created caixy
 */
public class MyRsaSignUtil {

    public static String getSignContent(Map params) {
        StringBuilder content = new StringBuilder();
        List<String> keys = new ArrayList(params.keySet());
        Collections.sort(keys);
        int index = 0;
        for (String key : keys) {
            Object value = params.get(key);
            if(StringUtils.isNotEmpty(key)&&value!=null&&StringUtils.isNotEmpty(value.toString())) {
                content.append(index == 0 ? "" : "&")
                       .append(key)
                       .append("=")
                       .append(value.toString());
                ++index;
            }
        }
        return content.toString();
    }

    public static String rsaSign(Map map, String privateKey, String charset) throws Exception {
        String content = getSignContent(map);

        System.out.println(content);
        PrivateKey priKey = getPrivateKeyFromPKCS8("RSA", privateKey.getBytes());
        Signature signature = Signature.getInstance("SHA1WithRSA");
        signature.initSign(priKey);
        if(StringUtils.isEmpty(charset)) {
            signature.update(content.getBytes());
        } else {
            signature.update(content.getBytes(charset));
        }

        byte[] signed = signature.sign();
        return new String(Base64.getEncoder().encode(signed));
    }

    public static PrivateKey getPrivateKeyFromPKCS8(String algorithm, byte[] encodedKey) throws Exception {
        if(encodedKey != null && !StringUtils.isEmpty(algorithm)) {
            KeyFactory keyFactory = KeyFactory.getInstance(algorithm);
            encodedKey = Base64.getDecoder().decode(encodedKey);
            return keyFactory.generatePrivate(new PKCS8EncodedKeySpec(encodedKey));
        } else {
            return null;
        }
    }

    public static boolean rsaCheck(Map<String, String> params,String sign, String publicKey, String charSet) {
        return rsaCheckContent(getSignContent(params),sign,publicKey,charSet);
    }

    public static boolean rsaCheckContent(String content, String sign, String publicKey, String charset){
        try {
            PublicKey pubKey = getPublicKeyFromX509("RSA", new ByteArrayInputStream(publicKey.getBytes()));
            Signature signature = Signature.getInstance("SHA1WithRSA");
            signature.initVerify(pubKey);
            if(StringUtils.isEmpty(charset)) {
                signature.update(content.getBytes());
            } else {
                signature.update(content.getBytes(charset));
            }

            return signature.verify(Base64.getDecoder().decode(sign.getBytes()));
        } catch (Exception var6) {
            throw new RuntimeException("RSAcontent = " + content + ",sign=" + sign + ",charset = " + charset, var6);
        }
    }

    public static PublicKey getPublicKeyFromX509(String algorithm, InputStream ins) throws Exception {
        KeyFactory keyFactory = KeyFactory.getInstance(algorithm);
        StringWriter writer = new StringWriter();
        StreamUtil.io(new InputStreamReader(ins), writer);
        byte[] encodedKey = writer.toString().getBytes();
        encodedKey = com.alipay.api.internal.util.codec.Base64.decodeBase64(encodedKey);
        return keyFactory.generatePublic(new X509EncodedKeySpec(encodedKey));
    }
}
