package com.ihomeui.pay.client.mns;

import com.ihomeui.mesh.utils.StringUtils;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Collection;
import java.util.Objects;

/**
 * Created by XXM on 2016/12/13.
 */
public class MnsMessage implements Serializable {

    private Long id;

    private Long communityGroupId;

    private Long communityId;

    @Size(max = 64)
    private String relatedDataId;

    @Size(max = 255)
    private String relatedDataJson;

    @Size(max = 60)
    private String senderAliasName;

    private Long senderId;

    @Size(max = 60)
    private String templateCode;

    @Size(max = 100)
    private String title;

    @Size(max = 512)
    private String content;

    @Size(max = 512)
    private String toUsers;

    @Size(max = 512)
    private String toUserGroups;

    @Size(max = 512)
    private String toOffices;

    @Size(max = 512)
    private String toCommunitiesCustomer;

    @Size(max = 512)
    private String toCommunityGroupsCustomer;

    @Size(max = 512)
    private String toCommunitiesEmployee;

    @Size(max = 512)
    private String toCommunityGroupsEmployee;

    @Size(max = 512)
    private String remark;

    public MnsMessage(){}

    public Long getId() {
        return id;
    }
    public void setId(Long id) {
        this.id = id;
    }

    public Long getCommunityGroupId() {
        return communityGroupId;
    }
    public MnsMessage communityGroupId(Long communityGroupId) {
        this.communityGroupId = communityGroupId;
        return this;
    }
    public void setCommunityGroupId(Long communityGroupId) {
        this.communityGroupId = communityGroupId;
    }

    public Long getCommunityId() {
        return communityId;
    }
    public MnsMessage communityId(Long communityId) {
        this.communityId = communityId;
        return this;
    }
    public void setCommunityId(Long communityId) {
        this.communityId = communityId;
    }

    public String getRelatedDataId() {
        return relatedDataId;
    }
    public MnsMessage relatedDataId(String relatedDataId) {
        this.relatedDataId = relatedDataId;
        return this;
    }
    public MnsMessage relatedDataId(Long relatedDataId) {
        this.relatedDataId = (relatedDataId == null ? null : relatedDataId.toString());
        return this;
    }
    public void setRelatedDataId(String relatedDataId) {
        this.relatedDataId = relatedDataId;
    }

    public String getRelatedDataJson() {
        return relatedDataJson;
    }
    public MnsMessage relatedDataJson(String relatedDataJson) {
        this.relatedDataJson = relatedDataJson;
        return this;
    }
    public void setRelatedDataJson(String relatedDataJson) {
        this.relatedDataJson = relatedDataJson;
    }

    public String getSenderAliasName() {
        return senderAliasName;
    }
    public MnsMessage senderAliasName(String senderAliasName) {
        this.senderAliasName = senderAliasName;
        return this;
    }
    public void setSenderAliasName(String senderAliasName) {
        this.senderAliasName = senderAliasName;
    }

    public Long getSenderId() {
        return senderId;
    }
    public MnsMessage senderId(Long senderId) {
        this.senderId = senderId;
        return this;
    }
    public void setSenderId(Long senderId) {
        this.senderId = senderId;
    }

    public String getTemplateCode() {
        return templateCode;
    }
    public MnsMessage templateCode(String templateCode) {
        this.templateCode = templateCode;
        return this;
    }
    public void setTemplateCode(String templateCode) {
        this.templateCode = templateCode;
    }

    public String getTitle() {
        return title;
    }
    public MnsMessage title(String title) {
        this.title = title;
        return this;
    }
    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }
    public MnsMessage content(String content) {
        this.content = content;
        return this;
    }
    public void setContent(String content) {
        this.content = content;
    }

    public String getToUsers() {
        return toUsers;
    }
    public MnsMessage toUsers(Collection<Long> toUsers) {
        this.toUsers = StringUtils.join(toUsers, ",");
        return this;
    }
    public void setToUsers(Collection<Long> toUsers) {
        this.toUsers = StringUtils.join(toUsers, ",");
    }
    public MnsMessage toUsers(String toUsers) {
        this.toUsers = toUsers;
        return this;
    }
    public MnsMessage toUsers(Long toUser) {
        this.toUsers = (toUser == null ? null : toUser.toString());
        return this;
    }
    public void setToUsers(String toUsers) {
        this.toUsers = toUsers;
    }

    public String getToUserGroups() {
        return toUserGroups;
    }
    public MnsMessage toUserGroups(Collection<Long> toUserGroups) {
        this.toUserGroups = StringUtils.join(toUserGroups, ",");
        return this;
    }
    public void setToUserGroups(Collection<Long> toUserGroups) {
        this.toUserGroups = StringUtils.join(toUserGroups, ",");
    }
    public MnsMessage toUserGroups(String toUserGroups) {
        this.toUserGroups = toUserGroups;
        return this;
    }
    public void setToUserGroups(String toUserGroups) {
        this.toUserGroups = toUserGroups;
    }

    public String getToOffices() {
        return toOffices;
    }
    public MnsMessage toOffices(String toOffices) {
        this.toOffices = toOffices;
        return this;
    }
    public void setToOffices(String toOffices) {
        this.toOffices = toOffices;
    }
    public MnsMessage toOffices(Collection<Long> toOffices) {
        this.toOffices = StringUtils.join(toOffices, ",");
        return this;
    }
    public void setToOffices(Collection<Long> toOffices) {
        this.toOffices = StringUtils.join(toOffices, ",");
    }

    public String getToCommunitiesCustomer() {
        return toCommunitiesCustomer;
    }
    public MnsMessage toCommunitiesCustomer(String toCommunitiesCustomer) {
        this.toCommunitiesCustomer = toCommunitiesCustomer;
        return this;
    }
    public void setToCommunitiesCustomer(String toCommunitiesCustomer) {
        this.toCommunitiesCustomer = toCommunitiesCustomer;
    }

    public String getToCommunityGroupsCustomer() {
        return toCommunityGroupsCustomer;
    }
    public MnsMessage toCommunityGroupsCustomer(String toCommunityGroupsCustomer) {
        this.toCommunityGroupsCustomer = toCommunityGroupsCustomer;
        return this;
    }
    public void setToCommunityGroupsCustomer(String toCommunityGroupsCustomer) {
        this.toCommunityGroupsCustomer = toCommunityGroupsCustomer;
    }

    public String getToCommunitiesEmployee() {
        return toCommunitiesEmployee;
    }
    public MnsMessage toCommunitiesEmployee(String toCommunitiesEmployee) {
        this.toCommunitiesEmployee = toCommunitiesEmployee;
        return this;
    }
    public void setToCommunitiesEmployee(String toCommunitiesEmployee) {
        this.toCommunitiesEmployee = toCommunitiesEmployee;
    }

    public String getToCommunityGroupsEmployee() {
        return toCommunityGroupsEmployee;
    }
    public MnsMessage toCommunityGroupsEmployee(String toCommunityGroupsEmployee) {
        this.toCommunityGroupsEmployee = toCommunityGroupsEmployee;
        return this;
    }
    public void setToCommunityGroupsEmployee(String toCommunityGroupsEmployee) {
        this.toCommunityGroupsEmployee = toCommunityGroupsEmployee;
    }

    public String getRemark() {
        return remark;
    }
    public MnsMessage remark(String remark) {
        this.remark = remark;
        return this;
    }
    public void setRemark(String remark) {
        this.remark = remark;
    }

    //    public LocalDateTime getExpireTime() {
    //        return expireTime;
    //    }
    //    public MnsMessage expireTime(LocalDateTime expireTime) {
    //        this.expireTime = expireTime;
    //        return this;
    //    }
    //    public void setExpireTime(LocalDateTime expireTime) {
    //        this.expireTime = expireTime;
    //    }
    //
    //    public Integer getReadCount() {
    //        return readCount;
    //    }
    //    public MnsMessage readCount(Integer readCount) {
    //        this.readCount = readCount;
    //        return this;
    //    }
    //    public void setReadCount(Integer readCount) {
    //        this.readCount = readCount;
    //    }
    //
    //    public Boolean getClosed() {
    //        return closed;
    //    }
    //    public MnsMessage closed(Boolean closed) {
    //        this.closed = closed;
    //        return this;
    //    }
    //    public void setClosed(Boolean closed) {
    //        this.closed = closed;
    //    }
    //
    //    public Long getTemplateId() {
    //        return templateId;
    //    }
    //    public MnsMessage templateId(Long messageTemplateId) {
    //        this.templateId = messageTemplateId;
    //        return this;
    //    }
    //    public void setTemplateId(Long messageTemplateId) {
    //        this.templateId = messageTemplateId;
    //    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        MnsMessage mnsMessage = (MnsMessage) o;

        if ( ! Objects.equals(id, mnsMessage.id)) return false;

        return true;
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(id);
    }

    @Override
    public String toString() {
        return "MnsMessage{" +
            "id=" + id +
            ", communityGroupId='" + communityGroupId + "'" +
            ", communityId='" + communityId + "'" +
            ", relatedDataId='" + relatedDataId + "'" +
            ", relatedDataJson='" + relatedDataJson + "'" +
            ", senderAliasName='" + senderAliasName + "'" +
            ", senderId='" + senderId + "'" +
            ", templateCode='" + templateCode + "'" +
            ", toUsers='" + toUsers + "'" +
            ", toUserGroups='" + toUserGroups + "'" +
            ", toOffices='" + toOffices + "'" +
            ", toCommunitiesCustomer='" + toCommunitiesCustomer + "'" +
            ", toCommunityGroupsCustomer='" + toCommunityGroupsCustomer + "'" +
            ", toCommunitiesEmployee='" + toCommunitiesEmployee + "'" +
            ", toCommunityGroupsEmployee='" + toCommunityGroupsEmployee + "'" +
            ", remark='" + remark + "'" +
            '}';
    }
}
