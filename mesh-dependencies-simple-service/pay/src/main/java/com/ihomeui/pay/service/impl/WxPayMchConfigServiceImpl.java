package com.ihomeui.pay.service.impl;

import com.ihomeui.mesh.client.uaa.SimpleUaaRemoteService;
import com.ihomeui.mesh.utils.StringUtils;
import com.ihomeui.pay.client.weixin.WeixinRemoteService;
import com.ihomeui.pay.domain.constant.PayConstants;
import com.ihomeui.pay.service.WxPayMchConfigService;
import com.ihomeui.pay.domain.WxPayMchConfig;
import com.ihomeui.pay.repository.WxPayMchConfigRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Service;
import javax.inject.Inject;
import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Service Implementation for managing WxPayMchConfig.
 */
@Service
@Transactional
public class WxPayMchConfigServiceImpl implements WxPayMchConfigService{

    private final Logger log = LoggerFactory.getLogger(WxPayMchConfigServiceImpl.class);

    @Inject
    private WxPayMchConfigRepository wxPayMchConfigRepository;

    @Inject
    private WeixinRemoteService weixinRemoteService;

    @Inject
    private SimpleUaaRemoteService simpleUaaRemoteService;

    /**
     * Save a wxPayMchConfig.
     *
     * @param wxPayMchConfig the entity to save
     * @return the persisted entity
     */
    public WxPayMchConfig save(WxPayMchConfig wxPayMchConfig) {
        log.debug("Request to save WxPayMchConfig : {}", wxPayMchConfig);
        return wxPayMchConfigRepository.save(wxPayMchConfig);
    }

    /**
     *  Get all the wxPayMchConfigs.
     *
     *  @param pageable the pagination information
     *  @return the list of entities
     */
    @Transactional(readOnly = true)
    public Page<WxPayMchConfig> findAll(Pageable pageable) {
        log.debug("Request to get all WxPayMchConfigs");
        return wxPayMchConfigRepository.findAll(pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<WxPayMchConfig> findAll(Specification<WxPayMchConfig> specification, Pageable pageable) {
        return wxPayMchConfigRepository.findAll(specification,pageable);
    }

    /**
     *  Get one wxPayMchConfig by id.
     *
     *  @param id the id of the entity
     *  @return the entity
     */
    @Transactional(readOnly = true)
    public WxPayMchConfig findOne(Long id) {
        log.debug("Request to get WxPayMchConfig : {}", id);
        return wxPayMchConfigRepository.findById(id).orElse(null);
    }

    @Override
    @Transactional(readOnly = true)
    public WxPayMchConfig findOne(Specification<WxPayMchConfig> specification) {
        return wxPayMchConfigRepository.findOne(specification).orElse(null);
    }

    /**
     *  Delete the  wxPayMchConfig by id.
     *
     *  @param id the id of the entity
     */
    public void delete(Long id) {
        log.debug("Request to delete WxPayMchConfig : {}", id);
        wxPayMchConfigRepository.deleteById(id);
    }

    /**
     * 支持配置场景
     * 应用级别（公众号级别小程序使用绑定公众号的支付配置）
     * 平台级别（只有一种选择后台数据库配置）
     * 集团级别 > 小区级别 > 应用级别（集团支付配置集团之下还有小区级别，集团应用级别）
     * @param communityGroupId
     * @param communityId
     * @param appId
     * @param platformPay
     * @return
     */
    @Override
    @Transactional(readOnly = true)
    public WxPayMchConfig getWxPayMchConfig(Long communityGroupId, Long communityId, String appId, Boolean platformPay) {
        WxPayMchConfig wxPayMchConfig = null;
        if ( appId != null ) appId = weixinRemoteService.getParentAppId(appId);
        if ( platformPay != null && platformPay ) {
            return wxPayMchConfigRepository.findFirstByPlatformPayIsTrue();
        } else if ( communityGroupId != null ){
            List<WxPayMchConfig> wxPayMchConfigs = wxPayMchConfigRepository.findAll((root, cq, cb) -> {
                List<Predicate> predicates = new ArrayList<>();
                if (communityId == null) {
                    predicates.add(cb.isNull(root.get("communityId")));
                }
                predicates.add(cb.equal(root.get("communityGroupId"), communityGroupId));
                return cb.and(predicates.toArray(new Predicate[0]));
            }).stream().filter(c-> StringUtils.isNotBlank(c.getMchId())).collect(Collectors.toList());

            //集团级别配置
            for (WxPayMchConfig wxPayMchConfig_ : wxPayMchConfigs) {
                if ( wxPayMchConfig_.getCommunityId() == null ) {
                    wxPayMchConfig = wxPayMchConfig_;
                    break;
                }
            }
            //小区级别配置
            if (communityId != null){
                for (WxPayMchConfig wxPayMchConfig_ : wxPayMchConfigs) {
                    if ( communityId.equals(wxPayMchConfig_.getCommunityId()) ) {
                        wxPayMchConfig = wxPayMchConfig_;
                        break;
                    }
                }
            }
            //应用级别配置
            if ( appId != null ) {
                for (WxPayMchConfig wxPayMchConfig_ : wxPayMchConfigs) {
                    if ( appId.equals(wxPayMchConfig_.getAppId()) ) {
                        wxPayMchConfig = wxPayMchConfig_;
                        break;
                    }
                }
            }
        }
        if (wxPayMchConfig == null && appId != null) {
            wxPayMchConfig = getPlatformAppConfig(appId);
        }
        if (wxPayMchConfig != null && appId != null && ( communityGroupId != null || communityId != null ) ){
            Set<Long> communityGroupIds = weixinRemoteService.getBindCommunityGroupIds(appId);
            Set<Long> communityIds = weixinRemoteService.getBindCommunityIds(appId);
            if (communityGroupIds.contains(communityGroupId) || communityIds.contains(communityId)){
                return wxPayMchConfig;
            } else {
                return null;
            }
        }
        return wxPayMchConfig;
    }

    @Override
    public WxPayMchConfig saveCommunityGroupConfig(Long communityGroupId, String mchType, String mchId, String apiKey) {
        WxPayMchConfig wxPayMchConfig = getCommunityGroupConfig(communityGroupId);
        if (wxPayMchConfig == null){
            wxPayMchConfig = new WxPayMchConfig();
        }
        wxPayMchConfig.setCommunityGroupId(communityGroupId);
        wxPayMchConfig.setCommunityId(null);
        wxPayMchConfig.setMchType(mchType);
        wxPayMchConfig.setMchId(mchId);
        wxPayMchConfig.setApiKey(apiKey);

        if (PayConstants.WxMchType.SUB_MCH.equals(mchType)) wxPayMchConfig.setServiceMch(getServiceMch());
        return wxPayMchConfigRepository.save(wxPayMchConfig);
    }

    @Override
    public WxPayMchConfig savePlatformAppConfig(String appId, String mchId, String apiKey) {
        WxPayMchConfig wxPayMchConfig = getPlatformAppConfig(appId);
        if (wxPayMchConfig == null){
            wxPayMchConfig = new WxPayMchConfig();
        }
        wxPayMchConfig.mchType(PayConstants.WxMchType.GENERAL)
                    .mchId(mchId)
                    .communityGroupId(null)
                    .communityId(null)
                    .apiKey(apiKey)
                    .appId(appId)
                    .platformPay(false);
        return wxPayMchConfigRepository.save(wxPayMchConfig);
    }

    @Override
    @Transactional(readOnly = true)
    public WxPayMchConfig getPlatformAppConfig(String appId) {
        return wxPayMchConfigRepository.findOne((root, cq, cb) -> cb.and(
            cb.equal(root.get("appId"),appId),
            cb.isNull(root.get("communityId")),
            cb.isNull(root.get("communityGroupId")),
            cb.equal(root.get("mchType"),PayConstants.WxMchType.GENERAL),
            cb.isFalse(root.get("platformPay"))
        )).orElse(null);
    }

    @Override
    @Transactional(readOnly = true)
    public WxPayMchConfig getCommunityGroupConfig(Long communityGroupId) {
        return wxPayMchConfigRepository.findOne((root, cq, cb) -> cb.and(
            cb.equal(root.get("communityGroupId"),communityGroupId),
            cb.isNull(root.get("appId")),
            cb.isNull(root.get("communityId")),
            cb.isFalse(root.get("platformPay"))
        )).orElse(null);
    }

    @Override
    @Transactional(readOnly = true)
    public WxPayMchConfig getAppConfig(String appId) {
        return wxPayMchConfigRepository.findOne((root, cq, cb) -> cb.and(
            cb.equal(root.get("appId"),appId),
            cb.isNull(root.get("communityId")),
            cb.equal(root.get("mchType"),PayConstants.WxMchType.GENERAL),
            cb.isFalse(root.get("platformPay"))
        )).orElse(null);
    }

    @Override
    @Transactional(readOnly = true)
    public WxPayMchConfig getCommunityConfig(Long communityId) {
        return wxPayMchConfigRepository.findOne((root, cq, cb) -> cb.and(
            cb.equal(root.get("communityId"),communityId),
            cb.isNull(root.get("appId")),
            cb.isFalse(root.get("platformPay"))
        )).orElse(null);
    }

    @Override
    public WxPayMchConfig saveCommunityConfig(Long communityId, String mchType, String mchId, String apiKey) {
        WxPayMchConfig wxPayMchConfig = getCommunityConfig(communityId);
        if (wxPayMchConfig == null){
            wxPayMchConfig = new WxPayMchConfig();
        }
        wxPayMchConfig.setCommunityGroupId(simpleUaaRemoteService.getAssignedCommunityGroupId(communityId));
        wxPayMchConfig.setCommunityId(communityId);
        wxPayMchConfig.setMchType(mchType);
        wxPayMchConfig.setMchId(mchId);
        if (PayConstants.WxMchType.SUB_MCH.equals(mchType)) wxPayMchConfig.setServiceMch(getServiceMch());

        wxPayMchConfig.setApiKey(apiKey);
        return wxPayMchConfigRepository.save(wxPayMchConfig);
    }

    @Transactional(readOnly = true)
    public WxPayMchConfig getServiceMch() {
        return wxPayMchConfigRepository.findOne((root, cq, cb) -> cb.and(
            cb.equal(root.get("mchType"),PayConstants.WxMchType.SERVICE),
            cb.isNull(root.get("communityGroupId")),
            cb.isNull(root.get("communityId")),
            cb.isFalse(root.get("platformPay"))
        )).orElse(null);
    }

    @Override
    public WxPayMchConfig saveAppConfig(Long communityGroupId, String mchId, String apiKey, String appId) {
        WxPayMchConfig wxPayMchConfig = getAppConfig(appId);
        if (wxPayMchConfig == null){
            wxPayMchConfig = new WxPayMchConfig();
        }
        wxPayMchConfig.setAppId(appId);
        wxPayMchConfig.setCommunityGroupId(communityGroupId);
        wxPayMchConfig.setCommunityId(null);
        wxPayMchConfig.setMchType(PayConstants.WxMchType.GENERAL);
        wxPayMchConfig.setMchId(mchId);
        wxPayMchConfig.setApiKey(apiKey);
        return wxPayMchConfigRepository.save(wxPayMchConfig);
    }
}
