package com.ihomeui.pay.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;

@Configuration("paySecurityConfiguration")
public class CustomMicroserviceSecurityConfiguration extends MicroserviceSecurityConfiguration {

    @Override
    public void configure(HttpSecurity http) throws Exception {
        super.configure(http);
    }
}
