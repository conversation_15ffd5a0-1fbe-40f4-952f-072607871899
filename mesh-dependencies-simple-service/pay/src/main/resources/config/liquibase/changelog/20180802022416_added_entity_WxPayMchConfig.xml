<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.4.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <property name="now" value="now()" dbms="mysql,h2"/>
    <property name="now" value="current_timestamp" dbms="postgresql"/>
    <property name="now" value="sysdate" dbms="oracle"/>
    <property name="now" value="GETDATE()" dbms="mssql"/>

    <property name="autoIncrement" value="true" dbms="mysql,h2,postgresql,oracle,mssql"/>

    <property name="floatType" value="float4" dbms="postgresql, h2"/>
    <property name="floatType" value="float" dbms="mysql, oracle, mssql"/>

    <!--
        Added the entity WxPayMchConfig.
    -->
    <changeSet id="20180802022416-1" author="jhipster">
        <createTable tableName="wx_pay_mch_config">
            <column name="id" type="bigint" autoIncrement="${autoIncrement}">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="community_group_id" type="bigint">
                <constraints nullable="true" />
            </column>

            <column name="community_ids" type="bigint">
                <constraints nullable="true" />
            </column>

            <column name="app_id" type="varchar(50)">
                <constraints nullable="false" />
            </column>

            <column name="app_name" type="varchar(400)">
                <constraints nullable="true" />
            </column>

            <column name="mch_type" type="varchar(50)">
                <constraints nullable="false" />
            </column>

            <column name="mch_id" type="varchar(80)">
                <constraints nullable="false" />
            </column>

            <column name="api_key" type="varchar(200)">
                <constraints nullable="true" />
            </column>

            <column name="service_mch_id" type="bigint">
                <constraints nullable="true" />
            </column>

            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here, do not remove-->
        </createTable>

    </changeSet>

    <changeSet id="2018080610594343-2" author="caixy">
        <modifyDataType tableName="wx_pay_mch_config" columnName="community_ids" newDataType="varchar(1024)"/>
    </changeSet>

    <changeSet id="201809290953555-3" author="caixy">
        <dropNotNullConstraint tableName="wx_pay_mch_config" columnName="app_id" columnDataType="varchar(50)"/>
    </changeSet>

    <changeSet id="201810290953566-4" author="caixy">
        <addColumn tableName="wx_pay_mch_config">
            <column name="platform_pay" type="bit"/>
        </addColumn>
    </changeSet>

    <changeSet id="201810290953577-5" author="caixy">
        <renameColumn tableName="wx_pay_mch_config" oldColumnName="community_ids" newColumnName="community_id" columnDataType="bigint"/>
    </changeSet>

    <changeSet id="201811081348787-6" author="caixy">
        <sql>
            update wx_pay_mch_config set platform_pay = 0 where platform_pay is null
        </sql>
    </changeSet>

    <changeSet id="201811121921566-7" author="caixy">
        <dropNotNullConstraint tableName="wx_pay_mch_config" columnName="mch_id" columnDataType="varchar(80)"/>
    </changeSet>

</databaseChangeLog>
