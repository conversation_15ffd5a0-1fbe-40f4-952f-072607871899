<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.6.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <property name="now" value="now()" dbms="h2"/>

    <property name="now" value="now()" dbms="mysql"/>
    <property name="autoIncrement" value="true"/>

    <property name="floatType" value="float4" dbms="postgresql, h2"/>
    <property name="floatType" value="float" dbms="mysql, oracle, mssql, mariadb"/>

    <!--
        Added the entity RefundOrder.
    -->
    <changeSet id="20200210081566-1" author="jhipster">
        <createTable tableName="refund_order">
            <column name="id" type="bigint" autoIncrement="${autoIncrement}">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="pay_order_id" type="varchar(50)">
                <constraints nullable="false" />
            </column>

            <column name="refund_no" type="varchar(50)">
                <constraints nullable="false" />
            </column>

            <column name="refund_amount" type="decimal(21,2)">
                <constraints nullable="false" />
            </column>

            <column name="refund_reason" type="varchar(500)" >
                <constraints nullable="true" />
            </column>

            <column name="status" type="varchar(20)">
                <constraints nullable="false" />
            </column>

            <column name="operate_time" type="datetime">
                <constraints nullable="false" />
            </column>

            <column name="user_id" type="bigint">
                <constraints nullable="false" />
            </column>

            <column name="operate_user_id" type="bigint">
                <constraints nullable="false" />
            </column>

            <column name="call_back_status" type="varchar(20)">
                <constraints nullable="false" />
            </column>

            <column name="business_id" type="varchar(255)" remarks="如果退款时有指定用这个返回 没有指定 默认使用 支付订单里面的值">
                <constraints nullable="false" />
            </column>

            <column name="return_msg" type="varchar(1024)" >
                <constraints nullable="true" />
            </column>

            <column name="result_code" type="varchar(100)" >
                <constraints nullable="true" />
            </column>

            <column name="result_body" type="varchar(2048)" >
                <constraints nullable="true" />
            </column>

            <column name="result_message" type="varchar(500)" >
                <constraints nullable="true" />
            </column>

            <column name="call_back_response_body" type="varchar(500)" >
                <constraints nullable="true" />
            </column>

            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here, do not remove-->
        </createTable>

        <dropDefaultValue tableName="refund_order" columnName="operate_time" columnDataType="datetime"/>
    </changeSet>

    <changeSet id="20200503160501323-2" author="caixy">
        <dropNotNullConstraint tableName="refund_order" columnName="operate_user_id" columnDataType="bigint"/>
    </changeSet>

    <changeSet id="20200503160501344-2" author="caixy">
        <addColumn tableName="refund_order">
            <column name="down_time" type="datetime"/>
        </addColumn>
    </changeSet>

</databaseChangeLog>
