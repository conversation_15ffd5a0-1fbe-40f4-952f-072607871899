# ===================================================================
# Spring Boot configuration.
# ===================================================================

eureka:
    client:
        enabled: true
        healthcheck:
            enabled: true
        fetch-registry: true
        register-with-eureka: true
        instance-info-replication-interval-seconds: 10
        registry-fetch-interval-seconds: 10
    instance:
        appname: jmt-advertising
        instanceId: jmt-advertising:${spring.application.instance-id:${random.value}}
        lease-renewal-interval-in-seconds: 5
        lease-expiration-duration-in-seconds: 10
        status-page-url-path: ${management.endpoints.web.base-path}/info
        health-check-url-path: ${management.endpoints.web.base-path}/health
        metadata-map:
            zone: primary # This is needed for the load balancer
            profile: ${spring.profiles.active}
            version: ${info.project.version}
            git-version: ${git.commit.id.describe:}
            git-commit: ${git.commit.id.abbrev:}
            git-branch: ${git.branch:}
# 负载均衡
ribbon:
    eureka:
        enabled: true
    ReadTimeout: 60000
    ConnectTimeout: 60000
# 微服务客户端
feign:
    hystrix:
        enabled: true
    client:
        config:
            default:
                connectTimeout: 60000 # feign 的超时设置
                readTimeout: 60000

# 熔断器
hystrix:
    command:
        default:
            execution:
                isolation:
                    strategy: SEMAPHORE
                    thread:
                        timeoutInMilliseconds: 60000
                timeout:
                    enabled: false
    shareSecurityContext: true

spring:
    application:
        name: jmt-advertising
    jmx:
        enabled: false
    data:
        jpa:
            repositories:
                bootstrap-mode: deferred
    ## quartz定时任务,采用数据库方式
    quartz:
        job-store-type: jdbc
#    jpa:
#        open-in-view: true
#        hibernate:
#            ddl-auto: none
#            naming:
#                physical-strategy: org.springframework.boot.orm.jpa.hibernate.SpringPhysicalNamingStrategy
#                implicit-strategy: org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy
    messages:
        basename: i18n/messages # 全球化文本定义文件
    main:
        allow-bean-definition-overriding: true # 允许bean覆盖
        lazy-initialization : true   # 开启懒加载
    mvc:
        favicon:
            enabled: false
    task:
        execution:
            thread-name-prefix: jmt-advertising-task-
            pool:
                core-size: 2 # 核心异步任务池数量
                max-size: 50 # 最大数量
                queue-capacity: 10000
        scheduling:
            thread-name-prefix: jmt-advertising-scheduling-
            pool:
                size: 2 # 定时任务线程数量
    thymeleaf:
        mode: HTML

    jackson:
        # 忽略null字段 不返回前端
        #defaultPropertyInclusion: NON_NULL
        # 忽略未知字段
        deserialization:
            FAIL_ON_UNKNOWN_PROPERTIES: false
        parser:
            ALLOW_UNQUOTED_CONTROL_CHARS: true
        #serialization:
            #WRITE_DATES_AS_TIMESTAMPS: false
    #json 时间戳统一转换
        date-format: yyyy-MM-dd HH:mm:ss
        time-zone: GMT+8
    servlet:
        multipart:
            max-file-size: 100MB
            max-request-size: 100MB
    redis:
        port: 6379
        hostname: r-bp1gzx5dw9ip4x11yppd.redis.rds.aliyuncs.com
        host: r-bp1gzx5dw9ip4x11yppd.redis.rds.aliyuncs.com
        password: jmt#9901#JMT
        database: 6
#        port: 6379
#        hostname: r-bp1gzx5dw9ip4x11yp.redis.rds.aliyuncs.com
#        host: r-bp1gzx5dw9ip4x11yp.redis.rds.aliyuncs.com
#        password: jmt#9901#JMT
#        database: 6
#        host: localhost
management:
    endpoints:
        web:
            base-path: /management
            exposure:
                include: ['health', 'info']

security:
    oauth2:
        resource:
            jwt:
                key-uri: http://uaa/oauth/token_key

        client:
            access-token-uri: http://uaa/oauth/token
            grant-type: client_credentials
            client-id: internal
            client-secret: internal

server:
    servlet:
        session:
            cookie:
                http-only: true

# Properties to be exposed on the /info management endpoint
info:
    # Comma separated list of profiles that will trigger the ribbon to show
    display-ribbon-on-profiles: 'dev'

mesh:
    swagger:
        default-include-pattern: /api/.*
        title: jmt-advertising API
        description: jmt-advertising API documentation
        version: ${mesh.application.version}
        terms-of-service-url:
        contact-name:
        contact-url:
        contact-email:
        license:
        license-url:
    cache:
        hazelcast:
            managementCenter:
                enabled: true
                url: http://localhost:8080/hazelcast-mancenter
    mqtt:
        urls: tcp://**************:1883
        user-name: admin
        pass-word: public
        client-id: ${spring.application.name}:${server.port}

# application:
mybatis-plus:
    mapper-locations: classpath:**/*Mapper.xml
    global-config:
        # 关闭MP3.0自带的banner
        banner: false
        db-config:
            #主键类型  0:"数据库ID自增",1:"该类型为未设置主键类型", 2:"用户输入ID",3:"全局唯一ID (数字类型唯一ID)", 4:"全局唯一ID UUID",5:"字符串全局唯一ID (idWorker 的字符串表示)";
            id-type: 4
            # 默认数据库表下划线命名
            table-underline: true
    configuration:
        log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

#jmt配置
jmt:
    #阿里云oss存储配置
    oss:
        endpoint: oss-cn-hangzhou.aliyuncs.com
        accessKey: LTAI5tL1eBoJBbtTDNE7Bg6o
        secretKey: ******************************
        bucketName: jmt-video
        fileDir: file/video
        staticDomain: https://video.resources.jmingt.com
integral:
    integralToMoney: 100
