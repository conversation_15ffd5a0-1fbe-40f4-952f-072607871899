<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.modules.system.mapper.SysLogMapper">

	<!-- 清空所有日志记录 -->
	<delete id="removeAll">
		DELETE FROM sys_log
	</delete>
	
	<!-- 获取访问总数 -->
	<select id="findTotalVisitCount" resultType="long">
        select count(1) from sys_log where log_type = 1
    </select>

	<!-- 获取今日访问总数 -->
    <select id="findTodayVisitCount" resultType="long">
        select count(1) from sys_log where log_type = 1 and create_time &gt;= #{dayStart} and create_time &lt; #{dayEnd}
    </select>
    
	<!-- 获取今日访问总IP数 -->
    <select id="findTodayIp" resultType="long">
        select count(distinct(ip)) from sys_log where log_type = 1 and create_time &gt;= #{dayStart} and create_time &lt; #{dayEnd}
    </select>
    
   	<!-- 首页访问统计 -->
    <select id="findVisitCount" resultType="java.util.HashMap">
        select count(*) as visit,
        	   count(distinct(ip)) as ip,
        	   DATE_FORMAT(create_time, '%Y-%m-%d') as tian,
        	   DATE_FORMAT(create_time, '%m-%d') as type
         from sys_log 
         where log_type = 1 and create_time &gt;= #{dayStart} and create_time &lt; #{dayEnd}
         group by tian,type
         order by tian asc
    </select>

</mapper>
