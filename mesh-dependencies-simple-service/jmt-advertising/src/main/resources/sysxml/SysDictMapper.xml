<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.modules.system.mapper.SysDictMapper">

	<!-- 通过字典code获取字典数据 -->
	<select id="queryDictItemsByCode" parameterType="String"  resultType="com.jmt.common.system.vo.DictModel">
		   select s.item_value as value,s.item_text as text from sys_dict_item s 
		   where dict_id = (select id from sys_dict where dict_code = #{code})
		   order by s.sort_order asc
	</select>
	
	<!-- 通过字典code获取字典数据 -->
	<select id="queryDictTextByKey" parameterType="String"  resultType="String">
		   select s.item_text from sys_dict_item s 
		   where s.dict_id = (select id from sys_dict where dict_code = #{code})
		   and s.item_value = #{key}
	</select>


	<!--通过查询指定table的 text code 获取字典-->
	<select id="queryTableDictItemsByCode" parameterType="String"  resultType="com.jmt.common.system.vo.DictModel">
		   select ${text} as 'text',${code} as 'value' from ${table}
	</select>
	
	<!--通过查询指定table的 text code 获取字典（指定查询条件）-->
	<select id="queryTableDictItemsByCodeAndFilter" parameterType="String"  resultType="com.jmt.common.system.vo.DictModel">
		   select ${text} as 'text',${code} as 'value' from ${table} where ${filterSql}
	</select>
	
	<!--通过查询指定table的 text code key 获取字典值-->
	<select id="queryTableDictTextByKey" parameterType="String"  resultType="String">
		   select ${text} as 'text' from ${table} where ${code}= #{key}
	</select>

	<!-- 重复校验 sql语句 -->
	<select id="duplicateCheckCountSql" resultType="Long" parameterType="com.jmt.modules.system.model.DuplicateCheckVo">
		SELECT COUNT(*) FROM ${tableName} WHERE ${fieldName} = #{fieldVal} and id &lt;&gt; #{dataId}
	</select>
	
	<!-- 重复校验 sql语句 -->
	<select id="duplicateCheckCountSqlNoDataId" resultType="Long" parameterType="com.jmt.modules.system.model.DuplicateCheckVo">
		SELECT COUNT(*) FROM ${tableName} WHERE ${fieldName} = #{fieldVal}
	</select>
		
	<!-- 查询部门信息 作为字典数据 -->
	<select id="queryAllDepartBackDictModel" resultType="com.jmt.common.system.vo.DictModel">
		select id as value,depart_name as text from sys_depart where del_flag = '0'
	</select>
	
		<!-- 查询部门信息 作为字典数据 -->
	<select id="queryAllUserBackDictModel" resultType="com.jmt.common.system.vo.DictModel">
		select username as value,realname as text from sys_depart where del_flag = '0'
	</select>
	
	<!--通过查询指定table的 text code 获取字典数据，且支持关键字查询 -->
	<select id="queryTableDictItems" parameterType="String"  resultType="com.jmt.common.system.vo.DictModel">
		select ${text} as 'text',${code} as 'value' from ${table} where ${text} like #{keyword}
	</select>
	
	<!-- 根据表名、显示字段名、存储字段名、父ID查询树 -->
	<select id="queryTreeList" parameterType="String"  resultType="com.jmt.modules.system.model.TreeSelectModel">
		select ${text} as 'title',
			   ${code} as 'key',
			   <if test="hasChildField != null and hasChildField != ''">
			   (case ${hasChildField} when '1' then 0 else 1 end) as 'isLeaf',
			   </if>
			   ${pidField} as parentId
			   from ${table} where ${pidField} = #{pid}
	</select>
	
	
</mapper>
