<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.modules.system.mapper.SysUserMapper">

	<!-- 根据用户名查询 -->
	<select id="getUserByName" resultType="com.jmt.modules.system.entity.SysUser">
		select * from  sys_user  where username = #{username} and del_flag = '0'
	</select>

	<!-- 根据部门Id查询 -->
	<select id="getUserByDepId" resultType="com.jmt.modules.system.entity.SysUser">
		select * from sys_user where del_flag = '0' and id in (select user_id from sys_user_depart where dep_id=#{departId})
		<if test="username!=null and username!=''">
			and username = #{username}
		</if>
	</select>

	<!-- 根据角色Id查询 -->
	<select id="getUserByRoleId" resultType="com.jmt.modules.system.entity.SysUser">
		select * from sys_user where del_flag = '0' and id in (select user_id from sys_user_role where role_id=#{roleId})
		<if test="username!=null and username!=''">
			and username = #{username}
		</if>
	</select>
	
	<!--  修改用户部门code -->
	<update id="updateUserDepart">
		UPDATE sys_user SET org_code = #{orgCode} where username = #{username}
	</update>

	<!-- 根据手机号查询 -->
	<select id="getUserByPhone"  resultType="com.jmt.modules.system.entity.SysUser">
		select * from  sys_user  where phone = #{phone} and del_flag = '0'
	</select>
	
	<!-- 根据邮箱查询用户信息 -->
	<select id="getUserByEmail" resultType="com.jmt.modules.system.entity.SysUser">
	select * from  sys_user  where email = #{email} and del_flag = '0'
	</select>
	
</mapper>