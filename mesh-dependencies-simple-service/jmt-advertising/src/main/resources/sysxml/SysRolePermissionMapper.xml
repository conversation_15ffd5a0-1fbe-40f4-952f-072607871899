<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.modules.system.mapper.SysRolePermissionMapper">

	<select id="listSysPermissionByRoleId" resultType="com.jmt.modules.system.entity.SysPermission">
			SELECT * FROM sys_role_permission p,sys_permission p1
			WHERE p.`permission_id`=p1.`id`
			AND role_id=#{roleId}
			AND p1.`menu_type`=#{menuType}
	</select>

</mapper>
