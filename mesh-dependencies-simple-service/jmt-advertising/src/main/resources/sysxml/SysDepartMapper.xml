<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.modules.system.mapper.SysDepartMapper">

	<select id="queryUserDeparts" parameterType="String" resultType="com.jmt.modules.system.entity.SysDepart">
		select a.* from sys_depart a
			join sys_user_depart b on a.id = b.dep_id
			where b.user_id = #{userId}
	</select>
</mapper>