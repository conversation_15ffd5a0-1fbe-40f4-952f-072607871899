<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.modules.workOrder.mapper.PlayVideoMapper">

    <resultMap id="playVideoMap" type="com.jmt.modules.workOrder.entity.PlayVideo">
        <result property="id" column="ID" />
        <result property="filmName" column="FILM_NAME" />
        <result property="filmDuration" column="FILM_DURATION" />
        <result property="filmEndTime" column="FILM_END_TIME" />
        <result property="areaProvinceNum" column="AD_AREA_PROVINCE_NUM" />
        <result property="areaCityNum" column="AD_AREA_CITY_NUM" />
        <result property="areaCountyNum" column="AD_AREA_COUNTY_NUM" />
        <result property="eqType" column="AD_EQ_TYPE" />
        <result property="bCode" column="B_CODE" />
        <result property="eqCode" column="EQ_CODE" />
        <result property="filmUrl" column="FILM_URL" />
        <result property="filmId" column="FILM_ID" />
        <result property="filmMd5" column="FILM_MD5" />
        <result property="filmStartTime" column="FILM_START_TIME" />
        <result property="filmType" column="FILM_TYPE" />
        <result property="filmPutType" column="FILM_PUTTYPE" />
        <result property="imgUrl" column="IMG_URL" />
        <result property="playStatus" column="PLAY_STATUS" />
    </resultMap>



    <select id="todayPlayList" resultMap="playVideoMap">
        select s.*,f.film_id FILM_ID from ad_play_list  s left join film_library f on f.FILM_URL = s.film_url
        where 1=1
        and  PLAY_STATUS = 0
        and str_to_date(s.FILM_START_TIME,'%Y-%m-%d') = str_to_date(NOW(),'%Y-%m-%d')  GROUP BY s.id
    </select>

    <select id="todayPlayImageList" resultMap="playVideoMap">
        SELECT s.*, f.film_id FILM_ID FROM ad_play_list s LEFT JOIN film_library f ON f.FILM_URL = s.film_url WHERE
	        1 = 1
	        AND f.FILM_TYPE = 9
	AND str_to_date( s.FILM_START_TIME, '%Y-%m-%d' ) = str_to_date( NOW( ), '%Y-%m-%d' )
GROUP BY
	s.id
    </select>


    <update id="playVideoExpire">
         update ad_play_list
         set
           PLAY_STATUS = 2
        where  DATE_FORMAT( FILM_END_TIME,'%Y-%m-%d') = DATE_FORMAT(CURDATE(),'%Y-%m-%d')
    </update>



    <delete id="deleteAllPlayVideo">
        delete  from ad_play_list where  FILM_MD5 in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!--新增所有列-->
    <insert id="insertPlayVideo" keyProperty="id" useGeneratedKeys="true">
        insert into ad_play_list(FILM_NAME, FILM_DURATION, FILM_END_TIME, AD_AREA_PROVINCE_NUM, AD_AREA_CITY_NUM, AD_AREA_COUNTY_NUM, AD_EQ_TYPE, B_CODE, EQ_CODE, FILM_URL, FILM_MD5, FILM_START_TIME, FILM_TYPE, FILM_PUTTYPE, IMG_URL, PLAY_STATUS)
        values (#{filmName}, #{filmDuration}, #{filmEndTime}, #{areaProvinceNum}, #{areaCityNum}, #{areaCountyNum}, #{eqType}, #{bCode}, #{eqCode}, #{filmUrl}, #{filmMd5}, #{filmStartTime}, #{filmType}, #{filmPutType}, #{imgUrl}, #{playStatus})
    </insert>


    <select id="synVideo" parameterType="java.util.Map" resultType="com.jmt.modules.workOrder.entity.PlayVideo">
        select  * from ad_play_list where 1=1
        <if test="areaProvinceNum !=null and areaProvinceNum!=''">
            and PLAY_STATUS = 1  and AD_EQ_TYPE =#{eqType}
            and AD_AREA_PROVINCE_NUM =#{areaProvinceNum}
            and  (AD_AREA_CITY_NUM = ''  or   ISNULL(AD_AREA_CITY_NUM) )
            and (AD_AREA_COUNTY_NUM = '' or   ISNULL(AD_AREA_COUNTY_NUM))
        </if>
        <if test="areaCityNum !=null and areaCityNum!=''">
            and PLAY_STATUS = 1  and AD_EQ_TYPE =#{eqType}
            and AD_AREA_CITY_NUM = #{areaCityNum}
            and (AD_AREA_COUNTY_NUM = '' or ISNULL(AD_AREA_COUNTY_NUM))
        </if>
        <if test="areaCountyNum !=null and areaCountyNum !=''">
            and PLAY_STATUS = 1  and AD_EQ_TYPE =#{eqType}
            and AD_AREA_COUNTY_NUM = #{areaCountyNum}
            and (B_CODE = '' or ISNULL(B_CODE))
        </if>
        <if test="bCode !=null and bCode!=''">
            and PLAY_STATUS = 1  and AD_EQ_TYPE =#{eqType}
            and B_CODE = #{bCode}
        </if>
        <if test="eqCode !=null and eqCode!=''">
            and PLAY_STATUS = 1  and AD_EQ_TYPE =#{eqType}
            and EQ_CODE = #{eqCode}
        </if>
    </select>

    <update id="updatePlayStatus" parameterType="map">
         UPDATE ad_play_list  SET
         play_status=1  WHERE id=#{id}
    </update>

    <select id="countProvinceVideoNum" resultType="int" parameterType="java.util.Map">
        select  count(*) from ad_play_list where 1=1
        <if test="areaProvinceNum !=null and areaProvinceNum!=''">
            and PLAY_STATUS = 1  and AD_EQ_TYPE =#{eqType}
            and AD_AREA_PROVINCE_NUM =#{areaProvinceNum}
            and  (AD_AREA_CITY_NUM = ''  or   ISNULL(AD_AREA_CITY_NUM) )
            and (AD_AREA_COUNTY_NUM = '' or   ISNULL(AD_AREA_COUNTY_NUM))
            and FILM_TYPE = #{filmType}
        </if>
        <if test="areaCityNum !=null and areaCityNum!=''">
            and PLAY_STATUS = 1  and AD_EQ_TYPE =#{eqType}
            and AD_AREA_CITY_NUM = #{areaCityNum}
            and (AD_AREA_COUNTY_NUM = '' or ISNULL(AD_AREA_COUNTY_NUM))
            and FILM_TYPE = #{filmType}
        </if>
        <if test="areaCountyNum !=null and areaCountyNum !=''">
            and PLAY_STATUS = 1  and AD_EQ_TYPE =#{eqType}
            and AD_AREA_COUNTY_NUM = #{areaCountyNum}
            and FILM_TYPE = #{filmType}
        </if>
        <if test="bCode !=null and bCode!=''">
            and PLAY_STATUS = 1  and AD_EQ_TYPE =#{eqType}
            and B_CODE = #{bCode}
            and FILM_TYPE = #{filmType}
        </if>
        <if test="eqCode !=null and eqCode!=''">
            and PLAY_STATUS = 1  and AD_EQ_TYPE =#{eqType}
            and EQ_CODE = #{eqCode}
            and FILM_TYPE = #{filmType}
        </if>
    </select>


</mapper>