<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.modules.workOrder.mapper.FilmLibraryMapper">
    <resultMap id="filmLibrary" type="com.jmt.modules.workOrder.entity.FilmLibrary">
        <result column="FILM_ID" property="filmId"/>
        <result column="AD_CONTRACT_NUM" property="contractNum"/>
        <result column="FILM_NAME" property="filmName"/>
        <result column="FILM_DISK" property="filmDisk"/>
        <result column="FILM_URL" property="filmUrl"/>
        <result column="FILM_MD5" property="filmMd5"/>
        <result column="FILM_DURATION" property="filmDuration"/>
        <result column="FILM_STATUS" property="filmStatus"/>
        <result column="FILM_START_TIME" property="filmStartTime"/>
        <result column="FILM_END_TIME" property="filmEndTime"/>
        <result column="FILM_TYPE" property="filmType"/>
        <result column="PLAY_POSITION" property="playPosition"/>
    </resultMap>

    <resultMap id="filmLibraryListResult" type="com.jmt.modules.workOrder.model.FilmLibraryListResult">
        <result column="FILM_ID" property="filmId"/>
        <result column="AD_CONTRACT_NUM" property="contractNum"/>
        <result column="FILM_NAME" property="filmName"/>
        <result column="FILM_DISK" property="filmDisk"/>
        <result column="FILM_URL" property="filmUrl"/>
        <result column="FILM_MD5" property="filmMd5"/>
        <result column="FILM_DURATION" property="filmDuration"/>
        <result column="FILM_STATUS" property="filmStatus"/>
        <result column="FILM_START_TIME" property="filmStartTime"/>
        <result column="FILM_END_TIME" property="filmEndTime"/>
        <result column="FILM_TYPE" property="filmType"/>
        <result column="PLAY_POSITION" property="playPosition"/>
    </resultMap>

    <resultMap id="filmLibraryListResults" type="com.jmt.modules.workOrder.model.FilmLibraryListResults">
        <result column="FILM_ID" property="filmId"/>
        <result column="AD_CONTRACT_NUM" property="adContractNum"/>
        <result column="FILM_NAME" property="filmName"/>
        <result column="FILM_URL" property="filmUrl"/>
        <result column="FILM_MD5" property="filmMd5"/>
        <result column="FILM_DURATION" property="filmDuration"/>
        <result column="FILM_STATUS" property="filmStatus"/>
        <result column="FILM_START_TIME" property="filmStartTime"/>
        <result column="FILM_END_TIME" property="filmEndTime"/>
        <result column="FILM_TYPE" property="filmType"/>
        <result column="PLAY_POSITION" property="playPosition"/>
        <result column="AD_JOB_NUM" property="jobNum"/>
        <result column="AD_CUSTOM_NAME" property="customName"/>
        <result column="FILM_PUTTYPE" property="filmPutType"/>
        <result column="CREATE_TIME" property="createTime"/>
        <result column="UPDATE_TIME" property="updateTime"/>
    </resultMap>
    <select id="queryLibraryNoHistoryList" resultMap="filmLibraryListResult"
            parameterType="com.jmt.modules.workOrder.model.FilmLibraryuQueryCriteria">
        select f.*,a.ad_custom_name as customName from film_library f
        inner join ad_work_order a
        on f.ad_contract_num=a.ad_contract_num
        and f.film_id=a.AD_FILM_ID
        and f.film_status !=4
        <where>
            <if test="filmName !=null and filmName!=''">
                and f.film_name LIKE CONCAT('%',#{filmName},'%')
            </if>
            <if test="customName !=null and customName!=''">
                and a.ad_custom_name LIKE CONCAT('%',#{customName},'%')
            </if>
            <if test="filmDuration !=null and filmDuration!=''">
                and f.film_duration=#{filmDuration}
            </if>
            <if test="filmStatus !=null and filmStatus!=''">
                and f.film_status=#{filmStatus}
            </if>
            <if test="filmStartTime !=null and filmStartTime!=''">
                and str_to_date(f.film_start_time,'%Y-%m-%d') &gt;= str_to_date(#{filmStartTime},'%Y-%m-%d')
            </if>
            <if test="filmEndTime !=null and filmEndTime!=''">
                and str_to_date(f.film_end_time,'%Y-%m-%d') &lt;=str_to_date(#{filmEndTime},'%Y-%m-%d')
            </if>
        </where>
    </select>

    <select id="queryLibraryHistoryList" resultMap="filmLibraryListResult"
            parameterType="com.jmt.modules.workOrder.model.FilmLibraryuQueryCriteria">
        select f.*,a.ad_custom_name as customName from film_library f
        left join ad_work_order a
        on f.ad_contract_num=a.ad_contract_num
        and f.film_id=a.AD_FILM_ID
        and f.film_status =4
        <where>
            <if test="filmName !=null and filmName!=''">
                and f.film_name=#{filmName}
            </if>
            <if test="customName !=null and customName!=''">
                and a.ad_custom_name=#{customName}
            </if>
            <if test="filmDuration !=null and filmDuration!=''">
                and f.film_duration=#{filmDuration}
            </if>
            <if test="filmName !=null and filmName!=''">
                and f.film_name=#{filmName}
            </if>
            <if test="filmStartTime !=null and filmStartTime!=''">
                and str_to_date(f.film_start_time,'%Y-%m-%d') &gt;= str_to_date(#{filmStartTime},'%Y-%m-%d')
            </if>
            <if test="filmEndTime !=null and filmEndTime!=''">
                and str_to_date(f.film_end_time,'%Y-%m-%d') &lt;=str_to_date(#{filmEndTime},'%Y-%m-%d')
            </if>
        </where>
    </select>
    <!-- 插入数据:返回记录主键id值 -->
    <insert id="addFilmLibrary" parameterType="com.jmt.modules.workOrder.entity.FilmLibrary" useGeneratedKeys="true"
            keyProperty="filmId" keyColumn="FILM_ID">
        insert into film_library(film_md5,
                                 film_name,
                                 film_disk,
                                 film_url,
                                 CREATE_TIME)
        values (#{filmMd5},
                #{filmName},
                #{filmDisk},
                #{filmUrl},
                #{createTime})
    </insert>
    <update id="updateFilmLibrary" parameterType="com.jmt.modules.workOrder.entity.FilmLibrary">
        update film_library
        set AD_CONTRACT_NUM=#{contractNum},
            FILM_NAME=#{filmName},
            FILM_DURATION=#{filmDuration},
            FILM_START_TIME=#{filmStartTime},
            FILM_END_TIME=#{filmEndTime},
            FILM_TYPE=#{filmType},
            IMG_URL=#{landingPageUrl},
            FILM_PUTTYPE=#{filmPutType},
            UPDATE_TIME=#{updateTime}
        where film_id = #{filmId}
          and film_md5 = #{filmMd5}
    </update>

    <update id="updateFilmLibraryStatus">
        update film_library
        set film_status=#{filmStatus}
        where film_id = #{filmId}
    </update>

    <select id="queryPlayVideoFilmLibraryList" resultMap="filmLibrary"
            parameterType="com.jmt.modules.workOrder.entity.PlayVideo">
        select * from ad_work_order a
        left join ad_work_order_area b on a.AD_JOB_NUM=b.AD_JOB_NUM
        left join film_library l on a.AD_FILM_ID=l.film_id
        where a.AD_STATUS=4 and l.film_status=1 and l.FILM_TYPE=1
        and b.AD_AREA_PROVINCE_NUM=#{areaProvinceNum}
        and b.AD_AREA_CITY_NUM=#{areaCityNum}
        and b.AD_AREA_COUNTY_NUM=#{areaCountyNum}
        and l.FILM_DURATION=#{filmDuration}
        <if test="eqType==1 ||eqType=='1'">
            and a.AD_EQ_TYPE_A=1
        </if>
        <if test="eqType==2 ||eqType=='2'">
            and a.AD_EQ_TYPE_B=1
        </if>
        <if test="eqType==3 ||eqType=='3'">
            and a.AD_EQ_TYPE_C=1
        </if>
        <!-- <if test="filmEndTime !=null">
             and str_to_date(l.FILM_START_TIME,'%Y-%m-%d') &gt; str_to_date(#{filmEndTime},'%Y-%m-%d')
         </if>-->
        and str_to_date(l.FILM_START_TIME,'%Y-%m-%d') &gt;= str_to_date(NOW(),'%Y-%m-%d')
        order by l.FILM_START_TIME asc
    </select>

    <delete id="deleteFilmLibrary">
        delete
        from film_library
        where film_md5 = #{filmMd5}
    </delete>

    <delete id="deleteFilmLibraryById">
        delete
        from film_library
        where FILM_ID = #{filmId}
    </delete>
    <delete id="deleteAllFilmLibrary" parameterType="java.util.List">
        delete from film_library where film_md5 in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <select id="queryFilmLibraryById" resultMap="filmLibrary">
        select *
        from film_library
        where film_id = #{filmId}
    </select>

    <select id="queryCutPlayVideoFilmLibraryList" resultMap="filmLibrary"
            parameterType="com.jmt.modules.workOrder.entity.InsertPlayVideo">
        select * from ad_work_order a
        left join ad_work_order_area b on a.AD_JOB_NUM=b.AD_JOB_NUM
        left join film_library l on a.AD_FILM_ID=l.film_id
        where a.AD_STATUS=4 and l.film_status=1 and l.FILM_TYPE=2
        and b.AD_AREA_PROVINCE_NUM=#{areaProvinceNum}
        and b.AD_AREA_CITY_NUM=#{areaCityNum}
        and b.AD_AREA_COUNTY_NUM=#{areaCountyNum}
        <if test="eqType==1 ||eqType=='1'">
            and a.AD_EQ_TYPE_A=1
        </if>
        <if test="eqType==2 ||eqType=='2'">
            and a.AD_EQ_TYPE_B=1
        </if>
        <if test="eqType==3||eqType=='3'">
            and a.AD_EQ_TYPE_C=1
        </if>
    </select>

    <update id="updateFilmLibraryStatusHistory">
        update film_library
        set film_status=4
        where DATE_FORMAT(FILM_END_TIME, '%Y-%m-%d') = DATE_FORMAT(CURDATE(), '%Y-%m-%d')
          and FILM_STATUS = 3
    </update>

    <select id="fullVideoList" resultMap="filmLibraryListResult">
        select f.*
        from film_library f
                 inner join ad_work_order a on f.FILM_ID = a.AD_FILM_ID and f.AD_CONTRACT_NUM = a.AD_CONTRACT_NUM
        where a.AD_STATUS = 4
          and f.FILM_STATUS = 1
          and f.FILM_TYPE = 4
        ORDER BY f.FILM_START_TIME desc
    </select>


    <delete id="delTypeFull">
        DELETE
        FROM film_library
        WHERE FILM_TYPE = 4
    </delete>


    <!--2020.3.26 jxl 查询当前在播放的全网充电视频-->
    <select id="queryFilmLibrary" resultMap="filmLibrary">
        select *
        from film_library
        where 1 = 1
          and FILM_TYPE = 4
          and FILM_STATUS = 3
    </select>

    <!--2020.3.26 jxl 查询当前在播放的全网商户视频-->
    <select id="queryShopFilmLibrary" resultMap="filmLibrary">
        select *
        from film_library
        where 1 = 1
          and FILM_TYPE = 5
          and FILM_STATUS = 3
          and PLAY_POSITION !=0
    </select>


    <insert id="addFilmLibrarys" keyProperty="filmId" useGeneratedKeys="true">
        insert into film_library(FILM_MD5, AD_CONTRACT_NUM, FILM_NAME, FILM_DISK, FILM_URL, FILM_DURATION, FILM_STATUS,
                                 FILM_START_TIME, FILM_END_TIME, FILM_TYPE, PLAY_POSITION)
        values (#{filmMd5}, #{contractNum}, #{filmName}, #{filmDisk}, #{filmUrl}, #{filmDuration}, #{filmStatus},
                #{filmStartTime}, #{filmEndTime}, #{filmType}, #{playPosition})
    </insert>

    <update id="update" parameterType="com.jmt.modules.workOrder.entity.FilmLibrary">
        update film_library
        <set>
            <if test="filmMd5 != null and filmMd5 != ''">
                FILM_MD5 = #{filmMd5},
            </if>
            <if test="contractNum != null and contractNum != ''">
                AD_CONTRACT_NUM = #{contractNum},
            </if>
            <if test="filmName != null and filmName != ''">
                FILM_NAME = #{filmName},
            </if>
            <if test="filmDisk != null and filmDisk != ''">
                FILM_DISK = #{filmDisk},
            </if>
            <if test="filmUrl != null and filmUrl != ''">
                FILM_URL = #{filmUrl},
            </if>
            <if test="filmDuration != null">
                FILM_DURATION = #{filmDuration},
            </if>
            <if test="filmStatus != null">
                FILM_STATUS = #{filmStatus},
            </if>
            <if test="filmStartTime != null">
                FILM_START_TIME = #{filmStartTime},
            </if>
            <if test="filmEndTime != null">
                FILM_END_TIME = #{filmEndTime},
            </if>
            <if test="filmType != null">
                FILM_TYPE = #{filmType},
            </if>
            <if test="playPosition != null">
                PLAY_POSITION = #{playPosition},
            </if>
        </set>
        where FILM_ID = #{filmId}
    </update>

    <select id="queryWholeNetworkAllFilm" resultType="com.jmt.modules.workOrder.model.FilmLibraryListResults">
        select * from film_library f,ad_work_order w
        <where>
            f.FILM_ID = w.AD_FILM_ID
            <choose>
                <when test="workOrderQuery.filmType == 4">
                    and (FILM_TYPE between 4 and 5)
                </when>
                <otherwise>
                    and FILM_TYPE=#{workOrderQuery.filmType}
                </otherwise>
            </choose>
            <if test="workOrderQuery.userId!=null and workOrderQuery.userId!=''">
                and w.AD_CREATOR_NUM=#{workOrderQuery.userId}
            </if>
            <if test="workOrderQuery.filmStatus!=null">
                and FILM_STATUS = #{workOrderQuery.filmStatus}
            </if>
            <if test="workOrderQuery.putType !=null">
                and FILM_PUTTYPE = #{workOrderQuery.putType}
            </if>
        </where>
    </select>


    <select id="netWorkVideoList" parameterType="map" resultType="com.jmt.modules.workOrder.entity.FilmLibrary">
        select * from film_library where 1=1
        <if test="filmId!=null">
            and FILM_ID = #{filmId}
        </if>
        <if test="playPosition!=null">
            and PLAY_POSITION = #{playPosition}
        </if>
        and FILM_STATUS = 1
    </select>


    <update id="changeStatus">
        update film_library
        set FILM_STATUS = 3
        where 1 = 1
          and FILM_MD5 = #{filmMd5}
    </update>

    <select id="listFilmLibrary" parameterType="com.jmt.modules.workOrder.model.FilmLibraryListQueryCriteria"
            resultMap="filmLibraryListResults">
        SELECT * FROM ad_work_order w LEFT JOIN film_library f ON w.`AD_FILM_ID` = f.`FILM_ID`
        <where>

            <choose>
                <when test="filmLibraryListQueryCriteria.filmType == 2">
                    and (FILM_TYPE = 2 or FILM_TYPE = 8 or FILM_TYPE = 9)
                </when>
                <when test="filmLibraryListQueryCriteria.filmType == 4">
                    and (FILM_TYPE between 4 and 5)
                </when>
                <otherwise>
                    and FILM_TYPE=#{filmLibraryListQueryCriteria.filmType}
                </otherwise>
            </choose>
            <if test="filmLibraryListQueryCriteria.filmStatus !=null">
                and FILM_STATUS=#{filmLibraryListQueryCriteria.filmStatus}
            </if>
            <if test="filmLibraryListQueryCriteria.filmDuration !=null">
                and FILM_DURATION=#{filmLibraryListQueryCriteria.filmDuration}
            </if>
            <if test="filmLibraryListQueryCriteria.customName !=null and filmLibraryListQueryCriteria.customName!=''">
                and AD_CUSTOM_NAME=#{filmLibraryListQueryCriteria.customName}
            </if>
            <if test="filmLibraryListQueryCriteria.filmStartTime !=null and filmLibraryListQueryCriteria.filmStartTime !=''">
                and FILM_START_TIME &gt;=CONCAT(#{filmLibraryListQueryCriteria.filmStartTime},' 00:00:00')
            </if>
            <if test="filmLibraryListQueryCriteria.filmSendTime !=null and filmLibraryListQueryCriteria.filmSendTime !=''">
                and FILM_END_TIME &lt;=CONCAT(#{filmLibraryListQueryCriteria.filmSendTime},' 23:59:59')
            </if>

            <if test="filmLibraryListQueryCriteria.filmType == 4">
                ORDER BY PLAY_POSITION
            </if>
            <if test="filmLibraryListQueryCriteria.filmType != 4">
                ORDER BY UPDATE_TIME DESC
            </if>
        </where>
    </select>


    <select id="queryNetWork" parameterType="map" resultMap="filmLibrary">
        select *
        from film_library
        where 1 = 1
          and FILM_TYPE = #{filmType}
          and film_md5 = #{filmMd5}

    </select>

    <update id="updateNetWork" parameterType="map">
        update film_library set FILM_STATUS = #{filmStatus}
        <if test="playPosition!=null">
            , PLAY_POSITION=#{playPosition}
        </if>
        where AD_CONTRACT_NUM = #{contractNum} and film_md5 = #{filmMd5}
    </update>

    <update id="updateNetWorkElse" parameterType="map">
        update film_library set FILM_STATUS = #{filmStatus}
        <if test="playPosition!=null">
            , PLAY_POSITION=#{playPosition}
        </if>
        where AD_CONTRACT_NUM = #{contractNum} and film_md5 != #{filmMd5} and FILM_TYPE = #{filmType}
    </update>


    <select id="queryFilmNum" resultType="integer" parameterType="map">
        SELECT COUNT(*) FROM
        ( SELECT w.`AD_FILM_ID`,o.* FROM ad_work_order w,ad_work_order_area o WHERE w.`AD_JOB_NUM` = O.`AD_JOB_NUM`) a
        LEFT JOIN film_library f ON a.AD_FILM_ID = f.`FILM_ID`
        <where>
            <if test="areaProvinceNum!=null">
                and AD_AREA_PROVINCE_NUM=#{areaProvinceNum}
            </if>
            <if test="areaCityNum !=null">
                and AD_AREA_CITY_NUM=#{areaCityNum}
            </if>
            <if test="areaCountyNum!=null">
                and AD_AREA_COUNTY_NUM=#{areaCountyNum}
            </if>
        </where>
    </select>

    <select id="queryPendingFilmNum" resultType="integer" parameterType="map">
        SELECT COUNT(*) FROM
        ( SELECT w.`AD_FILM_ID`,o.* FROM ad_work_order w,ad_work_order_area o WHERE w.`AD_JOB_NUM` = o.`AD_JOB_NUM`) a
        LEFT JOIN film_library f ON a.AD_FILM_ID = f.`FILM_ID`
        <where>
            FILM_STATUS = 0
            <if test="areaProvinceNum!=null">
                and AD_AREA_PROVINCE_NUM=#{areaProvinceNum}
            </if>
            <if test="areaCityNum !=null">
                and AD_AREA_CITY_NUM=#{areaCityNum}
            </if>
            <if test="areaCountyNum!=null">
                and AD_AREA_COUNTY_NUM=#{areaCountyNum}
            </if>
        </where>
    </select>

    <insert id="save">

    </insert>

    <select id="getFilmByJobNum" resultMap="filmLibrary">
        select *
        from film_library
        where FILM_ID = #{jobNum};
    </select>

    <select id="getJobNumByFilmLibraryStatusHistory" resultType="string">
        SELECT t2.AD_JOB_NUM
        FROM (SELECT FILM_ID, FILM_STATUS
              FROM film_library
              WHERE DATE_FORMAT(FILM_END_TIME, '%Y-%m-%d') = DATE_FORMAT(CURDATE(), '%Y-%m-%d')) t1,
             ad_work_order t2
        WHERE t1.FILM_ID = t2.AD_FILM_ID
          AND t1.FILM_STATUS = 4
    </select>
</mapper>