<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jmt.modules.api.mapper.CmsOrderLogMapper">

    <resultMap id="BaseResultMap" type="com.jmt.modules.api.entity.CmsOrderLog">
        <!--@Table cms_order_log-->
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="orderId" column="order_id" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="payId" column="pay_id" jdbcType="VARCHAR"/>
        <result property="orderDescription" column="order_description" jdbcType="VARCHAR"/>
        <result property="orderType" column="order_type" jdbcType="INTEGER"/>
        <result property="logType" column="log_type" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="BaseResultMap">
        select
          id, order_id, user_id, pay_id, order_description, order_type, log_type, create_time
        from cms_order_log
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="BaseResultMap">
        select
          id, order_id, user_id, pay_id, order_description, order_type, log_type, create_time
        from cms_order_log
        limit #{offset}, #{limit}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="BaseResultMap">
        select
          id, order_id, user_id, pay_id, order_description, order_type, log_type, create_time
        from cms_order_log
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="orderId != null and orderId != ''">
                and order_id = #{orderId}
            </if>
            <if test="userId != null and userId != ''">
                and user_id = #{userId}
            </if>
            <if test="payId != null and payId != ''">
                and pay_id = #{payId}
            </if>
            <if test="orderDescription != null and orderDescription != ''">
                and order_description = #{orderDescription}
            </if>
            <if test="orderType != null">
                and order_type = #{orderType}
            </if>
            <if test="logType != null">
                and log_type = #{logType}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into cms_order_log(order_id, user_id, pay_id, order_description, order_type, log_type, create_time)
        values (#{orderId}, #{userId}, #{payId}, #{orderDescription}, #{orderType}, #{logType}, #{createTime})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update cms_order_log
        <set>
            <if test="orderId != null and orderId != ''">
                order_id = #{orderId},
            </if>
            <if test="userId != null and userId != ''">
                user_id = #{userId},
            </if>
            <if test="payId != null and payId != ''">
                pay_id = #{payId},
            </if>
            <if test="orderDescription != null and orderDescription != ''">
                order_description = #{orderDescription},
            </if>
            <if test="orderType != null">
                order_type = #{orderType},
            </if>
            <if test="logType != null">
                log_type = #{logType},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from cms_order_log where id = #{id}
    </delete>

</mapper>