package com.jmt.modules.workOrder.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 *  @author: hanshangrong
 *  @Date: 2020/3/31 0031 09:48
 *  @Description:类注释
 *  短视频管理
 *
 */
@Data
public class WorkOrderVideoListResult {
    /**主键*/
    private String id;
    /**广告工单号*/
    private String jobNum;
    /**广告合同编号*/
    private String contractNum;
    /**广告合同金额*/
    private Double amountMoney;
    /**客户联系方式*/
    private String customPhone;
    /**客户联系人*/
    private String customLiaison;
    /**投放开始时间*/
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date filmStartTime;
    /**投放结束时间*/
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date filmEndTime;
    /**视频关联id 片源库id*/
    private int filmId;
    /**视频名称*/
    private String filmName;
    /**视频长度*/
    private Integer filmDuration;
    /**创建时间*/
    private Date createTime;
    /**0:新增未审批 1：主管审批 2：财务审批 3：审片员审批 4:审批通过 5：驳回 */
    private int status;
    /**区域*/
    private String address;
    /**视频类型 1:非广告视频 2广告视频 4：全网充电视频 5：全网商家视频 3：紧急推送*/
    private Integer filmType;
    /**客户名称*/
    private String customName;
    /**视频url*/
    private String filmUrl;
}
