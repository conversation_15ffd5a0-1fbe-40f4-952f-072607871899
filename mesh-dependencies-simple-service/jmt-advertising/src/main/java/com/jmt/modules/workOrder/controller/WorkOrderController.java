package com.jmt.modules.workOrder.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jmt.common.api.vo.Result;
import com.jmt.common.aspect.annotation.WorkOrderDisplay;
import com.jmt.modules.api.service.AdvertisingOrderService;
import com.jmt.modules.client.commission.CommissionClient;
import com.jmt.modules.client.device.DeviceClient;
import com.jmt.modules.workOrder.entity.AdWorkOrderArea;
import com.jmt.modules.workOrder.entity.FilmLibrary;
import com.jmt.modules.workOrder.entity.WorkOrder;
import com.jmt.modules.workOrder.entity.WorkOrderArea;
import com.jmt.modules.workOrder.model.*;
import com.jmt.modules.workOrder.service.*;
import com.jmt.modules.workOrder.util.UserUtil;
import com.jmt.modules.workOrder.util.WorkOrderUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 广告工单
 */
@RestController
@RequestMapping("/workOrder")
@Slf4j
@Api(tags = "广告视频管理")
public class WorkOrderController {

    @Autowired
    private IWorkOrderService iWorkOrderService;
    @Autowired
    private IFilmLibraryService iFilmLibraryService;
    @Autowired
    private IAuditorUserPasswordService iAuditorUserPasswordService;
    @Resource
    private AdvertisingOrderService advertisingOrderServiceImpl;
    @Resource
    private CommissionClient commissionClient;
    @Resource
    private IAdWorkOrderAreaService adWorkOrderAreaServiceImpl;
    @Resource
    private DeviceClient deviceClient;
    @Resource
    private IFilmLibraryService filmLibraryServiceImpl;
    @Resource
    private IWorkOrderService iWorkOrderServiceImpl;
    @Resource
    private IBUserFileService ibUserFileServiceImpl;


    /**
     * 广告工单列表
     *
     * @return
     */
    @ApiOperation("广告工单列表")
    @WorkOrderDisplay
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public Result<Page<WorkOrderListResult>> workOrderList(WorkOrderListQueryCriteria workOrderQueryList) {
        return iWorkOrderService.workOrderList(workOrderQueryList);
    }

    /**
     * 广告工单列表
     *
     * @return
     */
    @ApiOperation("通过电话号码搜索广告工单列表")
    @WorkOrderDisplay
    @RequestMapping(value = "/listByPhone", method = RequestMethod.POST)
    public Result<Page<WorkOrderListResult>> workOrderListByPhone(WorkOrderListQueryCriteria workOrderQueryList) {
        workOrderQueryList.setUserId(null);
        return iWorkOrderService.workOrderList(workOrderQueryList);
    }

    @ApiOperation("获取工单详情")
    @RequestMapping(value = "/getWorkOrderInfo/{jobNum}", method = RequestMethod.GET)
    public Result<WorkOrderResult> getWorkOrderInfo(@PathVariable String jobNum) {
        return iWorkOrderService.getWorkOrderInfo(jobNum);
    }


    /**
     * 修改广告工单内容 新增广告工单
     * workOrder 工单内容
     *
     * @return
     */
    @PostMapping(value = "/updateWorkOrder")
    public Result<WorkOrder> updateWorkOrder(@RequestBody JSONObject jsonObject) {
        Result<WorkOrder> result = new Result<>();
        AddWorkOrder addWorkOrder = JSON.parseObject(jsonObject.toJSONString(), AddWorkOrder.class);
        JSONArray eqType = jsonObject.getJSONArray("eqType");
        //投放区域 二维数组
        JSONArray quyu = jsonObject.getJSONArray("quyu");
        if (quyu == null || quyu.size() == 0) {
            return result.error500("请选择区域");
        }
        WorkOrder workOrder = new WorkOrder();
        FilmLibrary filmLibrary = JSON.parseObject(jsonObject.toJSONString(), FilmLibrary.class);
        try {
            for (int i = 0; i < eqType.size(); i++) {
                String type = (String) eqType.get(i);
                if ("A型".equals(type)) {
                    addWorkOrder.setEqTypeA(1);
                } else if ("B型".equals(type)) {
                    addWorkOrder.setEqTypeB(1);
                } else if ("C型".equals(type)) {
                    addWorkOrder.setEqTypeC(1);
                }
            }
            BeanUtils.copyProperties(addWorkOrder, workOrder);//转移广告工单表需要的内容
            // BeanUtils.copyProperties(addWorkOrder, filmLibrary);//转移片源库表需要的内容
            //查看该视频是否已有工单添加
            WorkOrder wO = iWorkOrderService.queryWorkOrderIsFilmId(String.valueOf(addWorkOrder.getFilmId()));
            if (wO == null) {

            } else {
                workOrder.setJobNum(wO.getJobNum());
            }
            //是否存在该工单
            WorkOrder work = iWorkOrderService.queryWorkOrderByJobNum(workOrder.getJobNum());
            if (work == null) {
                //新增广告工单
                int addWorkOrderResult = iWorkOrderService.addWorkOrder(workOrder, quyu);
                //修改片源库的该合同的视频信息
                iFilmLibraryService.updateFilmLibrary(filmLibrary);
                if (addWorkOrderResult > 0) {
                    result.success("新增成功!");
                } else {
                    result.success("新增失败!");
                }
            } else {
                //未审核 ，驳回的可修改
                if (work.getStatus() == 0 || work.getStatus() == 4) {
                    //更改广告工单的内容
                    int updateWorkOrderResult = iWorkOrderService.updateWorkOrder(workOrder, quyu);
                    //修改片源库的该合同的视频信息
                    iFilmLibraryService.updateFilmLibrary(filmLibrary);
                    if (updateWorkOrderResult > 0) {
                        result.success("修改成功!");
                    } else {
                        result.success("修改失败!");
                    }
                } else {
                    result.success("该工单正在审核中，无法修改");
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.error500("操作失败");
        }
        return result;
    }

    @ApiOperation("新增广告工单")
    @RequestMapping(value = "/insertWork", method = RequestMethod.POST)
    public Result<Object> insertWork(@RequestBody @Valid AddWorkOrders addWorkOrders) {


        try {
            Result<Object> result = iWorkOrderService.insertWork(addWorkOrders);

            //561是直播的用户id
            if (UserUtil.getSysUser().getId() == 561) {
                //查询JobNum
                AddWorkOrder workOrderfilmId = iWorkOrderServiceImpl.queryJobNumByfilmId(addWorkOrders.getFilmId());
                AddWorkOrder workOrder = iWorkOrderServiceImpl.queryWorkOrderByJobNum(workOrderfilmId.getJobNum());
                System.out.println("filmId2====" + workOrder.getFilmId());
                AdWorkOrderArea orderArea = adWorkOrderAreaServiceImpl.getWorkOrderByJobNum(workOrderfilmId.getJobNum());

                FilmLibrary lilmLibrary = filmLibraryServiceImpl.getFilmByJobNum(workOrder.getFilmId().toString());
                ibUserFileServiceImpl.updateFileStatusByBcodeAndType(orderArea.getBCode(),   1, 1);
                commissionClient.updateFileStatusByMcode(orderArea.getBCode(),   1);

                //推送商家视频1号位
                JSONArray array = new JSONArray();
                JSONObject shopVideo = new JSONObject();
                shopVideo.put("snum", "1");
                shopVideo.put("url", lilmLibrary.getFilmUrl());
                shopVideo.put("md5", lilmLibrary.getFilmMd5());

                JSONObject shopMsg = new JSONObject();
                shopMsg.put("type", "3");
                array.add(shopVideo);
                shopMsg.put("list", array);

                JSONObject shopTop = new JSONObject();
                System.out.println("直通发送的bCode=="+orderArea.getBCode());
                shopTop.put("topic", orderArea.getBCode());
                shopTop.put("message", shopMsg);
                deviceClient.publishMerchantPlay(shopTop);
                System.out.println("发送视频直接完成===");
            }
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("新增广告失败");
        }
    }

    @ApiOperation("广告工单审核")
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping(value = "/auditWorkOrder", method = RequestMethod.POST)
    public Result<Map<String, Integer>> auditWorkOrder(@RequestParam("jobNum") String jobNum,
                                                       @RequestParam("status") String status,
                                                       @RequestParam(value = "auditorView", required = false) String auditorView,
                                                       @RequestParam("password") String password
    ) {
        Result<Map<String, Integer>> result = new Result<>();
        //密码校验
        boolean pass = iAuditorUserPasswordService.password(password);
        if (!pass) {
            return result.error500("密码错误或无效用户！");
        }
        AddWorkOrder addWorkOrder = iWorkOrderService.queryWorkOrderByJobNum(jobNum);
        if (jobNum == null || status == null) {
            return result.error500("工单不存在!");
        }
        //该广告工单视频不存在
        if (addWorkOrder.getFilmId() == 0 || StringUtils.isBlank(addWorkOrder.getFilmMd5())) {
            result.error500("该广告工单视频未上传或找不到！");
        }
        result = iWorkOrderService.auditWorkOrder(jobNum, status, auditorView);
        Optional.ofNullable(result.getResult()).ifPresent(map -> {
            if (map.get("source") != null && map.get("source") == 1) {
                advertisingOrderServiceImpl.updateOrder(map.get("audit"), jobNum);
            } else if (map.get("source") != null && map.get("source") == 2) {
                //查询工单详情
                AdWorkOrderArea orderArea = adWorkOrderAreaServiceImpl.getWorkOrderByJobNum(jobNum);
                AddWorkOrder workOrder = iWorkOrderServiceImpl.queryWorkOrderByJobNum(jobNum);
                //修改商户视频状态
                ibUserFileServiceImpl.updateFileStatusByBcodeAndType(orderArea.getBCode(), map.get("audit") == 0 ? 2 : 1, 1);
                FilmLibrary lilmLibrary = filmLibraryServiceImpl.getFilmByJobNum(workOrder.getFilmId().toString());
                commissionClient.updateFileStatusByMcode(orderArea.getBCode(), map.get("audit") == 0 ? 2 : 1);
                //推送商家视频1号位
                JSONArray array = new JSONArray();
                JSONObject shopVideo = new JSONObject();
                shopVideo.put("snum", "1");
                shopVideo.put("url", lilmLibrary.getFilmUrl());
                shopVideo.put("md5", lilmLibrary.getFilmMd5());
                shopVideo.put("id", workOrder.getFilmId().toString());
                System.out.println("1这个是广告id=="+"workOrder.getFilmId().toString()");
                JSONObject shopMsg = new JSONObject();
                shopMsg.put("type", "3");
                array.add(shopVideo);
                shopMsg.put("list", array);

                JSONObject shopTop = new JSONObject();
                shopTop.put("topic", orderArea.getBCode());
                shopTop.put("message", shopMsg);
                deviceClient.publishMerchantPlay(shopTop);
            }
        });
        return result;
    }

    /**
     * 查看该广告工单的审核进度
     * jobNum 广告工单号
     *
     * @return
     */
    @PostMapping(value = "/workOrderAuditQuery")
    public Result<Object> workOrderAuditQuery(@RequestBody JSONObject jsonObject) {
        Result<Object> result = new Result<>();
        String jobNum = jsonObject.getString("jobNum");
        if (jobNum == null) {
            result.error500("参数有误或不存在");
        } else {
            List<WorkOrderAuditListResult> list = iWorkOrderService.workOrderAuditQuery(jobNum);
            result.setResult(WorkOrderUtil.putgetMap("data", list));
        }
        return result;
    }


    /**
     * @param
     * @return
     * @Description 查询这个区域的机器数量
     * <AUTHOR>
     * @date 2019/8/28 0028
     */
    @PostMapping(value = "/queryEqAllListNum")
    public Result<Object> queryQuYuEqAllLisNum(@RequestBody JSONObject jsonObject) {
        Result<Object> result = new Result<>();
        WorkOrderIsEmptyPeriodQueryCriteria workOrderIsEmptyPeriodQueryCriteria = JSON.parseObject(jsonObject.toJSONString(), WorkOrderIsEmptyPeriodQueryCriteria.class);
        JSONArray quyu = jsonObject.getJSONArray("quyu");//投放区域 二维数组
        if (quyu == null || quyu.size() == 0) {
            return result;
        }
        List<WorkOrderIsEmptyPeriodQueryCriteria> list = new ArrayList<>();
        list = WorkOrderUtil.quyuzhaunListWorkOrderIsEmpty(quyu, list, workOrderIsEmptyPeriodQueryCriteria);
        WorkOrderIsEmptyPeriodQueryResult workOrderIsEmptyPeriodQueryResult = iWorkOrderService.queryQuYuEqAllLisNum(list);
        result.setResult(workOrderIsEmptyPeriodQueryResult);
        return result;
    }

    @ApiOperation("修改广告工单")
    @RequestMapping(value = "/updateWork", method = RequestMethod.POST)
    public Result<Object> updateWork(@RequestBody @Valid AddWorkOrders addWorkOrders) {
        return iWorkOrderService.updateWork(addWorkOrders);
    }

    @ApiOperation("广告短视频列表")
    @WorkOrderDisplay
    @RequestMapping(value = "/getWorkOrderVideoList", method = RequestMethod.POST)
    public Result<Page<WorkOrderVideoListResult>> getWorkOrderVideoList(@RequestBody WorkOrderListQueryCriteria workOrderListQueryCriteria) {
        return iWorkOrderService.queryWorkOrderVideoList(workOrderListQueryCriteria);
    }

    @ApiOperation("删除短视频")
    @RequestMapping(value = "/deleteWorkOrderVideo", method = RequestMethod.GET)
    public Result<Object> deleteWorkOrderVideo(Integer id) {
        return iWorkOrderService.deleteWorkOrderVideo(id);
    }

    @ApiOperation("用户投放管理")
    @RequestMapping(value = "/getWorkOrderUserList", method = RequestMethod.POST)
    public Result<Page<WorkOrderListResult>> getWorkOrderUserList(@RequestBody WorkOrderListQueryCriteria workOrderListQueryCriteria) {
        return iWorkOrderService.queryWorkOrderUserList(workOrderListQueryCriteria);
    }

    @ApiOperation("全网视频管理")
    @WorkOrderDisplay
    @RequestMapping(value = "/getWholeNetworkAllFilm", method = RequestMethod.POST)
    public Result<Page<FilmLibraryListResults>> getWholeNetworkAllFilm(@RequestBody WorkOrderListQueryCriteria workOrderListQueryCriteria) {
        return iWorkOrderService.queryWholeNetworkAllFilm(workOrderListQueryCriteria);
    }

    @ApiOperation("获取单店设备列表")
    @RequestMapping(value = "/getBuserEqList", method = RequestMethod.POST)
    public Result<Page<BUserListResult>> getBuserEqList(@RequestBody Map<String, Object> map) {
        return iWorkOrderService.queryBuserEqList(map);
    }

    @RequestMapping(value = "/getShopVideoList", method = RequestMethod.POST)
    public Result<Page<ShopVideoListResult>> getShopVideoList(@RequestBody WorkOrderListQueryCriteria workOrderListQueryCriteria) {
        return iWorkOrderService.getShopVideoList(workOrderListQueryCriteria);
    }

    @ApiOperation("校验密码")
    @RequestMapping(value = "/checkPassword", method = RequestMethod.POST)
    public Result<Object> checkPassword(String password) {
        Result<Object> result = new Result<>();
        if (password == null) {
            return result.error500("请输入密码！");
        }
        //密码校验
        boolean pass = iAuditorUserPasswordService.password(password);
        if (!pass) {
            return result.error500("密码错误或无效用户！");
        }
        result.setCode(200);
        result.setMessage("密码正确");
        return result;
    }


}
