package com.jmt.modules.workOrder.model;

import lombok.Data;

import java.util.Date;

/**
 * 片源库列表查询条件
 */
@Data
public class FilmLibraryuQueryCriteria {

    /**片源名称*/
    private  String filmName;
    /**投放人 广告工单中的客户名称*/
    private String customName;
    /**视频时长*/
    private String filmDuration;
    /**视频状态 0:未审核 1：通过审核 2：未通过审核 3：播放中 4：历史片源*/
    private String filmStatus;
    /**投放开始时间*/
    private String  filmStartTime;
    /**投放结束时间*/
    private String filmEndTime;

}
