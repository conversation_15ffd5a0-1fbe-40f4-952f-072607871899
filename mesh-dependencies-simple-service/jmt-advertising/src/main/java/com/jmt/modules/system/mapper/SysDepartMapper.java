package com.jmt.modules.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jmt.modules.system.entity.SysDepart;
import com.jmt.modules.system.model.SysDepartTreeModel;
import com.jmt.modules.system.model.TreeModel;
import org.springframework.data.repository.query.Param;
import java.util.List;


public interface SysDepartMapper extends BaseMapper<SysDepart> {
	
	/**
	 * 根据用户ID查询部门集合
	 */
	public List<SysDepart> queryUserDeparts(@Param("userId") String userId);
}
