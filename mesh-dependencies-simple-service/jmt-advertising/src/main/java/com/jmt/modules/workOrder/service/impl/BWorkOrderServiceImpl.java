package com.jmt.modules.workOrder.service.impl;

import com.jmt.modules.workOrder.service.IBWorkOrderService;
import org.springframework.stereotype.Service;

@Service
public class BWorkOrderServiceImpl implements IBWorkOrderService {

//    @Resource
//    private BWorkOrderMapper bWorkOrderMapper;
//
//    @Resource
//    private BUserFileMapper bUserFileMapper;
//
//    @Resource
//    private JoggleService joggleService;
//
//    @Override
//    public List<BWorkOrderLisrResult> bWorkOrderList(BWorkOrderListQueryCriteria bWorkOrderListQueryCriteria) {
//
//        return  bWorkOrderMapper.bWorkOrderList(bWorkOrderListQueryCriteria);
//    }
//
//    @Override
//    public BWorkOrderQueryResult queryBWorkOrder(String bJobNum) {
//        BWorkOrderQueryResult bWorkOrderQueryResult = bWorkOrderMapper.queryBWorkOrder(bJobNum);
//        if(bWorkOrderQueryResult !=null){
//            //查找商户图片
//            List<BUserFile> bUserFiles = bUserFileMapper.queryBUserFile(bWorkOrderQueryResult.getBCode(), "0");
//            bWorkOrderQueryResult.setBUserFiles(bUserFiles);
//        }
//        return bWorkOrderQueryResult;
//    }
//
//    @Override
//    public Result<String> updateBWorkOrder(BWorkOrderQueryResult bWorkOrderQueryResult) {
//        Result<String> result=new Result<>();
//
//        int a=bWorkOrderMapper.updateBWorkOrder(bWorkOrderQueryResult);
//        if(a>0){
//            result.success("修改成功");
//        }else{
//            result.success("修改失败");
//        }
//        return result;
//    }
//
//    @Override
//    public List<BWorkOrderAuditListResult> queryBWorkOrderAuditor(String bJobNum) {
//        return bWorkOrderMapper.queryBWorkOrderAuditor(bJobNum);
//    }
//
//
//    @Override
//    public int bWorkOrderDeployed(String bJobNum) {
//        return  bWorkOrderMapper.bWorkOrderDeployed(bJobNum);
//    }
//
//    /**
//     * 描述:  获取工单列表
//     * @method  getBworkOrderList
//     * @date: 2020/3/26 0026
//     * @author: hanshangrong
//     * @param bWorkOrder
//     * @return com.jmt.common.api.vo.Result<com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.jmt.modules.workOrder.entity.BWorkOrder>>
//     */
//    @Override
//    public Result<Page<BWorkOrderLisrResult>> getBworkOrderList(BWorkOrderListQueryCriteria bWorkOrder) {
//        Page<BWorkOrderLisrResult> page=new Page<>(bWorkOrder.getPageNo(),bWorkOrder.getPageSize());
//        page.setRecords(bWorkOrderMapper.getBworkOrderList(page,bWorkOrder));
//        Result<Page<BWorkOrderLisrResult>> result=new Result<>();
//        result.setResult(page);
//        result.setSuccess(true);
//        result.setCode(200);
//        return result;
//    }
//
//    /**
//     * 描述:  新增工单
//     * @method  insertBwork
//     * @date: 2020/3/26 0026
//     * @author: hanshangrong
//     * @param bWorkOrder
//     * @return com.jmt.common.api.vo.Result
//     */
//    @Override
//    public Result insertBwork(BWorkOrder bWorkOrder) {
//        bWorkOrder.setBCreatorTime(new Date());
//        int count=bWorkOrderMapper.insertBWorkOrder(bWorkOrder);
//        if(count == 1){
//            return Result.ok("新增工单成功");
//        }
//        return Result.error("新增失败");
//    }
}
