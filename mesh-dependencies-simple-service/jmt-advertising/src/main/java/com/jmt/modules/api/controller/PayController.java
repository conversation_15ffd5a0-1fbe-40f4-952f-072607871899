package com.jmt.modules.api.controller;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jmt.common.exception.PayException;
import com.jmt.modules.api.entity.*;
import com.jmt.modules.api.mapper.CmsMoneyDetailMapper;
import com.jmt.modules.api.mapper.CmsMoneyMapper;
import com.jmt.modules.api.model.vo.PayVo;
import com.jmt.modules.api.service.PushMsgService;
import com.jmt.modules.client.commission.CommissionClient;
import com.jmt.modules.client.uaa.UaaCilent;
import com.jmt.modules.client.uaa.UserOpenAuthInfoDto;
import com.jmt.modules.client.uaa.UserPointsRecordDTO;
import com.jmt.modules.workOrder.entity.AdWorkOrderGreen;
import com.jmt.modules.workOrder.entity.BrandUser;
import com.jmt.modules.workOrder.mapper.AdWorkOrderGreenMapper;
import com.jmt.modules.workOrder.mapper.UserMapper;
import com.jmt.modules.workOrder.util.ALiYunGreenUtil;
import com.jmt.modules.workOrder.util.UserUtil;
import io.swagger.annotations.ApiOperation;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import com.jmt.common.api.vo.Result;
import com.jmt.modules.api.service.AdWorkOrderService;
import com.jmt.modules.api.service.AdvertisingOrderService;
import com.jmt.modules.api.util.*;
import com.jmt.modules.api.util.wx.WechatRefundApiResult;
import com.jmt.modules.api.util.wx.WechatUtil;
import com.jmt.modules.api.model.vo.CmsEqNum;
import com.jmt.modules.api.service.CmsFileService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.text.DateFormat;
import java.time.LocalDate;
import java.util.*;

@RestController
@Slf4j
public class PayController extends ApiBaseAction {

    @Resource
    private AdvertisingOrderService advertisingOrderServiceImpl;

    @Resource
    private AdWorkOrderService adWorkOrderServiceImpl;

    @Resource
    private CmsFileService cmsFileServiceImpl;

    @Resource
    private CommissionClient commissionClient;

    @Resource
    private UaaCilent uaaCilent;

    @Resource
    private PushMsgService pushMsgService;

    @Resource
    private CmsMoneyDetailMapper cmsMoneyDetailMapper;

    @Resource
    private CmsMoneyMapper cmsMoneyMapper;

    @Resource
    private UserMapper userMapper;

    @Resource
    private AdWorkOrderGreenMapper adWorkOrderGreenMapper;


    /**
     * 获取支付的请求参数
     */
    @ApiOperation(value = "获取支付的请求参数")
    @PostMapping("/auth/applets/pay/prepay")
    @Transactional(rollbackFor = Exception.class)
    public Result<Map<Object, Object>> payPrepay(PayVo payVo) {
        Result<Map<Object, Object>> result=new Result<>();
        UserOpenAuthInfoDto openAuthInfoDto=UserUtil.getUserOpenAuthInfo();

        AdvertisingOrder order = advertisingOrderServiceImpl.queryOrderByOrderId(payVo.getOrderId());
        if (null == order) {
            return result.error500("订单不存在");
        }
        if (order.getOrderStatus() != 2 ) {
            switch (order.getOrderStatus()){
                case 1:
                    return result.error500("订单已取消");
                case 7:
                    return result.error500("订单已过期");
                case 8:
                    return result.error500("订单已退款");
                default:
                    return result.error500("订单已支付");
            }
        }

        CmsFile cmsFile = cmsFileServiceImpl.queryByUserIdAndMd5(order.getUserId(),order.getOrderMp4Md5());
        //微信支付
        if ("0".equals(payVo.getPayType())) {
            if(order.getIntegralDeduction() !=0 && BigDecimalUtils.equal(order.getActuallyPaidAmount(),new BigDecimal(0))){
                String content=String.format("用户%s在%s通过微信投广小程序下单%s,下单金额%s,支付积分%s",openAuthInfoDto.getId().toString(),
                        getDateStr(),payVo.getOrderId(),order.getTotalAmount().toString(),order.getIntegralDeduction());
                createWorkOrder(order,content,null, cmsFile);
                result.setCode(0);
                result.setMessage("支付成功");
                return result;
            }
            String nonceStr = CharUtil.getRandomString(32);
            Map<Object, Object> resultObj = new TreeMap<>();
            try {
                Map<Object, Object> parame = new TreeMap<>();
                parame.put("appid", ResourceUtil.getConfigByName("wx.appId"));
                // 商家账号。
                parame.put("mch_id", ResourceUtil.getConfigByName("wx.mchId"));
                String randomStr = CharUtil.getRandomNum(18).toUpperCase();
                // 随机字符串
                parame.put("nonce_str", randomStr);
                // 商户订单编号
                parame.put("out_trade_no", order.getOrderId());
                // 商品描述
                parame.put("body", "投广-支付");
                //订单的点位
                List<AdvertisingOrderDetailed> orderGoods = advertisingOrderServiceImpl.queryOrderDetai(payVo.getOrderId());
                if (null != orderGoods && orderGoods.size() >0 ) {
                    StringBuilder body = new StringBuilder( "投广-");
                    for (AdvertisingOrderDetailed goodsVo : orderGoods) {
                        body.append(goodsVo.getMerchantName()).append("、");
                    }
                    // 商品描述
                    parame.put("body", body.toString());
                }
                //支付金额
                parame.put("total_fee", order.getActuallyPaidAmount().multiply(new BigDecimal(100)).intValue());
                // 回调地址
                parame.put("notify_url", ResourceUtil.getConfigByName("wx.notifyUrl"));
                // 交易类型APP
                parame.put("trade_type", ResourceUtil.getConfigByName("wx.tradeType"));
                parame.put("spbill_create_ip", getClientIp());
                parame.put("openid", openAuthInfoDto.getOpenid());
                String sign = WechatUtil.arraySign(parame, ResourceUtil.getConfigByName("wx.paySignKey"));
                // 数字签证
                parame.put("sign", sign);
                String xml = MapUtils.convertMap2Xml(parame);
                log.info("xml:" + xml);
                Map<String, Object> resultUn = XmlUtil.xmlStrToMap(WechatUtil.requestOnce(ResourceUtil.getConfigByName("wx.uniformorder"), xml));
                // 响应报文
                String returnCode = MapUtils.getString("return_code", resultUn);
                String returnMsg = MapUtils.getString("return_msg", resultUn);
                //
                if ("FAIL".equalsIgnoreCase(returnCode)) {
                    return result.error500("微信下单失败," + returnMsg);
                } else if ("SUCCESS".equalsIgnoreCase(returnCode)) {
                    // 返回数据
                    String resultCode = MapUtils.getString("result_code", resultUn);
                    String errCodeDes = MapUtils.getString("err_code_des", resultUn);

                    if ("FAIL".equalsIgnoreCase(resultCode)) {
                        return result.error500("支付失败," + errCodeDes);
                    } else if ("SUCCESS".equalsIgnoreCase(resultCode)) {
                        String prepayId = MapUtils.getString("prepay_id", resultUn);
                        // 先生成paySign 参考https://pay.weixin.qq.com/wiki/doc/api/wxa/wxa_api.php?chapter=7_7&index=5
                        resultObj.put("appId", ResourceUtil.getConfigByName("wx.appId"));
                        resultObj.put("timeStamp", DateUtils.timeToStr(System.currentTimeMillis() / 1000, DateUtils.DATE_TIME_PATTERN));
                        resultObj.put("nonceStr", nonceStr);
                        resultObj.put("package", "prepay_id=" + prepayId);
                        resultObj.put("signType", "MD5");
                        String paySign = WechatUtil.arraySign(resultObj, ResourceUtil.getConfigByName("wx.paySignKey"));
                        resultObj.put("paySign", paySign);
                        // 业务处理
                        CmsOrderLog orderLog=new CmsOrderLog();
                        orderLog.setOrderId(payVo.getOrderId());
                        orderLog.setUserId(openAuthInfoDto.getId().toString());
                        orderLog.setPayId(prepayId);
                        String content=String.format("用户%s在%s通过微信投广小程序下单%s,下单金额%s,微信交易号%s",openAuthInfoDto.getId().toString(),
                                getDateStr(),payVo.getOrderId(),order.getTotalAmount().toString(),prepayId);
                        orderLog.setOrderDescription(content);
                        orderLog.setCreateTime(new Date());
                        advertisingOrderServiceImpl.saveLog(orderLog);
                        // 付款中
                        result.setCode(200);
                        result.setMessage("微信统一订单下单成功");
                        result.setResult(resultObj);
                        return result;
                    }
                }
            } catch (Exception e) {
                log.error(e.getMessage(),e);
                return result.error500("微信下单失败");
            }
        }



        //积分支付
        if ("1".equals(payVo.getPayType())) {
            if ((order.getActuallyPaidAmount().doubleValue() * 100) > openAuthInfoDto.getIntegral()) {
                return result.error500("积分不足");
            } else {

                double integral = order.getActuallyPaidAmount().doubleValue() * 100;
                UserPointsRecordDTO userPointsRecordDTO = new UserPointsRecordDTO()
                        .setUaaId(openAuthInfoDto.getId())
                        .setIntegralDetail("投广订单积分扣费")
                        .setIntegralNum((int) integral)
                        .setIntegralSource(2)
                        .setIntegralSourceDetail(6)
                        .setIntegralType(1)
                        .setCreateTime(new Date());
                uaaCilent.updateIntegral(userPointsRecordDTO);

                createWorkOrder(order,"积分支付","", cmsFile);

                // 送去机审
                try {
                    AdWorkOrderGreen adWorkOrderGreen = new AdWorkOrderGreen();
                    adWorkOrderGreen.setGreenStatus(false);
                    adWorkOrderGreen.setOrderId(order.getOrderId());

                    String requestId = "";
                    if (cmsFile.getFileType() == 1) {
                        adWorkOrderGreen.setFileType(1);
                        requestId = ALiYunGreenUtil.greenVideo(cmsFile.getFileUrl());

                        adWorkOrderGreen.setRequestId(requestId);

                        adWorkOrderGreenMapper.insert(adWorkOrderGreen);
                    } else if (cmsFile.getFileType() == 2 && cmsFile.getImgType() == 9) {
                        adWorkOrderGreen.setFileType(0);
                        requestId = ALiYunGreenUtil.greenImage(cmsFile.getFileUrl());

                        adWorkOrderGreen.setRequestId(requestId);

                        adWorkOrderGreenMapper.insert(adWorkOrderGreen);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }

                order.setOrderStatus(3);
                advertisingOrderServiceImpl.update(order);

                // 付款中
                result.setCode(200);
                result.setMessage("积分下单成功");
                return result;
            }
        }

        //额度支付
        if ("2".equals(payVo.getPayType())) {
            Double money = pushMsgService.queryMoney(openAuthInfoDto.getId()).get("money");

            if (order.getActuallyPaidAmount().doubleValue() > money) {
                return result.error500("额度不足");
            }
            CmsMoneyDetail cmsMoneyDetail = new CmsMoneyDetail();
            cmsMoneyDetail.setUserId(openAuthInfoDto.getId().intValue());
            cmsMoneyDetail.setOrderNum(IdUtils.getOrderId());
            cmsMoneyDetail.setPayType(2);
            cmsMoneyDetail.setPayMoney(order.getActuallyPaidAmount().doubleValue());

            cmsMoneyDetail.setCreateTime(new Date());
            cmsMoneyDetail.setUpdateTime(new Date());

            cmsMoneyDetailMapper.insert(cmsMoneyDetail);

            CmsMoney cmsMoney = cmsMoneyMapper.queryByUserId(cmsMoneyDetail.getUserId());

            cmsMoney.setMoney(cmsMoney.getMoney() - cmsMoneyDetail.getPayMoney());
            cmsMoney.setUpdateTime(new Date());

            cmsMoneyMapper.update(cmsMoney);

            createWorkOrder(order,"额度支付","", cmsFile);  //发送投广视频

            // 送去机审
            try {
                AdWorkOrderGreen adWorkOrderGreen = new AdWorkOrderGreen();
                adWorkOrderGreen.setGreenStatus(false);
                adWorkOrderGreen.setOrderId(order.getOrderId());

                String requestId = "";
                if (cmsFile.getFileType() == 1) {
                    adWorkOrderGreen.setFileType(1);
                    requestId = ALiYunGreenUtil.greenVideo(cmsFile.getFileUrl());

                    adWorkOrderGreen.setRequestId(requestId);

                    adWorkOrderGreenMapper.insert(adWorkOrderGreen);
                } else if (cmsFile.getFileType() == 2 && cmsFile.getImgType() == 9) {
                    adWorkOrderGreen.setFileType(0);
                    requestId = ALiYunGreenUtil.greenImage(cmsFile.getFileUrl());

                    adWorkOrderGreen.setRequestId(requestId);

                    adWorkOrderGreenMapper.insert(adWorkOrderGreen);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

            order.setOrderStatus(3);
            advertisingOrderServiceImpl.update(order);
            // 付款中
            result.setCode(200);
            result.setMessage("额度下单成功");
            return result;
        }
        if ("3".equals(payVo.getPayType())) {
            BrandUser brandUser = userMapper.getByUserId(Integer.parseInt(order.getUserId()));

            if (brandUser == null) {
                return result.error500("请先申请成为品牌用户");
            }

            // 天数
            long day = DateUtil.between(order.getLaunchStartTime(), order.getLaunchSendTime(), DateUnit.DAY);

            Double filmNum = Double.parseDouble(brandUser.getFilmNum()) - order.getLaunchEquipment() * day;
            Double trafficNum = Double.parseDouble(brandUser.getTrafficNum()) - filmNum * 0.1;

            if (filmNum < 0) {
                return result.error500("片源数不足");
            }

            if (trafficNum < 0) {
                return result.error500("流量不足");
            }

            brandUser.setFilmNum(filmNum.toString());
            brandUser.setTrafficNum(trafficNum.toString());

            userMapper.updateBrandUser(brandUser);

            createWorkOrder(order,"片源支付","", cmsFile);  //发送投广视频

            // 送去机审
            try {
                AdWorkOrderGreen adWorkOrderGreen = new AdWorkOrderGreen();
                adWorkOrderGreen.setGreenStatus(false);
                adWorkOrderGreen.setOrderId(order.getOrderId());

                String requestId = "";
                if (cmsFile.getFileType() == 1) {
                    adWorkOrderGreen.setFileType(1);
                    requestId = ALiYunGreenUtil.greenVideo(cmsFile.getFileUrl());
                    adWorkOrderGreen.setRequestId(requestId);

                    adWorkOrderGreenMapper.insert(adWorkOrderGreen);
                } else if (cmsFile.getFileType() == 2 && cmsFile.getImgType() == 9) {
                    adWorkOrderGreen.setFileType(0);
                    requestId = ALiYunGreenUtil.greenImage(cmsFile.getFileUrl());
                    adWorkOrderGreen.setRequestId(requestId);

                    adWorkOrderGreenMapper.insert(adWorkOrderGreen);
                }

            } catch (Exception e) {
                e.printStackTrace();
            }

            order.setOrderStatus(3);
            advertisingOrderServiceImpl.update(order);
            // 付款中
            result.setCode(200);
            result.setMessage("额度下单成功");

            return result;
        }

        return result.error500("下单失败");
    }

    /**
     * 微信查询订单状态
     */
    @ApiOperation(value = "查询订单状态")
    @PostMapping("/auth/applets/pay/query")
    public Result<Object> orderQuery(String orderId) {
        AdvertisingOrder orderDetail = advertisingOrderServiceImpl.queryOrderByOrderId(orderId);
        if(orderDetail == null){
            return Result.error("订单不存在");
        }
        Map<Object, Object> parame = new TreeMap<Object, Object>();
        parame.put("appid", ResourceUtil.getConfigByName("wx.appId"));
        // 商家账号。
        parame.put("mch_id", ResourceUtil.getConfigByName("wx.mchId"));
        String randomStr = CharUtil.getRandomNum(18).toUpperCase();
        // 随机字符串
        parame.put("nonce_str", randomStr);
        // 商户订单编号
        parame.put("out_trade_no", orderDetail.getOrderId());

        String sign = WechatUtil.arraySign(parame, ResourceUtil.getConfigByName("wx.paySignKey"));
        // 数字签证
        parame.put("sign", sign);

        String xml = MapUtils.convertMap2Xml(parame);
        log.info("xml:" + xml);
        Map<String, Object> resultUn = null;
        try {
            resultUn = XmlUtil.xmlStrToMap(WechatUtil.requestOnce(ResourceUtil.getConfigByName("wx.orderquery"), xml));
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.error("订单查询失败");
        }
        // 响应报文
        String returnCode = MapUtils.getString("return_code", resultUn);
        String returnMsg = MapUtils.getString("return_msg", resultUn);

        if (!"SUCCESS".equals(returnCode)) {
            return Result.ok("订单查询失败:"+returnMsg);
        }

        String tradeState = MapUtils.getString("trade_state", resultUn);
        if ("SUCCESS".equals(tradeState)) {
            // 业务处理
            AdvertisingOrder orderInfo = new AdvertisingOrder();
            orderInfo.setId(orderDetail.getId());
            orderInfo.setOrderId(orderId);
            orderInfo.setOrderStatus(3);
            orderInfo.setUpdateTime(new Date());
            advertisingOrderServiceImpl.update(orderInfo);
            return Result.ok("支付成功");
        } else if ("USERPAYING".equals(tradeState)) {
            // 重新查询 正在支付中
            return Result.ok("支付中");
        } else {
            // 失败
            return Result.ok("查询失败" + MapUtils.getString("err_code_des", resultUn));
        }
    }

    /**
     * 微信订单回调接口
     *
     * @return
     */
    @ApiOperation(value = "微信订单回调接口")
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping(value = "/public/applets/pay/notify", method = RequestMethod.POST, produces = "text/html;charset=UTF-8")
    @ResponseBody
    public void notify(HttpServletRequest request, HttpServletResponse response){
        try {
            request.setCharacterEncoding("UTF-8");
            response.setCharacterEncoding("UTF-8");
            response.setContentType("text/html;charset=UTF-8");
            response.setHeader("Access-Control-Allow-Origin", "*");
            InputStream in = request.getInputStream();
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int len = 0;
            while ((len = in.read(buffer)) != -1) {
                out.write(buffer, 0, len);
            }
            out.close();
            in.close();
            //xml数据
            String reponseXml = new String(out.toByteArray(), StandardCharsets.UTF_8);
            WechatRefundApiResult result = (WechatRefundApiResult) XmlUtil.xmlStrToBean(reponseXml, WechatRefundApiResult.class);
            String resultCode = result.getResult_code();
            if ("FAIL".equalsIgnoreCase(resultCode)) {
                //订单编号
                String outTradeNo = result.getOut_trade_no();
                log.info("订单" + outTradeNo + "支付失败");
                response.getWriter().write(setXml("SUCCESS", "OK"));
            } else if ("SUCCESS".equalsIgnoreCase(resultCode)) {
                //订单编号
                String outTradeNo = result.getOut_trade_no();
                log.info("订单" + outTradeNo + "支付成功");
                // 业务处理 更新订单状态
                AdvertisingOrder orderInfo = advertisingOrderServiceImpl.queryOrderByOrderId(outTradeNo);
                if(orderInfo.getOrderStatus() == 2){
                    String content=String.format("用户%s在%s通过微信支付订单%s,支付金额%s,微信交易号%s",orderInfo.getUserId(),
                            getDateStr(),outTradeNo,result.getCash_fee(),result.getTransaction_id());

                    CmsFile cmsFile = cmsFileServiceImpl.queryByUserIdAndMd5(orderInfo.getUserId(),orderInfo.getOrderMp4Md5());
                    if(createWorkOrder(orderInfo,content,result.getTransaction_id(), cmsFile)){
                        // 送去机审
                        try {
                            AdWorkOrderGreen adWorkOrderGreen = new AdWorkOrderGreen();
                            adWorkOrderGreen.setGreenStatus(false);
                            adWorkOrderGreen.setOrderId(orderInfo.getOrderId());

                            String requestId = "";
                            if (cmsFile.getFileType() == 1) {
                                adWorkOrderGreen.setFileType(1);
                                requestId = ALiYunGreenUtil.greenVideo(cmsFile.getFileUrl());
                                adWorkOrderGreen.setRequestId(requestId);

                                adWorkOrderGreenMapper.insert(adWorkOrderGreen);
                            } else if (cmsFile.getFileType() == 2 && cmsFile.getImgType() == 9) {
                                adWorkOrderGreen.setFileType(0);
                                requestId = ALiYunGreenUtil.greenImage(cmsFile.getFileUrl());
                                adWorkOrderGreen.setRequestId(requestId);

                                adWorkOrderGreenMapper.insert(adWorkOrderGreen);
                            }

                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        response.getWriter().write(setXml("SUCCESS", "OK"));
                    }
                    response.getWriter().write(setXml("SUCCESS", "NO"));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new PayException();
        }
    }

    /**
     * 订单退款请求
     */
    @ApiOperation(value = "订单退款请求")
    @PostMapping("/auth/applets/pay/refund")
    @Transactional(rollbackFor = Exception.class)
    public Result<Object> refund(String orderId) {
        AdvertisingOrder advertisingOrder=advertisingOrderServiceImpl.queryOrderByOrderId(orderId);
        if(advertisingOrder == null){
            return Result.error("订单不存在");
        }
        if(advertisingOrder.getOrderStatus() != 4){
            return Result.error("订单不能退款");
        }
        CmsOrderLog orderLog=new CmsOrderLog();
        orderLog.setOrderId(advertisingOrder.getOrderId());
        orderLog.setUserId(advertisingOrder.getUserId());
        orderLog.setCreateTime(new Date());
        if(advertisingOrder.getIntegralDeduction()!=0 && BigDecimalUtils.equal(advertisingOrder.getActuallyPaidAmount(),new BigDecimal(0))){
            advertisingOrder.setOrderStatus(8);
            advertisingOrderServiceImpl.update(advertisingOrder);
            String content=String.format("用户%s在%s通过微信对订单%s发起退款,退款金额%s,退还积分%s,退款成功!",advertisingOrder.getUserId(),
                    getDateStr(),advertisingOrder.getOrderId(),advertisingOrder.getActuallyPaidAmount(),advertisingOrder.getIntegralDeduction());
            orderLog.setOrderDescription(content);
            advertisingOrderServiceImpl.saveLog(orderLog);
            advertisingOrderServiceImpl.pointsRefund(Long.valueOf(advertisingOrder.getUserId()),advertisingOrder.getIntegralDeduction());
            return Result.ok("退款成功");
        }
        WechatRefundApiResult result = WechatUtil.wxRefund(advertisingOrder.getOrderId(),
                Double.valueOf(advertisingOrder.getActuallyPaidAmount().toString()), Double.valueOf(advertisingOrder.getActuallyPaidAmount().toString()));
        if ("SUCCESS".equals(result.getResult_code())) {
            advertisingOrder.setOrderStatus(8);
            advertisingOrderServiceImpl.update(advertisingOrder);
            String content=String.format("用户%s在%s通过微信对订单%s发起退款,退款金额%s,微信交易号%s,退款成功!",advertisingOrder.getUserId(),
                    getDateStr(),advertisingOrder.getOrderId(),result.getRefund_fee(),result.getRefund_id());
            orderLog.setOrderDescription(content);
            advertisingOrderServiceImpl.saveLog(orderLog);
            if(advertisingOrder.getIntegralDeduction() > 0){
                advertisingOrderServiceImpl.pointsRefund(Long.valueOf(advertisingOrder.getUserId()),advertisingOrder.getIntegralDeduction());
            }
            return Result.ok("退款成功");
        } else {
            String content=String.format("用户%s在%s通过微信对订单%s发起退款,退款金额%s,微信交易号%s,退款失败!",advertisingOrder.getUserId(),
                    getDateStr(),advertisingOrder.getOrderId(),result.getRefund_fee(),result.getRefund_id());
            orderLog.setOrderDescription(content);
            advertisingOrderServiceImpl.saveLog(orderLog);
            return Result.error("退款失败"+result.getErr_code_des());
        }
    }


    //返回微信服务
    public static String setXml(String returnCode, String returnMsg) {
        return "<xml><return_code><![CDATA[" + returnCode + "]]></return_code><return_msg><![CDATA[" + returnMsg + "]]></return_msg></xml>";
    }

    private static String getDateStr(){
        return DateFormat.getDateTimeInstance().format(new Date());
    }


    public boolean  createWorkOrder(AdvertisingOrder orderInfo,String content,String transactionId, CmsFile cmsFile){
        orderInfo.setOrderStatus(3);
        orderInfo.setUpdateTime(new Date());
        int updateResult=advertisingOrderServiceImpl.update(orderInfo);
        if(updateResult == 1){
            //新增工单
            //查询订单详情
            JSONArray param=new JSONArray();
            List<AdvertisingOrderDetailed> detailedList=advertisingOrderServiceImpl.queryOrderDetai(orderInfo.getOrderId());
            for (AdvertisingOrderDetailed detai:detailedList) {
                param.add(detai.getMerchantCode());
            }
            //查询商户设备信息
            log.info("查询商户设备信息");
            List<CmsEqNum> cmsEqNum= commissionClient.ListEqNumByMcodes(param);
            UserOpenAuthInfoDto openAuthInfoDto=UserUtil.getUserOpenAuthInfo(orderInfo.getUserId());
            UserVo userVo=new UserVo();
            userVo.setMobile(openAuthInfoDto.getPhone());
            userVo.setNickname(openAuthInfoDto.getNickName());
            adWorkOrderServiceImpl.saveWorkOrder(orderInfo,userVo,cmsEqNum,cmsFile);
            //新增工单详情
            for (AdvertisingOrderDetailed detais:detailedList) {
                log.info("List<CmsEqNum> ");
                List<CmsEqNum> eqNum=commissionClient.ListEqNumByMcode(detais.getMerchantCode());
                JSONObject params = new JSONObject();
                params.put("mCode",detais.getMerchantCode());
                CmsShopInfo cmsShopInfo=commissionClient.getShopInfo(params);
                adWorkOrderServiceImpl.saveWorkOrderArea(detais,eqNum,cmsShopInfo);
            }
            CmsOrderLog orderLog=new CmsOrderLog();
            orderLog.setOrderId(orderInfo.getOrderId());
            orderLog.setUserId(orderInfo.getUserId());
            orderLog.setPayId(transactionId);
            orderLog.setOrderDescription(content);
            orderLog.setCreateTime(new Date());
            advertisingOrderServiceImpl.saveLog(orderLog);
            return true;
        }
        return false;
    }
}
