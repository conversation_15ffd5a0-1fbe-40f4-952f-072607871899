package com.jmt.modules.api.util.aliUtil;


import com.aliyun.mts20140618.models.SubmitJobsResponseBody;
import com.aliyun.tea.*;

public class Sample {

    /**
     * 使用AK&SK初始化账号Client
     *
     * @param accessKeyId
     * @param accessKeySecret
     * @return Client
     * @throws Exception
     */


    public static com.aliyun.mts20140618.Client createClient(String accessKeyId, String accessKeySecret) throws Exception {
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config()
                // 必填，您的 AccessKey ID
                .setAccessKeyId("LTAI5tL1eBoJBbtTDNE7Bg6o")
                // 必填，您的 AccessKey Secret
                .setAccessKeySecret("******************************");
        // Endpoint 请参考 https://api.aliyun.com/product/Mts
        config.endpoint = "mts.cn-hangzhou.aliyuncs.com";


        return new com.aliyun.mts20140618.Client(config);
    }

    public static String changeVideo(String url, String newUrl) throws Exception {

        System.out.println(url + "==插入的==" + newUrl);

        // 请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_ID 和 ALIBABA_CLOUD_ACCESS_KEY_SECRET。
        // 工程代码泄露可能会导致 AccessKey 泄露，并威胁账号下所有资源的安全性。以下代码示例使用环境变量获取 AccessKey 的方式进行调用，仅供参考，建议使用更安全的 STS 方式，更多鉴权访问方式请参见：https://help.aliyun.com/document_detail/378657.html
        com.aliyun.mts20140618.Client client = createClient("", "");
        com.aliyun.mts20140618.models.SubmitJobsRequest submitJobsRequest = new com.aliyun.mts20140618.models.SubmitJobsRequest()
                .setInput("{\"Bucket\":\"jmt-video\",\"Location\":\"oss-cn-hangzhou\",\"Object\":\"" + url + "\"}")
                .setOutputs("[{\"OutputObject\":\"" + newUrl + "\",\"TemplateId\":\"9c658afce29545a9ae3e5dc4bdc770a2\"}]")
                .setOutputBucket("jmt-video")
                .setPipelineId("2dca2634e7364334865e23f8563cc919");
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();

        try {
            // 复制代码运行请自行打印 API 的返回值
            SubmitJobsResponseBody submitJobsResponseBody = client.submitJobsWithOptions(submitJobsRequest, runtime).getBody();

            System.out.println("1111");
            System.out.println("获取getrequestid:===" + submitJobsResponseBody.getRequestId());
            return submitJobsResponseBody.getRequestId();


        } catch (TeaException error) {
            // 如有需要，请打印 error
            System.out.println("2222"+error);
            com.aliyun.teautil.Common.assertAsString(error.message);
        } catch (Exception _error) {
            System.out.println("333");
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 如有需要，请打印 error
            System.out.println(error);
            com.aliyun.teautil.Common.assertAsString(error.message);
        }
        return null;
    }


    public static void main(String[] args) {
        try {
            changeVideo("file/video/e8cb867c53264e13b13b8c0e1dee0eb0.mp4", "file/video/Ce8cb867c53264e13b13b8c0e1dee0eb0.mp4");
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}