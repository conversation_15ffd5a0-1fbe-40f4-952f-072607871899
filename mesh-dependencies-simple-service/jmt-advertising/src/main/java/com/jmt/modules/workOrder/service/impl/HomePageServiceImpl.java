package com.jmt.modules.workOrder.service.impl;

import com.jmt.modules.workOrder.mapper.BUserMapper;
import com.jmt.modules.workOrder.mapper.BWorkOrderMapper;
import com.jmt.modules.workOrder.mapper.EqAllListMapper;
import com.jmt.modules.workOrder.mapper.FilmLibraryMapper;
import com.jmt.modules.workOrder.model.HomePage;
import com.jmt.modules.workOrder.model.ProvincialInformation;
import com.jmt.modules.workOrder.service.HomePageService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service
public class HomePageServiceImpl implements HomePageService {
    @Resource
    private BUserMapper bUserMapper;

    @Resource
    private EqAllListMapper eqAllListMapper;

    @Resource
    private FilmLibraryMapper filmLibraryMapper;

    @Override
    public HomePage homePage(Map<String, Integer> map) {
        //查询餐厅数量
        Integer numberOfMerchants=bUserMapper.queryUserNums(map);
        //广告视频数量
        Integer numberOfVideos=filmLibraryMapper.queryFilmNum(map);
        //部署机器数量
        Integer deployEquipmentNum=eqAllListMapper.queryEqAllListNums(map);
        //待审片源
        Integer pendingSource=filmLibraryMapper.queryPendingFilmNum(map);
        //查询
        List<ProvincialInformation> proList=eqAllListMapper.queryRegionalEquipmentNum(map);
        //查询待审核设备
        Integer pendingEquipment=bUserMapper.queryPendingEquipment(map);
        HomePage homePage=new HomePage();
        homePage.setNumberOfMerchants(numberOfMerchants);
        homePage.setNumberOfVideos(numberOfVideos);
        homePage.setDeployEquipmentNum(deployEquipmentNum);
        homePage.setPendingSource(pendingSource);
        homePage.setProvincialData(proList);
        homePage.setPendingEquipment(pendingEquipment);
        return homePage;
    }
}
