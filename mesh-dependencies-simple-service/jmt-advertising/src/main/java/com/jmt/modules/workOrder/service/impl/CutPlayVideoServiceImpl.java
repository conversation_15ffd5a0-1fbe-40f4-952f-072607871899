package com.jmt.modules.workOrder.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jmt.modules.client.device.DeviceClient;
import com.jmt.modules.workOrder.entity.CutPlayVideo;
import com.jmt.modules.workOrder.mapper.CutPlayVideoMapper;
import com.jmt.modules.workOrder.service.ICutPlayVideoService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Map;

@Service
public class CutPlayVideoServiceImpl extends ServiceImpl<CutPlayVideoMapper, CutPlayVideo> implements ICutPlayVideoService {

    @Resource
    private DeviceClient deviceClient;

    @Resource
    private CutPlayVideoMapper cutPlayVideoMapper;

    @Override
    public void pushPlayVideo(CutPlayVideo cutPlayVideo) { //推送中间

        //将视频信息推给 消息中间件
        JSONArray toparray = new JSONArray();
        JSONArray jsonArray = new JSONArray();
        JSONObject list = new JSONObject();
        JSONObject message = new JSONObject();
        JSONObject top = new JSONObject();
        String topic = null;
        SimpleDateFormat fmt = new SimpleDateFormat("yyyy-MM-dd");
        /**投放区域-省-编号*/
        String areaProvinceNum = cutPlayVideo.getAreaProvinceNum();
        /**投放区域-市-编号*/
        String areaCityNum = cutPlayVideo.getAreaCityNum();
        /**投放区域-区县-编号*/
        String areaCountyNum = cutPlayVideo.getAreaCountyNum();
        //播放区域有互斥

        if ((!"".equals(areaProvinceNum) && areaProvinceNum != null) && ("".equals(areaCityNum) || areaCityNum == null) && ("".equals(areaCountyNum) || areaCountyNum == null)) {
            topic = areaProvinceNum;/**投放区域-省-编号*/
        }
        if ((!"".equals(areaProvinceNum) && areaProvinceNum != null) && (!"".equals(areaCityNum) && areaCityNum != null) && ("".equals(areaCountyNum) || areaCountyNum == null)) {
            topic = areaCityNum; /**投放区域-市-编号*/
        }
        if ((!"".equals(areaProvinceNum) && areaProvinceNum != null) && (!"".equals(areaCityNum) && areaCityNum != null) && (!"".equals(areaCountyNum) && areaCountyNum != null)) {
            topic = areaCountyNum;/**投放区域-区县-编号*/
        }
//        if(!"".equals(areaProvinceNum) && "".equals(areaCityNum) && "".equals(areaCountyNum)){
//
//        }
//        if(!"".equals(areaProvinceNum) && !"".equals(areaCityNum) && "".equals(areaCountyNum)){
//
//        }
//        if(!"".equals(areaProvinceNum) && !"".equals(areaCityNum) && !"".equals(areaCountyNum)){
//            topic = areaCountyNum;
//        }
        list.put("md5", cutPlayVideo.getFilmMd5());
        list.put("id", cutPlayVideo.getId()+"");
        System.out.println(cutPlayVideo.getId()+"====这是个id");
        list.put("video", cutPlayVideo.getFilmUrl());
        list.put("endTime", fmt.format(cutPlayVideo.getFilmEndTime()));
        jsonArray.add(list);
        message.put("type", "5");//播放列表
        message.put("list", jsonArray);
        top.put("topic", topic);
        top.put("message", message);
        toparray.add(top);
        deviceClient.publishPlayList(toparray);
        cutPlayVideoMapper.insertPlayVideo(cutPlayVideo);
    }



    public void pushPlayVideo6(CutPlayVideo cutPlayVideo) { //推送中间

        //将视频信息推给 消息中间件
        JSONArray toparray = new JSONArray();
        JSONArray jsonArray = new JSONArray();
        JSONObject list = new JSONObject();
        JSONObject message = new JSONObject();
        JSONObject top = new JSONObject();
        String topic = null;
        SimpleDateFormat fmt = new SimpleDateFormat("yyyy-MM-dd");
        /**投放区域-省-编号*/
        String areaProvinceNum = cutPlayVideo.getAreaProvinceNum();
        /**投放区域-市-编号*/
        String areaCityNum = cutPlayVideo.getAreaCityNum();
        /**投放区域-区县-编号*/
        String areaCountyNum = cutPlayVideo.getAreaCountyNum();
        //播放区域有互斥

        if ((!"".equals(areaProvinceNum) && areaProvinceNum != null) && ("".equals(areaCityNum) || areaCityNum == null) && ("".equals(areaCountyNum) || areaCountyNum == null)) {
            topic = areaProvinceNum;/**投放区域-省-编号*/
        }
        if ((!"".equals(areaProvinceNum) && areaProvinceNum != null) && (!"".equals(areaCityNum) && areaCityNum != null) && ("".equals(areaCountyNum) || areaCountyNum == null)) {
            topic = areaCityNum; /**投放区域-市-编号*/
        }
        if ((!"".equals(areaProvinceNum) && areaProvinceNum != null) && (!"".equals(areaCityNum) && areaCityNum != null) && (!"".equals(areaCountyNum) && areaCountyNum != null)) {
            topic = areaCountyNum;/**投放区域-区县-编号*/
        }

        list.put("md5", cutPlayVideo.getFilmMd5());
        list.put("id", cutPlayVideo.getId()+"");
        list.put("warningData", cutPlayVideo.getEntity().toString());
        list.put("endTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(cutPlayVideo.getFilmEndTime()));
        System.out.println("得到到期时间"+new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(cutPlayVideo.getFilmEndTime()));
        jsonArray.add(list);
        message.put("type", "6");//播放列表
        message.put("list", jsonArray);
        top.put("topic", "350100");
        top.put("message", message);
        toparray.add(top);
        deviceClient.publishPlayList(toparray);
        System.out.println(toparray.toString());
    //    cutPlayVideoMapper.insertPlayVideo(cutPlayVideo);
    }

    @Override
    public List<CutPlayVideo> synInVideo(Map<String, Object> map) {
        List<CutPlayVideo> list = cutPlayVideoMapper.synInVideo(map);
        return list;
    }
}
