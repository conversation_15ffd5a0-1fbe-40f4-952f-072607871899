package com.jmt.modules.workOrder.util;

import com.drew.imaging.ImageMetadataReader;
import com.drew.imaging.ImageProcessingException;
import com.drew.metadata.Directory;
import com.drew.metadata.Metadata;
import com.drew.metadata.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import com.jmt.modules.workOrder.enmu.VideoSpecificationsEnum;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * 视频工具类
 */
@Slf4j
public class VideoUtils {

    /**
     *  校验传入文件是否符合规范
     * @param videoEnum
     * @param is
     * @return
     */
    public static Map<String,Object> checkVideo(VideoSpecificationsEnum videoEnum, InputStream is){
        Map<String,Object> returnMap=new HashMap<>(3);
        Map<String,String> map = getFileParame(is);
        if (StringUtils.isNotBlank(map.get("errorMessage"))) {
            returnMap.put("result",false);
            returnMap.put("errorMessage",map.get("errorMessage"));
            return returnMap;
        }
        boolean check=true;
        StringBuilder sb = new StringBuilder("文件格式错误");
        if(StringUtils.isNotBlank(map.get("fileName")) && !map.get("fileName").equalsIgnoreCase(videoEnum.getExtensionName())){
            sb.append("-").append("格式错误,应为").append(videoEnum.getExtensionName()).append("格式");
            check = false;
        }
        if(!check){
            returnMap.put("errorMessage",sb.toString());
        }
        returnMap.put("Duration",map.get("Duration"));
        returnMap.put("result",check);
        return returnMap;
    }





    /**
     *  获取传入文件的 宽高 帧速 扩展名 大小
     * @param is
     * @return
     */
    private static Map<String,String> getFileParame(InputStream is){
        Map<String, String> map = new HashMap<>(7);
        try {
            Metadata metadata = ImageMetadataReader.readMetadata(is);
            for (Directory directory : metadata.getDirectories()) {
                for (Tag tag : directory.getTags()) {
                    if ("Width".equals(tag.getTagName())) {
                        map.put("Width", tag.getDescription().substring(0, tag.getDescription().indexOf(" ")));
                    }
                    if ("Height".equals(tag.getTagName())) {
                        map.put("Height", tag.getDescription().substring(0, tag.getDescription().indexOf(" ")));
                    }
                    if ("Duration".equals(tag.getTagName())) {
                        map.put("Duration", Integer.parseInt(tag.getDescription()) / 1000 + "");
                    }
                    if ("Frame Rate".equals(tag.getTagName())) {
                        map.put("FrameRate", tag.getDescription());
                    }
                    if ("Expected File Name Extension".equals(tag.getTagName())) {
                        map.put("fileName", tag.getDescription());
                    }
                }

            }
        } catch (ImageProcessingException | IOException e) {
            e.printStackTrace();
            map.put("errorMessage", e.getMessage());
            return map;
        }
        return map;
    }
}
