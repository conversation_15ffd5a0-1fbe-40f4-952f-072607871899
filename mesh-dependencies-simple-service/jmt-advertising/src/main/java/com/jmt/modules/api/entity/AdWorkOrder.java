package com.jmt.modules.api.entity;

import java.util.Date;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 广告工单(AdWorkOrder)实体类
 *
 * <AUTHOR>
 * @since 2020-05-26 15:03:26
 */
@Data
@Accessors(chain = true)
public class AdWorkOrder implements Serializable {
    private static final long serialVersionUID = 131799563336353634L;
    /**
    * 主键
    */    
    @JsonProperty("id")
    private Integer id;
    /**
    * 广告工单号
    */    
    @JsonProperty("adJobNum")
    private String adJobNum;
    /**
    * 广告合同编号
    */    
    @JsonProperty("adContractNum")
    private String adContractNum;
    /**
    * 客户名称
    */    
    @JsonProperty("adCustomName")
    private String adCustomName;
    /**
    * 客户联系方式
    */    
    @JsonProperty("adCustomPhone")
    private String adCustomPhone;
    /**
    * 客户联系人
    */    
    @JsonProperty("adCustomLiaison")
    private String adCustomLiaison;
    /**
    * 营业执照号
    */    
    @JsonProperty("adBusinessLicense")
    private String adBusinessLicense;
    /**
    * 合同金额
    */    
    @JsonProperty("adAmountMoney")
    private Double adAmountMoney;
    /**
    * 视频关联ID 片源库id标识
    */    
    @JsonProperty("adFilmId")
    private Integer adFilmId;
    /**
    * 是否投放A类型 1：投放 0：不投放
    */    
    @JsonProperty("adEqTypeA")
    private Integer adEqTypeA;
    /**
    * 是否投放B类型 1：投放 0：不投放
    */    
    @JsonProperty("adEqTypeB")
    private Integer adEqTypeB;
    /**
    * 是否投放C类型 1：投放 0：不投放
    */    
    @JsonProperty("adEqTypeC")
    private Integer adEqTypeC;
    /**
    * 创建人名字
    */    
    @JsonProperty("adCreatorName")
    private String adCreatorName;
    /**
    * 创建人工号
    */    
    @JsonProperty("adCreatorNum")
    private String adCreatorNum;
    /**
    * 0、广告机 1、投广小程序
    */    
    @JsonProperty("source")
    private Integer source;
    /**
    * 创建时间
    */    
    @JsonProperty("adCreatorTime")
    private Date adCreatorTime;
    /**
    * 0:新增未审批 1：主管审批 2：财务审批 3：审片员审批 4:审批通过 5：驳回 
    */    
    @JsonProperty("adStatus")
    private Integer adStatus;




}