package com.jmt.modules.workOrder.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jmt.modules.workOrder.entity.BShopProjectInfo;
import com.jmt.modules.workOrder.entity.BUser;
import com.jmt.modules.workOrder.entity.BUserFile;
import com.jmt.modules.workOrder.mapper.BShopProjectInfoMapper;
import com.jmt.modules.workOrder.mapper.BUserFileMapper;
import com.jmt.modules.workOrder.mapper.BUserMapper;
import com.jmt.modules.workOrder.mapper.EqAllListMapper;
import com.jmt.modules.workOrder.model.BUserListResult;
import com.jmt.modules.workOrder.model.BUserResult;
import com.jmt.modules.workOrder.service.IBuserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class BUserServiceImpl extends ServiceImpl<BUserMapper,BUser> implements IBuserService {

    private static final Logger LOGGER=LoggerFactory.getLogger(BUserServiceImpl.class);
    @Resource
    private BUserMapper bUserMapper;

    @Resource
    private BUserFileMapper bUserFileMapper;

    @Resource
    private BShopProjectInfoMapper bShopProjectInfoMapper;

    @Resource
    private EqAllListMapper eqAllListMapper;

    @Override
    public BUser queryBuserInf(String bCode) {
        return bUserMapper.queryBuserInf(bCode);
    }

    /**
     * 描述: 查询商户列表
     * @method  queryBuserLikeLimit
     * @date: 2020/3/22 0022
     * @author: hanshangrong
     * @param provinceNum
     * @param cityNum
     * @param countyNum
     * @param bCode
     * @param pageNo
     * @param pageSize
     * @return Page<BUserListResult>
     */
    @Override
    public Page<BUserListResult> queryBuserLikeLimit(Integer provinceNum, Integer cityNum, Integer countyNum, String bCode,String startDate,String sendDate, Integer pageNo, Integer pageSize) {
        //封装查询条件
        Map<String,Object> map=new HashMap<>(4);
        map.put("provinceNum",provinceNum);
        map.put("cityNum",cityNum);
        map.put("countyNum",countyNum);
        map.put("bCode",bCode);
        map.put("startDate",startDate);
        map.put("sendDate",sendDate);
        Page<BUserListResult> page=new Page<>(pageNo,pageSize);
        //查询结果集合
        List<BUserListResult> bUserList=bUserMapper.queryLikeLimit(page,map);
        for (BUserListResult userList:bUserList) {
            userList.setEqAllNum(eqAllListMapper.countEqNumByBcode(userList.getBCode()));
        }
        page.setRecords(bUserList);
        return page;

    }

    /**
     * 描述:  查询商户详情
     * @method  queryBUserByBCode
     * @date: 2020/3/22 0022
     * @author: hanshangrong
     * @param bCode
     * @return com.jmt.modules.workOrder.model.BUserResult
     */
    @Override
    public BUserResult queryBUserByBCode(String bCode) {
        //查询商户信息
        BUserResult bUserResult=bUserMapper.queryBUserByBCode(bCode);
        //查询商户视频
        List<BUserFile> bUserMp4=bUserFileMapper.queryBUserFiles(bCode,"1");
        //查询商户图片
        List<BUserFile> bUserFile=bUserFileMapper.queryBUserFiles(bCode,"0");
        //查询收益
        BShopProjectInfo bShopProjectInfo=new BShopProjectInfo();
        bShopProjectInfo.setBCode(bCode);
        List<BShopProjectInfo> bShopProjectInfos=bShopProjectInfoMapper.queryAll(bShopProjectInfo);
        //封装数据
        if(bUserResult != null){
            bUserResult.setBUserMp4(bUserMp4.size()>0?bUserMp4.get(0):null);
            bUserResult.setBUserFileList(bUserFile);
            bUserResult.setBShopProjectInfo(bShopProjectInfos);
        }
        //返回
        return bUserResult;
    }

    @Override
    public int addUser(BUser bUser) {

        return bUserMapper.addUser(bUser);
    }

    @Override
    public int updateInfo(BUser bUser) {
        return bUserMapper.updateInfo(bUser);
    }
}
