package com.jmt.modules.workOrder.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.aliyun.green20220302.Client;
import com.aliyun.green20220302.models.*;
import com.aliyun.tea.*;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.Common;
import com.aliyun.teautil.models.RuntimeOptions;
import com.jmt.modules.workOrder.entity.AdWorkOrderGreen;
import com.jmt.modules.workOrder.mapper.AdWorkOrderGreenMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.parameters.P;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: yyin
 * @description: TODO
 * @date: 2024/4/11 16:38
 */
@Slf4j
public class ALiYunGreenUtil {

    public static Client createClient() throws Exception {
        Config config = new Config()
                .setAccessKeyId("LTAI5tL1eBoJBbtTDNE7Bg6o")
                .setAccessKeySecret("******************************");
        config.endpoint = "green-cip.cn-shanghai.aliyuncs.com";
        return new Client(config);
    }

    public static String greenImage(String url) throws Exception {
        String result = "";

        Client client = createClient();

        JSONObject jsonObject = new JSONObject();
        jsonObject.set("imageUrl", url);

        ImageAsyncModerationRequest imageAsyncModerationRequest = new ImageAsyncModerationRequest()
                .setService("baselineCheck")
                .setServiceParameters(jsonObject.toString());
        RuntimeOptions runtime = new RuntimeOptions();
        try {
            // 复制代码运行请自行打印 API 的返回值
            ImageAsyncModerationResponseBody responseBody = client.imageAsyncModerationWithOptions(imageAsyncModerationRequest, runtime).body;

            if (responseBody.code == 200) {
                result = responseBody.requestId;
            }
        } catch (TeaException error) {
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            Common.assertAsString(error.message);

            result = null;
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            Common.assertAsString(error.message);

            result = null;
        }

        return result;
    }

    public static Integer greenImageResult(String requestId) throws Exception {
        int result = 0;

        Client client = createClient();
        DescribeImageModerationResultRequest describeImageModerationResultRequest = new DescribeImageModerationResultRequest()
                .setReqId(requestId);
        RuntimeOptions runtime = new RuntimeOptions();
        try {
            // 复制代码运行请自行打印 API 的返回值
            DescribeImageModerationResultResponseBody resultResponseBody = client.describeImageModerationResultWithOptions(describeImageModerationResultRequest, runtime).body;

            if (resultResponseBody.code == 200) {
                List<DescribeImageModerationResultResponseBody.DescribeImageModerationResultResponseBodyDataResult> bodyDataResults = resultResponseBody.data.result;
                if (StrUtil.equals("nonLabel", bodyDataResults.get(0).label)) {
                    result = 1;
                } else {
                    log.error("机审拒绝：" + bodyDataResults.get(0).label);
                    result = 2;
                }
            }

        } catch (TeaException error) {
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        }

        return result;
    }

    public static String greenVideo(String url) throws Exception {
        String result = "";

        Client client = createClient();

        JSONObject jsonObject = new JSONObject();
        jsonObject.set("url", url);

        VideoModerationRequest videoModerationRequest = new VideoModerationRequest()
                .setService("videoDetection")
                .setServiceParameters(jsonObject.toString());
        RuntimeOptions runtime = new RuntimeOptions();
        try {
            // 复制代码运行请自行打印 API 的返回值
            result = client.videoModerationWithOptions(videoModerationRequest, runtime).body.data.taskId;
        } catch (TeaException error) {
            result = null;
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            Common.assertAsString(error.message);
        } catch (Exception _error) {
            result = null;
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            Common.assertAsString(error.message);
        }

        return result;
    }

    public static Integer greenVideoResult(String requestId) throws Exception {

        int result = 0;

        Client client = createClient();

        JSONObject jsonObject = new JSONObject();
        jsonObject.set("taskId", requestId);

        VideoModerationResultRequest videoModerationResultRequest = new VideoModerationResultRequest()
                .setServiceParameters(jsonObject.toString())
                .setService("videoDetection");
        RuntimeOptions runtime = new RuntimeOptions();
        try {
            // 复制代码运行请自行打印 API 的返回值
            VideoModerationResultResponseBody responseBody = client.videoModerationResultWithOptions(videoModerationResultRequest, runtime).body;

            if (responseBody.code == 200) {
                VideoModerationResultResponseBody.VideoModerationResultResponseBodyData data = responseBody.data;

                boolean video = false;
                List<VideoModerationResultResponseBody.VideoModerationResultResponseBodyDataFrameResultFrameSummarys> frameSummarys = data.frameResult.getFrameSummarys();
                if (frameSummarys == null || frameSummarys.size() == 0) {
                    video = true;
                } else {
                    for (VideoModerationResultResponseBody.VideoModerationResultResponseBodyDataFrameResultFrameSummarys frameSummary : frameSummarys) {
                        // TODO 看看哪些给过，哪些不给过
                    }
                }

                boolean audio = false;
                List<VideoModerationResultResponseBody.VideoModerationResultResponseBodyDataAudioResultSliceDetails> sliceDetails = data.audioResult.getSliceDetails();
                if (sliceDetails == null || sliceDetails.size() == 0) {
                    audio = true;
                } else {
                    for (VideoModerationResultResponseBody.VideoModerationResultResponseBodyDataAudioResultSliceDetails sliceDetail : sliceDetails) {
                        // TODO 看看哪些给过，哪些不给过
                    }
                }

                result = video && audio ? 1 : 2;
            }

        } catch (TeaException error) {
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        }

        return result;
    }

    public static void main(String[] args) {
        try {
//            String requestId = greenImage("https://jmt-video.oss-cn-hangzhou.aliyuncs.com/file/video/03137de6d2a5422b8e2fe71acf581f29.jpg");
//
//            System.out.println(requestId);

//            boolean result = greenImageResult("EB834A69-2AF7-5AD3-8B32-93F62D1984C5");
//
//            System.out.println(result);

//            String requestId = greenVideo("https://video.resources.jmingt.com/file/video/Ca3e8fc8aa70f41da86561f2d2477e5b5.mp4");
//
//            System.out.println(requestId);

            int result = greenVideoResult("vi_f_g0Kn76gdeKS4RLIyQ1D9Ti-1zJ0Pz");

            System.out.println(result);
        } catch (Exception e) {

        }
    }
}
