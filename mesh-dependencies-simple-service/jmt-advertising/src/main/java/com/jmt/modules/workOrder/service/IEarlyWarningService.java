package com.jmt.modules.workOrder.service;

import com.alibaba.fastjson.JSONObject;
import com.jmt.common.api.vo.Result;
import com.jmt.modules.system.model.MapEntity;
import com.jmt.modules.workOrder.entity.AdWorkOrderArea;
import com.jmt.modules.workOrder.model.AddWorkOrders;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.List;

/**
 * @author: hanshangrong
 * @Date: 2020/4/2 0002 12:55
 * @Description:类注释 广告工单投放地区
 */
public interface IEarlyWarningService {
    /**
     * 描述:  投放区域列表
     *
     * @param
     * @return Result<java.util.List < AdWorkOrderArea>>
     * @method listAdWorkOrderArea
     * @date: 2020/4/2 0002
     * @author: hanshangrong
     */
    Result<JSONObject> upload(MultipartFile file);


    Result<Object> insertWork(AddWorkOrders addWorkOrders);

    /**
     * 描述:  发送短信验证码
     *
     * @param
     * @return com.jmt.common.api.vo.Result<java.lang.Object>
     * @method sendSms
     * @date: 2020/5/31 0031
     * @author: hanshangrong
     */
//    Result<Object> sendSms(String phone);
//
//    /*
//
//     */
//    public void investorUser(MapEntity entity);
}
