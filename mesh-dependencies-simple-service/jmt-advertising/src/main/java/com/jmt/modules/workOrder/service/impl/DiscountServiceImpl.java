package com.jmt.modules.workOrder.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jmt.common.api.vo.Result;
import com.jmt.modules.workOrder.entity.Discount;
import com.jmt.modules.workOrder.mapper.DiscountMapper;
import com.jmt.modules.workOrder.service.DiscountService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @author: yyin
 * @description: TODO
 * @date: 2024/3/25 18:31
 */
@Slf4j
@Service
public class DiscountServiceImpl implements DiscountService {

    @Resource
    private DiscountMapper discountMapper;


    @Override
    public Result<Page<Discount>> getList(Discount discount) {
        Result<com.baomidou.mybatisplus.extension.plugins.pagination.Page<Discount>> result = new Result<>();
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<Discount> page = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(discount.getPageNo(), discount.getPageSize());
        page.setRecords(discountMapper.getList(page, discount));
        result.setCode(200);
        result.setResult(page);
        return result;
    }

    @Override
    public Discount getDetail(Long id) {
        return discountMapper.getDetail(id);
    }

    @Override
    public void addOrUpdate(Discount discount) {
        if (discount.getId() == null) {
            discountMapper.add(discount);
        } else {
            discountMapper.update(discount);
        }
    }

    @Override
    public void deleteDiscount(Long id) {
        discountMapper.deleteDiscount(id);
    }

    @Override
    public Discount getOne() {
        return discountMapper.getOne();
    }
}
