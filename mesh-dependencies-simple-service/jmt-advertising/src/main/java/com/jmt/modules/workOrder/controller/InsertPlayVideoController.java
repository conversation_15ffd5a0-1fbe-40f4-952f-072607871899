package com.jmt.modules.workOrder.controller;

import lombok.extern.slf4j.Slf4j;
import com.jmt.modules.workOrder.service.ICutPlayVideoService;
import com.jmt.modules.workOrder.service.IFilmLibraryService;
import com.jmt.modules.workOrder.service.IInsertPlayVideoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("/insertPlayVideo")
@Slf4j
public class InsertPlayVideoController {

    @Autowired
    private IInsertPlayVideoService iInsertPlayVideoService;
    @Autowired
    private IFilmLibraryService iFilmLibraryService;
    @Autowired
    private ICutPlayVideoService iCutPushVideoService;










}
