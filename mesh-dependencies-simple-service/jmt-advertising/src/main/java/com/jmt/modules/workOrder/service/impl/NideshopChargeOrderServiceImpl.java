package com.jmt.modules.workOrder.service.impl;

import com.jmt.modules.workOrder.mapper.NideshopChargeOrderMapper;
import com.jmt.modules.workOrder.service.NideshopChargeOrderService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;

@Service
public class NideshopChargeOrderServiceImpl implements NideshopChargeOrderService {

    @Resource
    private NideshopChargeOrderMapper nideshopChargeOrderMapper;

    //@DS("platform")
    @Override
    public long checkChargingMinutes() {
        return nideshopChargeOrderMapper.checkChargingMinutes();
    }


    // @DS("platform")
    @Override
    public long checkChargingMinute(List<String> eqList) {
        return nideshopChargeOrderMapper.checkChargingMinute(eqList);
    }
}
