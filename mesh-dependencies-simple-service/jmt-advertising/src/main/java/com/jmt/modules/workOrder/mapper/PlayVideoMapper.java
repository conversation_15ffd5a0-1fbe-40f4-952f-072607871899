package com.jmt.modules.workOrder.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jmt.modules.workOrder.entity.PlayVideo;
import com.jmt.modules.workOrder.model.EqAllListJoggle;
import com.jmt.modules.workOrder.model.EqAllListPlayVideo;

import java.util.List;
import java.util.Map;


public interface PlayVideoMapper extends BaseMapper<PlayVideo> {

    /**
     * 每日定时任务查询需要推送的视频
     * @return
     */
    List<PlayVideo> todayPlayList();

    List<PlayVideo> todayPlayImageList();

    /**
     * 每天查询到期的视频并将状态转成2
     */
    void playVideoExpire();


    //删除视频时候对应MD5的全部视频
    void deleteAllPlayVideo(List<Object> list);


    /**
     * 描述:  新增播放视频
     * @method  insertPlayVideo
     * @date: 2020/4/3 0003
     * @author: hanshangrong
     * @param playVideo
     * @return int
     */
    int insertPlayVideo(PlayVideo playVideo);


    List<PlayVideo> synVideo(Map<String,Object> map);

    void updatePlayStatus (Map<String,Object> map);

    int countProvinceVideoNum(Map<String, Object> mapProvince);
}
