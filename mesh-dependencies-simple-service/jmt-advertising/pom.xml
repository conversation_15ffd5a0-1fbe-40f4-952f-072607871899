<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <artifactId>jmt-advertising</artifactId>
    <parent>
        <artifactId>mesh-dependencies-simple-service</artifactId>
        <groupId>com.ihomeui.mesh</groupId>
        <version>3.5.0</version>
        <relativePath/>
    </parent>

    <properties>
        <!--  (All inherited from parent.) -->
        <!-- Enable the line below to have remote debugging of your application on the port-->
<!--        <spring-boot-maven-plugin.jvmArguments>-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=1${mesh.application.server-port}</spring-boot-maven-plugin.jvmArguments>-->
<!--        <mesh.application.server-port>11200</mesh.application.server-port>-->
<!--        <mesh.application.base-package>com.jmt</mesh.application.base-package>-->

<!--        <mesh.registry.password>admin</mesh.registry.password>-->

<!--        <mesh.registry.server>127.0.0.1:8867</mesh.registry.server>-->

<!--        <mesh.registry.server>************:8867</mesh.registry.server>-->
    </properties>


    <dependencies>

        <dependency>
            <groupId>ws.schild</groupId>
            <artifactId>jave-all-deps</artifactId>
            <version>2.6.0</version>
        </dependency>

        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>mts20140618</artifactId>
            <version>3.3.56</version>
        </dependency>



        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.4.2</version>
            <scope>compile</scope>
        </dependency>




        <dependency>
            <groupId>com.jmt</groupId>
            <artifactId>jmt-common</artifactId>
            <version>2.0.2</version>
        </dependency>

        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15to18</artifactId>
            <version>1.71</version>
        </dependency>

        <!--mysql-->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.16</version>
            <scope>runtime</scope>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
            <version>1.1.17</version>
        </dependency>


        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <version>2.6</version>
        </dependency>


        <!-- AutoPoi Excel工具类-->
        <dependency>
            <groupId>org.jeecgframework</groupId>
            <artifactId>autopoi-web</artifactId>
            <version>1.2</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-codec</groupId>
                    <artifactId>commons-codec</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.poi</groupId>
                    <artifactId>poi</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.github.dadiyang</groupId>
            <artifactId>jave</artifactId>
            <version>1.0.5</version>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>3.9</version>
        </dependency>


        <dependency>
            <groupId>commons-beanutils</groupId>
            <artifactId>commons-beanutils</artifactId>
            <version>1.9.3</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
        </dependency>


        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>green20220302</artifactId>
            <version>2.2.2</version>
        </dependency>
        <dependency>
            <groupId>com.ihomeui.mesh</groupId>
            <artifactId>mesh-service</artifactId>
            <version>3.5.0</version>
            <scope>compile</scope>
        </dependency>

    </dependencies>
</project>
