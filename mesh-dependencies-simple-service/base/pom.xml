<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>mesh-dependencies-simple-service</artifactId>
        <groupId>com.ihomeui.mesh</groupId>
        <version>3.5.0</version>
        <relativePath/>
    </parent>

    <artifactId>base</artifactId>
    <version>3.5.0</version>
    <packaging>war</packaging>

    <properties>
        <!--  (All inherited from parent.) -->
        <!-- Enable the line below to have remote debugging of your application on the port-->
        <spring-boot-maven-plugin.jvmArguments>-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=50083</spring-boot-maven-plugin.jvmArguments>
        <mesh.application.server-port>8083</mesh.application.server-port>
        <mesh.application.base-package>com.ihomeui.base</mesh.application.base-package>

        <mesh.registry.server>127.0.0.1:8867</mesh.registry.server>
        <mesh.registry.password>admin</mesh.registry.password>
    </properties>

    <dependencies>
        <!--  (All inherited from parent.) -->

        <!-- Explicitly add mesh-jpa dependency -->
        <dependency>
            <groupId>com.ihomeui.mesh</groupId>
            <artifactId>mesh-jpa</artifactId>
            <version>${mesh-framework.version}</version>
        </dependency>

        <!-- Explicitly add mesh-service dependency -->
        <dependency>
            <groupId>com.ihomeui.mesh</groupId>
            <artifactId>mesh-service</artifactId>
            <version>${mesh-framework.version}</version>
        </dependency>
    </dependencies>

    <build>
        <!--  (All inherited from parent.) -->
    </build>

    <profiles>
        <!--  (All inherited from parent.) -->
    </profiles>

</project>
