#!/bin/bash
shpath=$(realpath $0)
binDir=$(dirname $shpath)
cd ${binDir}/../

#文件夹名即为服务名
baseDir=$(realpath ./)
service=$(basename $baseDir)

git reset --hard origin/master
git pull

cd ${binDir}
chmod 755 *.sh
./package.sh

jps -l | grep -E "${service}" | cut -d " " -f 1 | xargs kill

ln -s ${baseDir}/target/${service}-*.war ~/deploy/${service}.war -f
ln -s ${baseDir}/bin/start-dev.sh ~/deploy/${service}.sh -f
ln -s ${baseDir}/bin/auto-update-dev.sh ~/deploy/auto/${service}.sh -f

cd ~/deploy

./${service}.sh &
tail -f logs/${service}.log
