<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <property name="now" value="now()" dbms="h2"/>

    <property name="now" value="now()" dbms="mysql"/>
    <property name="autoIncrement" value="true"/>

    <property name="floatType" value="float4" dbms="postgresql, h2"/>
    <property name="floatType" value="float" dbms="mysql, oracle, mssql"/>

    <!--
        Added the entity DomainConfig.
    -->
    <changeSet id="20180822010046-1" author="jhipster">
        <createTable tableName="domain_config">
            <column name="id" type="bigint" autoIncrement="${autoIncrement}">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="office_id" type="bigint">
                <constraints nullable="true" />
            </column>

            <column name="domain_name" type="varchar(100)">
                <constraints nullable="true" unique="true"/>
            </column>

            <column name="login_page" type="varchar(1000)">
                <constraints nullable="true" />
            </column>

            <column name="main_page" type="varchar(1000)">
                <constraints nullable="true" />
            </column>

            <column name="create_time" type="timestamp">
                <constraints nullable="true" />
            </column>

            <column name="update_time" type="timestamp">
                <constraints nullable="true" />
            </column>

            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here, do not remove-->
        </createTable>
        <dropDefaultValue tableName="domain_config" columnName="create_time" columnDataType="datetime"/>
        <dropDefaultValue tableName="domain_config" columnName="update_time" columnDataType="datetime"/>

    </changeSet>
    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here, do not remove-->
</databaseChangeLog>
