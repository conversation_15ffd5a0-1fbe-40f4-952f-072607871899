<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.4.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <property name="now" value="now()" dbms="mysql,h2"/>
    <property name="now" value="current_timestamp" dbms="postgresql"/>
    <property name="now" value="sysdate" dbms="oracle"/>
    <property name="now" value="GETDATE()" dbms="mssql"/>

    <property name="autoIncrement" value="true" dbms="mysql,h2,postgresql,oracle,mssql"/>

    <property name="floatType" value="float4" dbms="postgresql, h2"/>
    <property name="floatType" value="float" dbms="mysql, oracle, mssql"/>

    <!--
        Added the entity Dict.
    -->
    <changeSet id="20170331094800-3" author="jhipster">
        <preConditions onError="MARK_RAN" onFail="MARK_RAN">
            <not><tableExists tableName="sys_dict"/></not>
        </preConditions>
        <createTable tableName="sys_dict">
            <column name="id" type="bigint" autoIncrement="${autoIncrement}">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="kind" type="varchar(64)">
                <constraints nullable="false" />
            </column>

            <column name="seq" type="bigint">
                <constraints nullable="false" />
            </column>

            <column name="value" type="varchar(256)">
                <constraints nullable="false" />
            </column>

            <column name="text" type="varchar(128)">
                <constraints nullable="false" />
            </column>

            <column name="is_system" type="bit">
                <constraints nullable="false" />
            </column>

            <column name="is_read_only" type="bit">
                <constraints nullable="false" />
            </column>

            <column name="remarks" type="varchar(256)">
                <constraints nullable="true" />
            </column>

            <column name="data" type="varchar(1024)">
                <constraints nullable="true" />
            </column>

            <column name="parent_id" type="bigint">
                <constraints nullable="true" />
            </column>

            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here, do not remove-->
        </createTable>
    </changeSet>

    <changeSet id="201806201002222-19" author="caixy">
        <addColumn tableName="sys_dict">
            <column name="service_name" type="varchar(100)"/>
        </addColumn>
    </changeSet>

    <changeSet id="201806201003333-20" author="caixy">
        <addColumn tableName="sys_dict">
            <column name="is_multi" type="bit"/>
        </addColumn>
    </changeSet>

</databaseChangeLog>
