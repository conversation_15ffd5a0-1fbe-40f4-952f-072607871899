package com.ihomeui.base.service;

import com.ihomeui.base.domain.Dict;
import com.ihomeui.mesh.criteria.CriteriaParser;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import java.util.List;
import java.util.Map;

/**
 * Service Interface for managing Dict.
 */
public interface DictService {

    /**
     * Save a dict.
     *
     * @param dict the entity to save
     * @return the persisted entity
     */
    Dict save(Dict dict);

    /**
     *  Get all the dicts.
     *
     *  @param pageable the pagination information
     *  @return the list of entities
     */
    Page<Dict> findAll(Pageable pageable);

    List<Dict> findAll(Specification<Dict> specification);

    List<Dict> findAll(Specification<Dict> specification, Sort sort);

    Page<Dict> findAll(Specification<Dict> specification,Pageable pageable);

    Page<Dict> findAll(String[] kind,Pageable pageable);

    Page<Dict> findAll(CriteriaParser query, Pageable pageable);

    Page<Dict> findAll(CriteriaParser filter, Pageable pageable,String kind,Long parentId,Long ownerId);

    /**
     *  Get the "id" dict.
     *
     *  @param id the id of the entity
     *  @return the entity
     */
    Dict findOne(Long id);

    Dict findOne(String kind, String value);

    /**
     *  Delete the "id" dict.
     *
     *  @param id the id of the entity
     */
    void delete(Long id);
    void delete(String kind, String value);

    Map findAllToMap(String[] kinds);

    Dict findDictKind(String sysDictKind);
}
