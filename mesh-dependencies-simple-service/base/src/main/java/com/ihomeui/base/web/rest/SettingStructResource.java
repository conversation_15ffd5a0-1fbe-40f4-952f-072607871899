package com.ihomeui.base.web.rest;

import com.ihomeui.base.domain.constants.SettingStructConstants;
import com.ihomeui.base.web.rest.dto.SettingStructDTO;
import com.ihomeui.base.web.rest.mapper.SettingStructMapper;
import com.ihomeui.mesh.criteria.CriteriaParser;
import com.ihomeui.mesh.web.utils.HeaderUtil;
import com.ihomeui.mesh.web.utils.PaginationUtil;
import io.micrometer.core.annotation.Timed;
import com.ihomeui.base.domain.Setting;
import com.ihomeui.base.domain.SettingStruct;
import com.ihomeui.base.repository.SettingStructRepository;
import com.ihomeui.base.service.SettingService;
import com.ihomeui.base.service.SettingStructService;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import javax.validation.Valid;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * REST controller for managing SettingStruct.
 */
@RestController
@RequestMapping("/api")
public class SettingStructResource {

    private final Logger log = LoggerFactory.getLogger(SettingStructResource.class);

    @Resource
    private SettingStructService settingStructService;

    @Resource
    private SettingService settingService;

    @Resource
    private SettingStructMapper settingStructMapper;

    @Resource
    private SettingStructRepository settingStructRepository;

    @PostMapping("/setting-structs")
    @Timed
    public ResponseEntity<SettingStructDTO> createSettingStruct(@Valid @RequestBody SettingStructDTO settingStructDTO) throws URISyntaxException {
        log.debug("REST request to save SettingStruct : {}", settingStructDTO);
        if (settingStructDTO.getId() != null) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert("settingStruct", "idexists", "A new settingStruct cannot already have an ID")).body(null);
        }
        SettingStructDTO result = settingStructMapper.settingStructToSettingStructDTO(
            settingStructService.save(settingStructMapper.settingStructDTOToSettingStruct(settingStructDTO))
        );
        return ResponseEntity.created(new URI("/api/setting-structs/" + result.getId()))
            .headers(HeaderUtil.createEntityCreationAlert("settingStruct", result.getId().toString()))
            .body(result);
    }

    @PutMapping("/setting-structs")
    @Timed
    public ResponseEntity<SettingStructDTO> updateSettingStruct(@Valid @RequestBody SettingStructDTO settingStructDTO) throws URISyntaxException {
        log.debug("REST request to update SettingStruct : {}", settingStructDTO);
        if (settingStructDTO.getId() == null) {
            return createSettingStruct(settingStructDTO);
        }
        SettingStructDTO result = settingStructMapper.settingStructToSettingStructDTO(
            settingStructService.save(settingStructMapper.settingStructDTOToSettingStruct(settingStructDTO))
        );
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert("settingStruct", settingStructDTO.getId().toString()))
            .body(result);
    }

    @GetMapping("/setting-structs")
    @Timed
    public ResponseEntity<List<SettingStructDTO>> getAllSettingStructs(
        @RequestParam(required = false) Long parentId,
        @RequestParam(required = false) String query,
        @RequestParam(required = false) String serviceName,
        @RequestParam(required = false) String valueTypes,
        @ApiParam Pageable pageable
    ) throws URISyntaxException {
        log.debug("REST request to get a page of SettingStructs");
        CriteriaParser parser = new CriteriaParser(query, "name,label");
        Page<SettingStructDTO> page = settingStructService.findAll(parser,(root, cq, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            if (parentId != null){
                if(parentId > 0){
                    predicates.add(cb.equal(root.get("parent").get("id"), parentId));
                } else{ // -1 读取根节点
                    predicates.add(cb.isNull(root.get("parent")));
                }
                if(StringUtils.isNotBlank(valueTypes)){
                    predicates.add(root.get("valueType").in(StringUtils.split(valueTypes, ',')));
                }
            }
            if (serviceName != null){
                predicates.add(cb.equal(root.get("serviceName"),serviceName));
            }
            return cb.and(predicates.toArray(new Predicate[0]));
        },pageable).map(settingStruct -> settingStructMapper.settingStructToSettingStructDTO(settingStruct));
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/setting-structs");
        return new ResponseEntity<>(page.getContent(), headers, HttpStatus.OK);
    }

    @GetMapping("/setting-structs/{id}")
    @Timed
    public ResponseEntity<SettingStructDTO> getSettingStruct(@PathVariable Long id) {
        log.debug("REST request to get SettingStruct : {}", id);
        SettingStructDTO settingStructDTO = settingStructMapper.settingStructToSettingStructDTO(
            settingStructService.findOne(id)
        );
        return Optional.ofNullable(settingStructDTO)
            .map(result -> new ResponseEntity<>(
                result,
                HttpStatus.OK))
            .orElse(new ResponseEntity<>(HttpStatus.NOT_FOUND));
    }

    @DeleteMapping("/setting-structs/{id}")
    @Timed
    public ResponseEntity<Void> deleteSettingStruct(@PathVariable Long id) {
        log.debug("REST request to delete SettingStruct : {}", id);
        settingStructService.delete(id);
        return ResponseEntity.ok().headers(HeaderUtil.createEntityDeletionAlert("settingStruct", id.toString())).build();
    }

    @GetMapping("/setting-structs/settings")
    @Timed
    public ResponseEntity<List<SettingStructDTO>> getAllSettingStructsSettingsTree(
        @RequestParam(required = false) Long communityGroupId,
        @RequestParam(required = false) Long communityId,
        @RequestParam(required = false) String serviceName,
        @RequestParam(required = false) Boolean visibility
    ) {
        log.debug("REST request to get a page of SettingStructs");
        List<SettingStructDTO> result = settingStructService.findAll((root,query,cb)->{
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(cb.isNull(root.join("parent", JoinType.LEFT).get("id")));
            if (serviceName != null){
                predicates.add(cb.equal(root.get("serviceName"),serviceName));
            }
            return cb.and(predicates.toArray(new Predicate[0]));
        })
            .stream()
            .map(settingStruct -> settingStructToSettingStructDTO(communityGroupId,communityId,settingStruct,true))
            .filter(
                settingStructDTO ->
                    settingStructDTO != null &&
                        (
                            visibility == null ||
                                visibility.equals(settingStructDTO.getVisibility() == null ? false : settingStructDTO.getVisibility())
                        )
            ).collect(Collectors.toList());

        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @PostMapping("/setting-structs/settings")
    public ResponseEntity<List<SettingStructDTO>> saveSettingsBySettingStructs(
        @RequestParam(required = false) Long communityGroupId,
        @RequestParam(required = false) Long communityId,
        @Valid @RequestBody List<SettingStructDTO> settingStructDTOS
    ) throws URISyntaxException {
        List<Setting> settings = new ArrayList<>();
        for (SettingStructDTO settingStructDTO : settingStructDTOS) {
            settings.addAll(settingStructDTOsToSettings(communityGroupId,communityId,settingStructDTO));
        }
        settingService.saveStructSettings(settings);
        return ResponseEntity.created(new URI("/setting-structs/settings"))
            .headers(HeaderUtil.createEntityCreationAlert("setting",""))
            .body(settingStructDTOS);
    }

    private SettingStructDTO settingStructToSettingStructDTO(Long communityGroupId, Long communityId, SettingStruct settingStruct, Boolean withChild) {
        if ( settingStruct == null ) return null;
        SettingStructDTO settingStructDTO = settingStructMapper.settingStructToSettingStructDTO(settingStruct);
        Setting setting = settingService.findStructSetting(settingStruct);

        settingStructDTO.setEditable(true);
        settingStructDTO.setVisibility(true);

        if (setting!=null) {
            settingStructDTO.setValue(setting.getValue());
        } else {
            settingStructDTO.setValue("");
        }

        if (withChild){
            if (settingStruct.getChildren().size()!=0){
                List<SettingStruct> structs = new ArrayList<>(settingStruct.getChildren());
                structs.sort(Comparator.comparingInt(SettingStruct::getSeq));

                List<SettingStructDTO> children = new ArrayList<>();
                settingStructDTO.setChildren(children);
                for (SettingStruct struct : structs) {
                    children.add(settingStructToSettingStructDTO(communityGroupId,communityId,struct, true));
                }
            }
        }
        return settingStructDTO;
    }

    public List<Setting> settingStructDTOsToSettings(Long communityGroupId, Long communityId, SettingStructDTO settingStructDTO) {
        List<Setting> settings = new ArrayList<>();
        Setting setting = settingStructMapper.settingStructDTOToSetting(settingStructDTO);

        setting.setSettingLevel(SettingStructConstants.Level.SYSTEM);
        setting.setId(null);
        if (setting.getValue()==null)
            setting.setValue("");

        if (!settingStructDTO.getValueType().equals("NONE")){
            settings.add(setting);
        }

        if (settingStructDTO.getChildren() != null && settingStructDTO.getChildren().size() != 0) {
            for (SettingStructDTO settingStruct : settingStructDTO.getChildren()) {
                settings.addAll(settingStructDTOsToSettings(communityGroupId,communityId,settingStruct));
            }
        }
        return settings;
    }

    @PostMapping("/_resources/init_data")
    @Transactional
    public ResponseEntity<Void> initData(){
        List<SettingStruct> roots = settingStructRepository.findAll((root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(cb.isNull(root.get("parent")));
            return cb.and(predicates.toArray(new Predicate[0]));
        });

        for (SettingStruct root : roots) {
            for (SettingStruct child : root.getChildren()) {
                child.setServiceName(root.getServiceName());
                settingStructRepository.save(child);
            }
        }
        return ResponseEntity.ok().build();
    }

}
