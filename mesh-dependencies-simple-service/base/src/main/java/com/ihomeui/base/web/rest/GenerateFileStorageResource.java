package com.ihomeui.base.web.rest;

import com.ihomeui.mesh.utils.HttpRequestParamsUtil;
import com.ihomeui.mesh.utils.StringUtils;
import com.ihomeui.mesh.web.utils.HeaderUtil;
import com.ihomeui.mesh.web.utils.PaginationUtil;
import io.micrometer.core.annotation.Timed;
import com.ihomeui.base.domain.GenerateFileStorage;
import com.ihomeui.base.service.GenerateFileStorageService;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import javax.validation.Valid;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.*;

@RestController
@RequestMapping("/api")
public class GenerateFileStorageResource {

    private final Logger log = LoggerFactory.getLogger(GenerateFileStorageResource.class);

    @Resource
    private GenerateFileStorageService generateFileStorageService;

    private static final Timer timer = new Timer();

    @PostMapping("/generate-file-storages")
    @Timed
    public ResponseEntity<GenerateFileStorage> createGenerateFileStorage(@Valid @RequestBody GenerateFileStorage generateFileStorage) throws URISyntaxException {
        log.debug("REST request to save GenerateFileStorage : {}", generateFileStorage);
        if (generateFileStorage.getId() != null) {
            return ResponseEntity.badRequest()
                .headers(HeaderUtil.createFailureAlert("generateFileStorage", "idexists", "A new generateFileStorage cannot already have an ID")).body(null);
        }
        GenerateFileStorage result = generateFileStorageService.save(generateFileStorage);
        return ResponseEntity.created(new URI("/api/generate-file-storages/" + result.getId()))
            .headers(HeaderUtil.createEntityCreationAlert("generateFileStorage", result.getId().toString()))
            .body(result);
    }

    @PutMapping("/generate-file-storages")
    @Timed
    public ResponseEntity<GenerateFileStorage> updateGenerateFileStorage(@Valid @RequestBody GenerateFileStorage generateFileStorage) throws URISyntaxException {
        log.debug("REST request to update GenerateFileStorage : {}", generateFileStorage);
        if (generateFileStorage.getId() == null) {
            return createGenerateFileStorage(generateFileStorage);
        }
        GenerateFileStorage result = generateFileStorageService.save(generateFileStorage);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert("generateFileStorage", generateFileStorage.getId().toString()))
            .body(result);
    }

    @GetMapping("/generate-file-storages")
    @Timed
    public ResponseEntity<List<GenerateFileStorage>> getAllGenerateFileStorages(
        @ApiParam("生成源")
        @RequestParam String source,
        @PageableDefault(sort = "generateTime", direction = Sort.Direction.DESC)
        @ApiParam Pageable pageable
    ) {
        log.debug("REST request to get a page of GenerateFileStorages");
        Map<String, String> sourceParamMap = HttpRequestParamsUtil.getRequestParams();
        sourceParamMap.remove("page");
        sourceParamMap.remove("size");
        sourceParamMap.remove("sort");
        sourceParamMap.remove("source");
        String sourceParam = HttpRequestParamsUtil.getParamsContent(sourceParamMap);
        Page<GenerateFileStorage> page = generateFileStorageService.findAll((root, cq, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(cb.equal(root.get("status"), "DONE"));
            predicates.add(cb.equal(root.get("source"), source));
            if (StringUtils.isNotBlank(sourceParam)) {
                predicates.add(cb.like(root.get("sourceParam"), "%" + sourceParam + "%"));
            }
            return cb.and(predicates.toArray(new Predicate[0]));
        }, pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/generate-file-storages");
        return new ResponseEntity<>(page.getContent(), headers, HttpStatus.OK);
    }

    @GetMapping("/generate-file-storages/{fileKey}")
    @Timed
    public ResponseEntity<GenerateFileStorage> getGenerateFileStorage(@PathVariable String fileKey) {
        log.debug("REST request to get GenerateFileStorage : {}", fileKey);
        GenerateFileStorage generateFileStorage = generateFileStorageService.findOneByFileKey(fileKey);
        return Optional.ofNullable(generateFileStorage)
            .map(result -> {
                timer.schedule(new TimerTask(){
                    @Override
                    public void run() {
                        if (result.getStatus().equals("ERROR")){
                            generateFileStorageService.delete(result.getId());
                        }
                    }
                },60 * 1000L);
                return new ResponseEntity<>(result, HttpStatus.OK);
            }).orElse(new ResponseEntity<>(HttpStatus.NOT_FOUND));
    }

    @GetMapping("/_generate-file-storages/last-one")
    @Timed
    public ResponseEntity<GenerateFileStorage> getLastOne(
        @ApiParam("生成源")
        @RequestParam String source
    ) {
        Map<String,String> requestParams = HttpRequestParamsUtil.getRequestParams();
        requestParams.remove("source");
        String sourceParam = HttpRequestParamsUtil.getParamsContent(requestParams);

        List<GenerateFileStorage> generateFileStorages = generateFileStorageService.findAll((root, cq, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(cb.equal(root.get("status"), "DONE"));
            predicates.add(cb.equal(root.get("source"), source));
            if (StringUtils.isNotBlank(sourceParam)) {
                predicates.add(cb.like(root.get("sourceParam"), "%" + sourceParam + "%"));
            }
            cq.orderBy(cb.desc(root.get("generateTime")));
            return cb.and(predicates.toArray(new Predicate[0]));
        },PageRequest.of(0,1)).getContent();
        if (generateFileStorages.size() > 0) {
            return new ResponseEntity<>(generateFileStorages.get(0), HttpStatus.OK);
        } else {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
    }
}
