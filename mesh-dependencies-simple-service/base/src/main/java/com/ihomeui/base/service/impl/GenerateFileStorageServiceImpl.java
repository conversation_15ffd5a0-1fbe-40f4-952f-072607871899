package com.ihomeui.base.service.impl;

import com.ihomeui.mesh.service.AliyunOSSService;
import com.ihomeui.base.domain.GenerateFileStorage;
import com.ihomeui.base.domain.GenerateFileStorageSetting;
import com.ihomeui.base.repository.GenerateFileStorageRepository;
import com.ihomeui.base.repository.GenerateFileStorageSettingRepository;
import com.ihomeui.base.service.GenerateFileStorageService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Service Implementation for managing GenerateFileStorage.
 */
@Service
@Transactional
public class GenerateFileStorageServiceImpl implements GenerateFileStorageService{

    private final Logger log = LoggerFactory.getLogger(GenerateFileStorageServiceImpl.class);

    @Resource
    private GenerateFileStorageRepository generateFileStorageRepository;

    @Resource
    private GenerateFileStorageSettingRepository generateFileStorageSettingRepository;

    @Resource
    private AliyunOSSService aliyunOSSService;

    /**
     * Save a generateFileStorage.
     *
     * @param generateFileStorage the entity to save
     * @return the persisted entity
     */
    public GenerateFileStorage save(GenerateFileStorage generateFileStorage) {
        log.debug("Request to save GenerateFileStorage : {}", generateFileStorage);
        if (generateFileStorage.getId() == null) {
            generateFileStorage.setGenerateTime(LocalDateTime.now());
        }
        GenerateFileStorageSetting setting = generateFileStorageSettingRepository.findOneBySource(generateFileStorage.getSource());
        if ( setting == null ) {
            setting = new GenerateFileStorageSetting();
            BeanUtils.copyProperties(generateFileStorage,setting);
            setting.setSaveLastOne(false);
            setting.setExpireTime(60);
            setting = generateFileStorageSettingRepository.save(setting);
        }
        Integer expireSec = setting.getExpireTime();
        if ( expireSec != null && !expireSec.equals(0) ) {
            generateFileStorage.setExpireTime(generateFileStorage.getGenerateTime().plusSeconds(expireSec));
        }
        generateFileStorage.setSetting(setting);
        return generateFileStorageRepository.save(generateFileStorage);
    }

    /**
     *  Get all the generateFileStorages.
     *
     *  @param pageable the pagination information
     *  @return the list of entities
     */
    @Transactional(readOnly = true)
    public Page<GenerateFileStorage> findAll(Pageable pageable) {
        log.debug("Request to get all GenerateFileStorages");
        return generateFileStorageRepository.findAll(pageable);
    }

    @Transactional(readOnly = true)
    public Page<GenerateFileStorage> findAll(Specification<GenerateFileStorage> spec, Pageable pageable) {
        return generateFileStorageRepository.findAll(spec,pageable);
    }

    @Transactional(readOnly = true)
    public List<GenerateFileStorage> findAll(Specification<GenerateFileStorage> spec) {
        return generateFileStorageRepository.findAll(spec);
    }

    @Transactional(readOnly = true)
    public List<GenerateFileStorage> findAll(Specification<GenerateFileStorage> spec, Sort sort) {
        return generateFileStorageRepository.findAll(spec,sort);
    }

    /**
     *  Get one generateFileStorage by id.
     *
     *  @param id the id of the entity
     *  @return the entity
     */
    @Transactional(readOnly = true)
    public GenerateFileStorage findOne(Long id) {
        log.debug("Request to get GenerateFileStorage : {}", id);
        return generateFileStorageRepository.findById(id).orElse(null);
    }

    /**
     *  Delete the  generateFileStorage by id.
     *
     *  @param id the id of the entity
     */
    public void delete(Long id) {
        log.debug("Request to delete GenerateFileStorage : {}", id);
        generateFileStorageRepository.findById(id).ifPresent(generateFileStorage -> {
            generateFileStorageRepository.delete(generateFileStorage);
            aliyunOSSService.remove(generateFileStorage.getFileKey());
        });
    }

    @Transactional(readOnly = true)
    public GenerateFileStorage findOneByFileKey(String fileKey) {
        return generateFileStorageRepository.findOneByFileKey(fileKey);
    }

    @Override
    public void deleteByFileKey(String fileKey) {
        generateFileStorageRepository.delete(
            generateFileStorageRepository.findOneByFileKey(fileKey)
        );
        aliyunOSSService.remove(fileKey);
    }
}
