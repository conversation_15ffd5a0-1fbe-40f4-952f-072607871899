package com.ihomeui.base.service;

import com.ihomeui.base.domain.Setting;
import com.ihomeui.base.domain.SettingStruct;
import com.ihomeui.mesh.criteria.CriteriaParser;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import java.util.List;
import java.util.Map;

public interface SettingService {

    Setting save(Setting setting);

    Setting saveStructSetting(Setting setting);

    List<Setting> saveStructSettings(List<Setting> settings);

    Page<Setting> findAll(CriteriaParser query, Pageable pageable);

    Setting findOne(Long id);

    void delete(Long id);

    Setting findByName(String name);

    Setting findStructSetting(SettingStruct settingStruct);

    Setting findStructSetting(String name);

    List<Setting> findAll();

    Map<String,String> getCachedRuntimeSettings();

    Map<String,String> getCachedRuntimeSettings(String scope);

    Map<String,String> getCachedRuntimeSettings(String scope, String prefix);

    Map<String,String> getCachedRuntimePublicSettings(String scope, String prefix);

    String getCachedRuntimeSettingValue(String settingName);

    String parseRuntimeSettingValue(String settingName);

    String parseRuntimeSettingValue(SettingStruct struct);

    Map<String,String> parseRuntimeSettings(String scope);

    Map<String,String> parseRuntimeSettings(String scope, String prefix);

    Map<String,String> parseRuntimeSettings(List<SettingStruct> structList);
}
