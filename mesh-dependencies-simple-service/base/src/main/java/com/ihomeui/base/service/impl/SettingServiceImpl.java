package com.ihomeui.base.service.impl;

import com.ihomeui.base.domain.Setting;
import com.ihomeui.base.domain.SettingStruct;
import com.ihomeui.base.repository.SettingRepository;
import com.ihomeui.base.repository.SettingStructRepository;
import com.ihomeui.base.service.SettingService;
import com.ihomeui.mesh.criteria.CriteriaParser;
import com.ihomeui.mesh.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.*;

/**
 * Service Implementation for managing Setting.
 */
@Service
@Transactional
public class SettingServiceImpl implements SettingService{

    private final Logger log = LoggerFactory.getLogger(SettingServiceImpl.class);

    @Resource
    private SettingRepository settingRepository;

    @Resource
    private SettingStructRepository settingStructRepository;

    /**
     * Save a setting.
     *
     * @param setting the entity to save
     * @return the persisted entity
     */
    @Override
    public Setting save(Setting setting) {
        log.debug("Request to save Setting : {}", setting);
        return settingRepository.save(setting);
    }

    @Override
    public Setting saveStructSetting(Setting setting) {
        log.debug("Request to save Setting : {}", setting);
        //找到父级
        SettingStruct settingStruct = settingStructRepository.findOneByName(setting.getName());
        if (StringUtils.isBlank(setting.getLabel())){
            setting.setLabel(settingStruct.getLabel());
        }
        //判断是否启用这个等级
        if((settingStruct.getEnableLevel() & setting.getSettingLevel()) != setting.getSettingLevel()){
            return setting;
        }
        //判断这个等级是否可以编辑
        if((settingStruct.getEditableFlag() & setting.getSettingLevel()) != setting.getSettingLevel()){
            return setting;
        }
        //找出数据库里面的对象
        Setting indate = findByName(
            setting.getName()
        );
        //如果值是空，那就是要删除
        if (StringUtils.isEmpty(setting.getValue())){
            if (indate!=null)
                delete(indate.getId());
            return setting;
        }
        //判断是否修改的和父级不一样
        //不一样保存
        if (indate==null){
            indate = settingRepository.save(setting);
        }
        indate.setValue(setting.getValue());
        indate.setSettingLevel(setting.getSettingLevel());
        return settingRepository.save(indate);
    }

    @Override
    public List<Setting> saveStructSettings(List<Setting> settings) {
        List<Setting> result = new ArrayList<>();
        for (Setting setting : settings) {
            result.add(saveStructSetting(setting));
        }
        return result;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Setting> findAll(CriteriaParser filter, Pageable pageable) {
        log.debug("Request to get all Settings");
        return settingRepository.findAll(filter::toPredicate, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Setting> findAll() {
        return settingRepository.findAll();
    }

    @Override
    @Transactional(readOnly = true)
    public Setting findOne(Long id) {
        log.debug("Request to get Setting : {}", id);
        return settingRepository.findById(id).orElse(null);
    }

    @Override
    public void delete(Long id) {
        log.debug("Request to delete Setting : {}", id);
        settingRepository.deleteById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public Setting findByName(String name) {
        return settingRepository.findOneByName(name);
    }

    @Override
    @Transactional(readOnly = true)
    public Setting findStructSetting(String name) {
        return findStructSetting(settingStructRepository.findOneByName(name));
    }

    @Override
    @Transactional(readOnly = true)
    public Setting findStructSetting(SettingStruct settingStruct) {
        return this.findByName(settingStruct.getName());
    }

    @Override
    @Transactional(readOnly = true)
    @Cacheable(value="RuntimeSettings", key="'All'")
    public Map<String,String> getCachedRuntimeSettings(){
        return parseRuntimeSettings(null, null);
    }

    @Override
    @Transactional(readOnly = true)
    @Cacheable(value="RuntimeSettings", key="'byName:'+':'+#scope")
    public Map<String,String> getCachedRuntimeSettings(String scope){
        return parseRuntimeSettings(scope, null);
    }

    @Override
    @Transactional(readOnly = true)
    @Cacheable(value="RuntimeSettings", key="'byName:'+':'+#scope+':'+#prefix")
    public Map<String,String> getCachedRuntimeSettings(String scope, String prefix){
        return parseRuntimeSettings(scope, prefix);
    }

    @Override
    @Transactional(readOnly = true)
    @Cacheable(value="RuntimePublicSettings", key="'byName:'+#scope+':'+#prefix")
    public Map<String,String> getCachedRuntimePublicSettings(String scope, String prefix){
        return parseRuntimeSettings(scope, prefix, true);
    }

    @Override
    @Transactional(readOnly = true)
    @Cacheable(value="RuntimeSettingValue", key="'byName:'+#settingName")
    public String getCachedRuntimeSettingValue(String settingName){
        return parseRuntimeSettingValue(settingName);
    }

    @Override
    @Transactional(readOnly = true)
    public String parseRuntimeSettingValue(String settingName){
        return parseRuntimeSettingValue(
            settingStructRepository.findOneByName(settingName)
        );
    }

    @Override
    @Transactional(readOnly = true)
    public String parseRuntimeSettingValue(SettingStruct struct) {
        return parseRuntimeSettingValue(struct.getName());
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, String> parseRuntimeSettings(String scope) {
        return parseRuntimeSettings(scope,null,false);
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, String> parseRuntimeSettings(String scope, String prefix) {
        return parseRuntimeSettings(scope,prefix,false);
    }

    private Map<String,String> parseRuntimeSettings(String scope, String prefix, boolean isPublic){
        List<SettingStruct> structList = settingStructRepository.findAll((Root<SettingStruct> root, CriteriaQuery<?> cq, CriteriaBuilder cb) ->{
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(cb.equal(root.get("isPublic"), isPublic));
            if(StringUtils.isNotBlank(scope)) {
                predicates.add(cb.like(root.get("frontendAccessibleScope"), "%" + scope + "%"));
            }
            if(StringUtils.isNotBlank(prefix)) {
                predicates.add(cb.like(root.get("name"), prefix + "%"));
            }
            return cb.and(predicates.toArray(new Predicate[0]));
        });
        return parseRuntimeSettings(structList);
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String,String> parseRuntimeSettings(List<SettingStruct> structList) {
        // 取系统的直接配置值
        Map<String,String> sysSettings = new HashMap<>();
        settingRepository.findAll().forEach(
            setting -> sysSettings.put(setting.getName(), setting.getValue())
        );
        // 分析有效的参数配置值
        Map<String, String> result = new TreeMap<>();
        String key;
        String value;
        for(SettingStruct struct : structList) {
            if("NONE".equals(struct.getValueType())) continue;
            key = struct.getName();
            value = sysSettings.get(key);
            if(StringUtils.isNotBlank(value)) {
                result.put(key, value);
            }
        }
        return result;
    }
}
