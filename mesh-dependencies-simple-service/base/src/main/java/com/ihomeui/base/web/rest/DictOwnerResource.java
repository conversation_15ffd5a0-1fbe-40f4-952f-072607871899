package com.ihomeui.base.web.rest;

import com.ihomeui.base.web.rest.dto.DictOwnerDTO;
import com.ihomeui.base.web.rest.mapper.DictOwnerMapper;
import com.ihomeui.mesh.criteria.CriteriaParser;
import com.ihomeui.mesh.web.utils.HeaderUtil;
import com.ihomeui.mesh.web.utils.PaginationUtil;
import io.micrometer.core.annotation.Timed;
import com.ihomeui.base.service.DictOwnerService;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Optional;

/**
 * REST controller for managing DictOwner.
 */
@RestController
@RequestMapping("/api")
public class DictOwnerResource {

    private final Logger log = LoggerFactory.getLogger(DictOwnerResource.class);

    @Resource
    private DictOwnerService dictOwnerService;

    @Resource
    private DictOwnerMapper dictOwnerMapper;

    /**
     * POST  /dict-owners : Create a new dictOwner.
     *
     * @param dictOwnerDTO the dictOwnerDTO to create
     * @return the ResponseEntity with status 201 (Created) and with body the new dictOwnerDTO, or with status 400 (Bad Request) if the dictOwner has already an ID
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PostMapping("/dict-owners")
    @Timed
    public ResponseEntity<DictOwnerDTO> createDictOwner(@Valid @RequestBody DictOwnerDTO dictOwnerDTO) throws URISyntaxException {
        log.debug("REST request to save DictOwner : {}", dictOwnerDTO);
        if (dictOwnerDTO.getId() != null) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert("dictOwner", "idexists", "A new dictOwner cannot already have an ID")).body(null);
        }
        DictOwnerDTO result = dictOwnerMapper.dictOwnerToDictOwnerDTO(
            dictOwnerService.save(dictOwnerMapper.dictOwnerDTOToDictOwner(dictOwnerDTO))
        );
        return ResponseEntity.created(new URI("/api/dict-owners/" + result.getId()))
            .headers(HeaderUtil.createEntityCreationAlert("dictOwner", result.getId().toString()))
            .body(result);
    }

    /**
     * PUT  /dict-owners : Updates an existing dictOwner.
     *
     * @param dictOwnerDTO the dictOwnerDTO to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated dictOwnerDTO,
     * or with status 400 (Bad Request) if the dictOwnerDTO is not valid,
     * or with status 500 (Internal Server Error) if the dictOwnerDTO couldnt be updated
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PutMapping("/dict-owners")
    @Timed
    public ResponseEntity<DictOwnerDTO> updateDictOwner(@Valid @RequestBody DictOwnerDTO dictOwnerDTO) throws URISyntaxException {
        log.debug("REST request to update DictOwner : {}", dictOwnerDTO);
        if (dictOwnerDTO.getId() == null) {
            return createDictOwner(dictOwnerDTO);
        }
        DictOwnerDTO result = dictOwnerMapper.dictOwnerToDictOwnerDTO(
            dictOwnerService.save(dictOwnerMapper.dictOwnerDTOToDictOwner(dictOwnerDTO))
        );

        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert("dictOwner", dictOwnerDTO.getId().toString()))
            .body(result);
    }

    /**
     * GET  /dict-owners : get all the dictOwners.
     *
     * @param pageable the pagination information
     * @return the ResponseEntity with status 200 (OK) and the list of dictOwners in body
     * @throws URISyntaxException if there is an error to generate the pagination HTTP headers
     */
    @GetMapping("/dict-owners")
    @Timed
    public ResponseEntity<List<DictOwnerDTO>> getAllDictOwners(@RequestParam(required = false) String query, @ApiParam Pageable pageable)
        throws URISyntaxException, IOException {
        log.debug("REST request to get a page of DictOwners");
        Page<DictOwnerDTO> page = dictOwnerService.findAll(new CriteriaParser(query), pageable)
            .map(dictOwner -> dictOwnerMapper.dictOwnerToDictOwnerDTO(dictOwner));

        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/dict-owners");
        return new ResponseEntity<>(page.getContent(), headers, HttpStatus.OK);
    }

    /**
     * GET  /dict-owners/:id : get the "id" dictOwner.
     *
     * @param id the id of the dictOwnerDTO to retrieve
     * @return the ResponseEntity with status 200 (OK) and with body the dictOwnerDTO, or with status 404 (Not Found)
     */
    @GetMapping("/dict-owners/{id}")
    @Timed
    public ResponseEntity<DictOwnerDTO> getDictOwner(@PathVariable Long id) {
        log.debug("REST request to get DictOwner : {}", id);
        DictOwnerDTO dictOwnerDTO = dictOwnerMapper.dictOwnerToDictOwnerDTO(
            dictOwnerService.findOne(id)
        );
        return Optional.ofNullable(dictOwnerDTO)
            .map(result -> new ResponseEntity<>(
                result,
                HttpStatus.OK))
            .orElse(new ResponseEntity<>(HttpStatus.NOT_FOUND));
    }

    /**
     * DELETE  /dict-owners/:id : delete the "id" dictOwner.
     *
     * @param id the id of the dictOwnerDTO to delete
     * @return the ResponseEntity with status 200 (OK)
     */
    @DeleteMapping("/dict-owners/{id}")
    @Timed
    public ResponseEntity<Void> deleteDictOwner(@PathVariable Long id) {
        log.debug("REST request to delete DictOwner : {}", id);
        dictOwnerService.delete(id);
        return ResponseEntity.ok().headers(HeaderUtil.createEntityDeletionAlert("dictOwner", id.toString())).build();
    }
}
