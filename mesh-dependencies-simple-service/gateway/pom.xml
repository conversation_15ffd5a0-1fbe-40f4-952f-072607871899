<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>mesh-dependencies-simple-service</artifactId>
        <groupId>com.ihomeui.mesh</groupId>
        <version>3.5.0</version>
        <relativePath/>
    </parent>

    <artifactId>gateway</artifactId>
    <version>3.5.0</version>
    <packaging>war</packaging>

    <properties>
        <!--  (All inherited from parent.) -->
        <spring-boot-maven-plugin.jvmArguments>-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=50083</spring-boot-maven-plugin.jvmArguments>
        <mesh.application.server-port>8082</mesh.application.server-port>
        <mesh.application.base-package>com.ihomeui.gateway</mesh.application.base-package>

        <mesh.registry.password>admin</mesh.registry.password>
<!--        <mesh.registry.server>************:8867</mesh.registry.server>-->
        <mesh.registry.server>127.0.0.1:8867</mesh.registry.server>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.ihomeui.mesh</groupId>
            <artifactId>mesh-gateway</artifactId>
            <version>${mesh-framework.version}</version>
        </dependency>
        <!--  (All inherited from parent.) -->
    </dependencies>

    <build>
        <!--  (All inherited from parent.) -->
    </build>
</project>
