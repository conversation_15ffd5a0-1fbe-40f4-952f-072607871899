'use strict';

/* jshint quotmark: double */
window.SwaggerTranslator.learn({
    "Warning: Deprecated":"Aviso: Depreciado",
    "Implementation Notes":"Notas de Implementação",
    "Response Class":"Classe de resposta",
    "Status":"Status",
    "Parameters":"Parâmetros",
    "Parameter":"Parâmetro",
    "Value":"Valor",
    "Description":"Descrição",
    "Parameter Type":"Tipo de parâmetro",
    "Data Type":"Tipo de dados",
    "Response Messages":"Mensagens de resposta",
    "HTTP Status Code":"Código de status HTTP",
    "Reason":"Razão",
    "Response Model":"Modelo resposta",
    "Request URL":"URL requisição",
    "Response Body":"Corpo da resposta",
    "Response Code":"Código da resposta",
    "Response Headers":"Cabeçalho da resposta",
    "Headers":"Cabeçalhos",
    "Hide Response":"Esconder resposta",
    "Try it out!":"Tente agora!",
    "Show/Hide":"Mostrar/Esconder",
    "List Operations":"Listar operações",
    "Expand Operations":"Expandir operações",
    "Raw":"Cru",
    "can't parse JSON.  Raw result":"Falha ao analisar JSON.  Resulto cru",
    "Model Schema":"Modelo esquema",
    "Model":"Modelo",
    "apply":"Aplicar",
    "Username":"Usuário",
    "Password":"Senha",
    "Terms of service":"Termos do serviço",
    "Created by":"Criado por",
    "See more at":"Veja mais em",
    "Contact the developer":"Contate o desenvolvedor",
    "api version":"Versão api",
    "Response Content Type":"Tipo de conteúdo da resposta",
    "fetching resource":"busca recurso",
    "fetching resource list":"buscando lista de recursos",
    "Explore":"Explorar",
    "Show Swagger Petstore Example Apis":"Show Swagger Petstore Example Apis",
    "Can't read from server.  It may not have the appropriate access-control-origin settings.":"Não é possível ler do servidor. Pode não ter as apropriadas configurações access-control-origin",
    "Please specify the protocol for":"Por favor especifique o protocolo",
    "Can't read swagger JSON from":"Não é possível ler o JSON Swagger de",
    "Finished Loading Resource Information. Rendering Swagger UI":"Carregar informação de recurso finalizada. Renderizando Swagger UI",
    "Unable to read api":"Não foi possível ler api",
    "from path":"do caminho",
    "server returned":"servidor retornou"
});
