<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>登录</title>
    <meta name="viewport" content="initial-scale=1,maximum-scale=1,user-scalable=no,width=device-width,height=device-height">
    <script type="text/javascript" src="lib/jquery-3.1.1.min.js"></script>
    <script type="text/javascript">
        (function(e,t){var i=document,n=window;var l=i.documentElement;var r,a;var d,o=document.createElement("style");var s;function m(){var i=l.getBoundingClientRect().width;if(!t){t=540}if(i>t){i=t}var n=i*100/e;o.innerHTML="html{font-size:"+n+"px;}"}r=i.querySelector('meta[name="viewport"]');a="width=device-width,initial-scale=1,maximum-scale=1.0,user-scalable=no,viewport-fit=cover";if(r){r.setAttribute("content",a)}else{r=i.createElement("meta");r.setAttribute("name","viewport");r.setAttribute("content",a);if(l.firstElementChild){l.firstElementChild.appendChild(r)}else{var c=i.createElement("div");c.appendChild(r);i.write(c.innerHTML);c=null}}m();if(l.firstElementChild){l.firstElementChild.appendChild(o)}else{var c=i.createElement("div");c.appendChild(o);i.write(c.innerHTML);c=null}n.addEventListener("resize",function(){clearTimeout(s);s=setTimeout(m,300)},false);n.addEventListener("pageshow",function(e){if(e.persisted){clearTimeout(s);s=setTimeout(m,300)}},false);if(i.readyState==="complete"){i.body.style.fontSize="16px"}else{i.addEventListener("DOMContentLoaded",function(e){i.body.style.fontSize="16px"},false)}})(750,750);
    </script>
    <style>
        * {
            margin: 0;
            padding: 0;
            -webkit-box-sizing: border-box;
            -moz-box-sizing: border-box;
            box-sizing: border-box;
        }

        .clearfix:after {
            clear: both;
            content: ' ';
            display: block;
            width: 0;
            height: 0;
            visibility: hidden;
        }

        html,
        body {
            width: 100%;
            height: 100%;
        }

        section {
            width: 500px;
            box-sizing: border-box;
            padding: 15px 15px;
            font-size: 16px;
        }

        section div {
            width: 100%;
            height: 40px;
            line-height: 40px;
            /*margin:0 calc(50% - 250px) 40px;*/
            margin-bottom: 15px;
            background-color: #f5f5f5;
            border-radius: 5px;
            border: 1px solid #ccc;
            overflow: hidden;
        }

        section lable {
            float: left;
            width: 150px;
            height: 40px;
            padding: 0 10px;
            text-align: center;
            display: inline-block;
        }

        section input {
            text-indent: 2em;
        }

        section input,
        select {
			width:300px;
            float: right;
            appearance: none;
            -moz-appearance: none;
            -webkit-appearance: none;
            background: none;
            outline: none;
            border: none;
            list-style: none;
            line-height: normal !important;
            resize: none;
            color: #282828;
            border-radius: 0;
            border-shadow: none;
            height: 40px;
            line-height: 40px;
            background-color: #fff;
            font-size: 16px;
        }

        section button {
            appearance: none;
            -moz-appearance: none;
            -webkit-appearance: none;
            background: none;
            outline: none;
            border: none;
            list-style: none;
            line-height: normal !important;
            resize: none;
            border-radius: 0;
            border-shadow: none;
            width: 100%;
            height: 40px;
            background-color: #89bf04;
            color: #fff;
            font-size: 16px;
        }

        section .iconfont {
            position: fixed;
            top: 25px;
            left: 10px;
            width: 70px;
            height: 70px;
            z-index: 9999;
            background-size: 48px 48px;
            border-radius: 3px;
        }

        section body iframe {
            opacity: 0;
            display: none;
        }

    </style>
</head>

<body>
    <section>
        <!--社区文化窗口登录-->
        <div class="clearfix" style="text-align:center">登录</div>
        <div class="clearfix">
            <lable>用户名</lable>
            <input name="win-username" value="">
        </div>
        <div class="clearfix">
            <lable>密码</lable>
            <input type="password" name="win-password" value="">
        </div>
        <div class="clearfix" hidden>
            <lable>登录方式</lable>
            <input name="win-grant_type" value="password">
        </div>
        <button onclick="winlogin()">登录</button>
    </section>
    <script type="text/javascript" >
        function setCookie(name, value) {
            var Days = 360;
            var exp = new Date();
            exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
            document.cookie = name + "=" + escape(value) + ";expires=" + exp.toGMTString() + ";path=/";
        }
    
        function getCookie(name) {
            var arr, reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");
            if (arr = document.cookie.match(reg))
                return unescape(arr[2]);
            else
                return null;
        }
    
        function winlogin() {
            $.ajax({
                headers: {
                    "Authorization": "Basic d2ViX2FwcDo="
                },
                url: '/uaa/oauth/token',
                type: 'post',
                dataType: 'json',
                data: {
                    'username': $('input[name=win-username]').val(),
                    'password': $('input[name=win-password]').val(),
                    'grant_type': $('input[name=win-grant_type]').val()
                }
            }).done(data => {
				if(data.access_token){
                    sessionStorage.setItem('iccp-authenticationtoken', data.access_token);
				}else{
					alert("登录失败！");
				}
                window.location.href = 'index.html'
            }).fail(err => {
				alert("登录失败！");
            }).always()
        }
    
    </script>
</body>

</html>
