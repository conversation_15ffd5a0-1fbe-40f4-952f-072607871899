package com.jmt.common.exception;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@RestControllerAdvice
@Slf4j
class ExceptionAdviceHandler {

    @ExceptionHandler(PayException.class)
    public String payException(PayException e){
        log.error("支付回调异常--->>{}",e.getMessage(),e);
        return "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[NO]]></return_msg></xml>";
    }
}
