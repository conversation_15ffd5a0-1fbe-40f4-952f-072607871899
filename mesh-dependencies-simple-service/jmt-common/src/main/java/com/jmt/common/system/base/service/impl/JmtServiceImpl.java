package com.jmt.common.system.base.service.impl;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jmt.common.system.base.entity.JmtEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
public class JmtServiceImpl<M extends BaseMapper<T>, T extends JmtEntity> extends ServiceImpl<M, T> implements IService<T> {

}
